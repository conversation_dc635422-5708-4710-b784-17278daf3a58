(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2f84080c"],{"1e42":function(e,t,r){"use strict";var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{display:"inline"}},[r("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)},n=[],i=(r("a481"),r("25ca")),o=r("21a6"),l=r.n(o),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var e=this.tableRef.$el,t=i["a"].table_to_book(e,{raw:!0}),r=i["b"](t,{bookType:"xlsx",bookSST:!0,type:"array"});try{var a="export-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";l.a.saveAs(new Blob([r],{type:"application/octet-stream"}),a)}catch(n){this.$message.error("导出失败, err: "+n.message),console.error(n)}return r}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,u=r("2877"),p=Object(u["a"])(c,a,n,!1,null,null,null);t["a"]=p.exports},"2b80":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{label:"集群"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:e.clusterChange},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(e.clusterOptions,(function(e){return r("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"环境"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",filterable:""},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},[r("el-option",{key:"",attrs:{label:"所有",value:""}}),e._v(" "),e._l(e.namespaceOptions,(function(e){return r("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),e._v(" "),r("el-form-item",{attrs:{label:"应用名"}},[r("el-select",{staticStyle:{width:"280px"},attrs:{filterable:"",placeholder:"请选择应用",clearable:""},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app",t)},expression:"searchForm.app"}},e._l(e.apps,(function(e){return r("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")])],1),e._v(" "),r("el-form-item",[r("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:function(t){return e.showEditDialog(null)}}},[e._v("新建")]),e._v(" "),r("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1),e._v(" "),e._m(0),e._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[r("el-table-column",{attrs:{type:"index"}}),e._v(" "),r("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),e._v(" "),r("el-table-column",{attrs:{label:"运行环境",prop:"namespace"}}),e._v(" "),r("el-table-column",{attrs:{label:"所在集群",prop:"cluster"}}),e._v(" "),r("el-table-column",{attrs:{label:"扩容时间段",sortable:"",prop:"startTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s(t.row.startTime)+" 至 "+e._s(t.row.endTime)+" (每天）\n      ")]}}])}),e._v(" "),r("el-table-column",{attrs:{label:"副本数",prop:"replicas",align:"center"}},[r("el-table-column",{attrs:{label:"发布流程",width:"100",align:"center",prop:"pipeReplicas"}}),e._v(" "),r("el-table-column",{attrs:{label:"当前运行",width:"100",align:"center",prop:"currReplicas"}}),e._v(" "),r("el-table-column",{attrs:{label:"扩容",width:"100",align:"center",prop:"replicas"}})],1),e._v(" "),r("el-table-column",{attrs:{label:"修改人",prop:"author"}}),e._v(" "),r("el-table-column",{attrs:{label:"操作",width:"240px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{staticStyle:{"margin-right":"5px"},attrs:{slot:"reference",type:"text",icon:"el-icon-edit",i:""},on:{click:function(r){return e.showEditDialog(t.row)}},slot:"reference"},[e._v("编辑\n        ")]),e._v(" "),r("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(r){return e.deleteCron(t.row)}}},[r("el-button",{staticStyle:{"margin-right":"5px"},attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[e._v("删除\n          ")])],1),e._v(" "),r("el-button",{staticClass:"el-icon-menu",attrs:{type:"text"},on:{click:function(r){return e.podPage(t.row)}}},[e._v("实例管理\n        ")])]}}])})],1),e._v(" "),r("el-dialog",{attrs:{title:"创建/编辑扩缩容配置",visible:e.dialogVisible,width:"700px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[r("el-form",{ref:"dialogEditForm",attrs:{model:e.editForm,"label-width":"120px",rules:e.editFormRules}},[r("el-form-item",{attrs:{label:"应用",prop:"app"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",disabled:e.editFormAppEditDisable},model:{value:e.editForm.app,callback:function(t){e.$set(e.editForm,"app",t)},expression:"editForm.app"}},e._l(e.appOptions,(function(e){return r("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"运行环境"}},[r("el-row",[r("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[r("el-form-item",{attrs:{prop:"cluster"}},[r("el-input",{attrs:{disabled:""},model:{value:e.editForm.cluster,callback:function(t){e.$set(e.editForm,"cluster",t)},expression:"editForm.cluster"}})],1)],1),e._v(" "),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{prop:"namespace"}},[r("el-input",{attrs:{disabled:""},model:{value:e.editForm.namespace,callback:function(t){e.$set(e.editForm,"namespace",t)},expression:"editForm.namespace"}})],1)],1)],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"扩容时间段"}},[r("el-time-select",{staticStyle:{width:"160px"},attrs:{placeholder:"起始时间","picker-options":{start:"00:00",step:"00:30",end:"23:00"}},model:{value:e.editForm.startTime,callback:function(t){e.$set(e.editForm,"startTime",t)},expression:"editForm.startTime"}}),e._v(" "),r("span",{staticStyle:{display:"inline-block",padding:"0 5px"}},[e._v("至")]),e._v(" "),r("el-time-select",{staticStyle:{width:"160px"},attrs:{placeholder:"结束时间","picker-options":{start:"00:00",step:"00:30",end:"23:30",minTime:e.editForm.startTime}},model:{value:e.editForm.endTime,callback:function(t){e.$set(e.editForm,"endTime",t)},expression:"editForm.endTime"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"副本数扩容到",prop:"replicas"}},[r("el-input-number",{model:{value:e.editForm.replicas,callback:function(t){e.$set(e.editForm,"replicas",e._n(t))},expression:"editForm.replicas"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.submitLoading,expression:"submitLoading"}],attrs:{type:"primary"},on:{click:function(t){return e.createCron()}}},[e._v("确 定")])],1)],1)],1)},n=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("table",{staticStyle:{color:"rgb(119, 119, 119)","font-size":"12px",padding:"10px"}},[r("tr",[r("th",{staticStyle:{width:"70px","text-align":"left","vertical-align":"top"}},[e._v("扩容机制:")]),e._v(" "),r("td",{staticStyle:{width:"1000px"}},[e._v("在扩容时间段之内，如果【当前运行副本数】小于【扩容副本数】，则一次性扩容到【扩容副本数】")])]),e._v(" "),r("tr",[r("th",{staticStyle:{width:"70px","text-align":"left","vertical-align":"top"}},[e._v("缩容机制:")]),e._v(" "),r("td",[e._v("在扩容时间段之外，如果【当前运行副本数】大于【发布流程副本数】并且应用当前的CPU使用率小于80%，则缩容一个实例。五分钟后继续检测，满足条件则再缩容一个实例，直到【当前运行副本数】等于【发布流程副本数】")])])])}],i=r("2d63"),o=(r("7f7f"),r("b562")),l=r("b144"),s=r("bb0b"),c=r("1e42"),u={components:{ExportButton:c["a"]},mounted:function(){var e=this;this.$route.query.cluster?this.searchForm.cluster=this.$route.query.cluster:this.searchForm.cluster=this.clusterOptions[0].name,this.$route.query.namespace&&(this.searchForm.namespace=this.$route.query.namespace),this.$route.query.app&&(this.searchForm.app=this.$route.query.app),this.searchForm.app&&this.loadTableData(),Object(o["l"])().then((function(t){e.apps=t.data})).catch((function(t){e.$message.error("加载应用数据出错！ "+t.message)}))},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(i["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var r=e.value;if(this.searchForm.cluster===r.name)return r.namespaces}}catch(a){t.e(a)}finally{t.f()}}return[]}},data:function(){return{searchEnv:"",apps:[],searchForm:{cluster:"",namespace:"",app:""},editForm:{},editFormAppEditDisable:!1,editFormRules:{app:[{required:!0,message:"值不能为空",trigger:"blur"}],cluster:[{required:!0,message:"值不能为空",trigger:"blur"}],namespace:[{required:!0,message:"值不能为空",trigger:"blur"}],timeRange:[{required:!0,message:"值不能为空",trigger:"blur"}],replicas:[{required:!0,message:"值不能为空",trigger:"blur"}]},tableData:[],tableLoading:!1,dialogVisible:!1,submitLoading:!1,appOptions:[]}},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,Object(s["l"])(this.searchForm).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},editFormReset:function(){this.editForm={app:"",cluster:this.searchForm.cluster,namespace:this.searchForm.namespace,replicas:2,startTime:"07:00",endTime:"13:00"}},showEditDialog:function(e){var t=this;if(this.appOptions.length<1&&Object(o["l"])().then((function(e){t.appOptions=e.data})).catch((function(e){t.$message.error(e.message)})),e)this.editForm=Object(l["a"])(e),this.editFormAppEditDisable=!0;else{if(!this.searchForm.cluster||!this.searchForm.namespace)return void this.$message.warning("请选选择集群和环境");this.editFormReset(),this.editFormAppEditDisable=!1}this.dialogVisible=!0},deleteCron:function(e){var t=this;Object(s["f"])(e.id).then((function(e){t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))},clusterChange:function(){this.searchForm.namespace=""},createCron:function(){var e=this;this.$refs["dialogEditForm"].validate((function(t){if(!t)return!1;e.editForm.startTime&&e.editForm.endTime?(e.submitLoading=!0,Object(s["b"])(e.editForm).then((function(t){e.dialogVisible=!1,e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.submitLoading=!1}))):e.$message.error("输入扩容时间段")}))},podPage:function(e){var t=this.$router.resolve({name:"pod-index",query:{cluster:e.cluster,namespace:e.namespace,app:e.app}});window.open(t.href,"_blank")}}},p=u,d=r("2877"),m=Object(d["a"])(p,a,n,!1,null,null,null);t["default"]=m.exports},b144:function(e,t,r){"use strict";function a(e){return JSON.parse(JSON.stringify(e))}function n(e){if(!e||!(e instanceof Date))return"";var t=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds();return t}function i(e){return"isCoreApp"===e?"核心服务":"onlyDeployTag"===e?"只允许部署Tag":"addSysctlKeepalive"===e?"调整内核参数":"skyWalkingAgent"===e?"性能跟踪":"appLogToKafka"===e?"接入ClickHouse日志":"buildUseRuntimeJDK"===e?"镜像JDK版本编译代码":"jvmGcLog"===e?"GC日志":e}r.d(t,"a",(function(){return a})),r.d(t,"c",(function(){return n})),r.d(t,"b",(function(){return i}))},b562:function(e,t,r){"use strict";r.d(t,"p",(function(){return n})),r.d(t,"b",(function(){return i})),r.d(t,"a",(function(){return o})),r.d(t,"l",(function(){return l})),r.d(t,"j",(function(){return s})),r.d(t,"d",(function(){return c})),r.d(t,"i",(function(){return u})),r.d(t,"h",(function(){return p})),r.d(t,"m",(function(){return d})),r.d(t,"o",(function(){return m})),r.d(t,"f",(function(){return f})),r.d(t,"e",(function(){return b})),r.d(t,"c",(function(){return h})),r.d(t,"k",(function(){return g})),r.d(t,"q",(function(){return v})),r.d(t,"n",(function(){return y})),r.d(t,"g",(function(){return _}));var a=r("b775");function n(e){return Object(a["a"])({url:"/v1/app/search",method:"get",params:e})}function i(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function o(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function l(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function s(e){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function c(e){return Object(a["a"])({url:"/v1/app",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/v1/app",method:"put",data:e})}function p(e){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function d(e,t,r){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:r}})}function m(e){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function f(e){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function b(e){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function h(e){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function g(e,t){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function v(e,t){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function y(e,t){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function _(e){return Object(a["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:e}})}},bb0b:function(e,t,r){"use strict";r.d(t,"k",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"e",(function(){return o})),r.d(t,"l",(function(){return l})),r.d(t,"b",(function(){return s})),r.d(t,"f",(function(){return c})),r.d(t,"n",(function(){return u})),r.d(t,"o",(function(){return p})),r.d(t,"m",(function(){return d})),r.d(t,"c",(function(){return m})),r.d(t,"d",(function(){return f})),r.d(t,"g",(function(){return b})),r.d(t,"i",(function(){return h})),r.d(t,"h",(function(){return g})),r.d(t,"j",(function(){return v}));var a=r("b775");function n(e){return Object(a["a"])({url:"/v1/k8s/scale/auto",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/v1/k8s/scale/auto",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/v1/k8s/scale/auto",method:"delete",params:{id:e}})}function l(e){return Object(a["a"])({url:"/v1/k8s/scale/cron",method:"get",params:e})}function s(e){return Object(a["a"])({url:"/v1/k8s/scale/cron",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/v1/k8s/scale/cron",method:"delete",params:{id:e}})}function u(e){return Object(a["a"])({url:"/v1/k8s/scale/log",method:"get",params:e})}function p(e){return Object(a["a"])({url:"/v1/k8s/scale/monitor/log",method:"get",params:e})}function d(e){return Object(a["a"])({url:"/v1/k8s/scale/podautoscaler",method:"get",params:e})}function m(e){return Object(a["a"])({url:"/v1/k8s/scale/podautoscaler",method:"post",data:e})}function f(e){return Object(a["a"])({url:"/v1/k8s/scale/podautoscaler/create-for-core-app",method:"post",params:e})}function b(e){return Object(a["a"])({url:"/v1/k8s/scale/podautoscaler",method:"delete",data:e})}function h(e){return Object(a["a"])({url:"/v1/k8s/scale/podautoscaler/migrate?cluster="+e,method:"post"})}function g(){return Object(a["a"])({url:"/v1/k8s/scale/podautoscaler/all-cluster-autoscaler-v2",method:"get"})}function v(e,t,r){return Object(a["a"])({url:"/v1/k8s/scale/all-by-app",method:"get",params:{cluster:e,namespace:t,app:r}})}}}]);