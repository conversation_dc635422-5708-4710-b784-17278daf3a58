import request from "@/utils/request";


export function searchAutoScale(params) {
  return request({
    url: '/v1/k8s/scale/auto',
    method: 'get',
    params
  })
}


export function createAutoScale(data) {
  return request({
    url: '/v1/k8s/scale/auto',
    method: 'post',
    data
  })
}

export function deleteAutoScale(id) {
  return request({
    url: '/v1/k8s/scale/auto',
    method: 'delete',
    params: {
      id: id
    }
  })
}

export function searchCronScale(params) {
  return request({
    url: '/v1/k8s/scale/cron',
    method: 'get',
    params
  })
}


export function createCronScale(data) {
  return request({
    url: '/v1/k8s/scale/cron',
    method: 'post',
    data
  })
}

export function deleteCronScale(id) {
  return request({
    url: '/v1/k8s/scale/cron',
    method: 'delete',
    params: {
      id: id
    }
  })
}

export function searchScaleLog(params) {
  return request({
    url: '/v1/k8s/scale/log',
    method: 'get',
    params
  })
}

export function searchScaleMonitorLog(params) {
  return request({
    url: '/v1/k8s/scale/monitor/log',
    method: 'get',
    params
  })
}



export function searchPodAutoScaler(params) {
  return request({
    url: '/v1/k8s/scale/podautoscaler',
    method: 'get',
    params
  })
}


export function createPodAutoScaler(data) {
  return request({
    url: '/v1/k8s/scale/podautoscaler',
    method: 'post',
    data
  })
}

export function createPodAutoScalerForCoreApp(params) {
  return request({
    url: '/v1/k8s/scale/podautoscaler/create-for-core-app',
    method: 'post',
    params
  })
}

export function deletePodAutoScaler(data) {
  return request({
    url: '/v1/k8s/scale/podautoscaler',
    method: 'delete',
    data
  })
}

export function migratePodAutoScaler(cluster) {
  return request({
    url: '/v1/k8s/scale/podautoscaler/migrate?cluster=' + cluster,
    method: 'post',
  })
}

export function getClustersAutoScalerV2() {
  return request({
    url: '/v1/k8s/scale/podautoscaler/all-cluster-autoscaler-v2',
    method: 'get',
  })
}


export function searchAppAllScale(cluster, namespace, app) {
  return request({
    url: '/v1/k8s/scale/all-by-app',
    method: 'get',
    params: {
      cluster: cluster,
      namespace: namespace,
      app: app
    }
  })
}
