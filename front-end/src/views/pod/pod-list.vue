<template>
  <div class="pod-list-container" v-loading="pageLoading">
    <pod-app :cluster="this.cluster" :namespace="this.namespace" :app="this.app" :app-high-light="true"></pod-app>
    <el-card class="box-card" style="margin-top: 10px" v-loading="tableLoading">
      <div>
        <div style="float:right; font-size: 12px;color: #909399; height: 30px;position: relative" class="app-btn-group">
          <el-dropdown style="margin-right: 40px;" @command="jacocoDropdownCommand"
                       v-if="namespace === 'jacoco'">
            <span class="el-dropdown-link" style="color: #E6A23C;">
              Jacoco覆盖率测试<i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item icon="el-icon-orange" command="reset">重置覆盖率</el-dropdown-item>
              <el-dropdown-item icon="el-icon-orange" command="reportTask">生成覆盖率报告</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <router-link :to="{name: 'cicd-app-deploy', query: {'app': this.app}}" target="_blank">
            <el-button
              type="text"
              icon="el-icon-position"
              style="margin-top: 0;padding-top: 0;">
              <span style="margin-left: -5px">去发布</span>
            </el-button>
          </router-link>
          <el-button type="text" @click="selectedPodShell">
            <svg-icon icon-class="console"/>
            进入所有容器
          </el-button>
          <el-button type="text" @click="appScaleDialog"
                     style="">
            <i class="el-icon-coin"></i>
            扩缩容
          </el-button>
          <el-button type="text" @click="showAddress"
                     style="">
            <i class="el-icon-link"></i>
            访问地址
          </el-button>

          <el-dropdown style="margin-left: 10px;" @command="appBtnDropdownCommand">
            <span class="el-dropdown-link">
              更多操作<i class="el-icon-arrow-down "></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item icon="el-icon-coin" command="scale" style="display: none">扩缩容</el-dropdown-item>
              <el-dropdown-item icon="el-icon-refresh-right" command="redeploy">重启所有实例</el-dropdown-item>
              <el-dropdown-item icon="el-icon-refresh-left" command="rollback">回滚</el-dropdown-item>
              <el-dropdown-item icon="el-icon-cpu" command="resourceUpdate">临时调整资源</el-dropdown-item>
              <el-dropdown-item icon="el-icon-document-copy" command="java_cms">查看Java应用配置</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="text" @click="loadPod(true)"
                     style="">
            <i class="el-icon-refresh"></i>
            刷新
          </el-button>
        </div>
        <el-table
          :data="tableData"
          element-loading-text="Loading"
          fit
          row-key="name"
          @expand-change="tableExpandChange"
        >
          <div slot="empty" style="text-align: center;color:#e86e20;">
            无任何Pod，应用可能没有部署到当前环境~
          </div>
          <el-table-column type="index" width="40"></el-table-column>
          <el-table-column type="expand" label="" width="30">
            <template v-slot="scope">
              <pod-expand :pod="scope.row"></pod-expand>
            </template>
          </el-table-column>
          <el-table-column label="实例名" prop="name" min-width="140">
            <template slot-scope="scope">
              <span :style="{color:'#214596'}">{{ scope.row.name }}</span>
              <div>
                <el-tag v-if="scope.row.deregister" type="warning" size="mini" effect="plain">已摘除</el-tag>
                <el-tag v-if="scope.row.versionRetain" type="info" size="mini" effect="plain">版本保留</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="110">
            <template slot-scope="slot" slot="header">
              状态
              <el-tooltip effect="light" placement="top">
                <div slot="content">
                  <el-image
                    style="max-width: 800px;"
                    src="/images/pod-status.svg"
                    alt="pod状态">
                  </el-image>
                </div>
                <svg-icon icon-class="question" class-name="question-icon-size"/>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span :class="podStatusClass(scope.row.statusDesc)"></span>
              {{ scope.row.statusDesc }}
            </template>
          </el-table-column>
          <el-table-column label="运行版本" prop="deployTag" show-overflow-tooltip min-width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.deployModules && scope.row.deployModules.length > 1" style="display:inline-block;margin-right: 5px;">
                <el-popover
                  placement="left"
                  width="860"
                  trigger="click">
                  <el-table :data="scope.row.deployModules" style="max-height: 480px;overflow-y: auto;">
                    <el-table-column type="index" width="40"></el-table-column>
                    <el-table-column prop="gitUrl" label="Git地址"></el-table-column>
                    <el-table-column prop="module" label="子模块"></el-table-column>
                    <el-table-column prop="contextPath" label="ContextPath"></el-table-column>
                    <el-table-column prop="tag" label="版本"></el-table-column>
                    <el-table-column prop="commitIdShort" label="提交ID"></el-table-column>
                  </el-table>
                  <el-button slot="reference" type="text" style="margin: 0;padding:0;font-size: 12px;">详情</el-button>
                </el-popover>
              </div>
              {{ scope.row.deployTag }}
            </template>
          </el-table-column>
          <el-table-column label="IP" width="170" prop="podIP">
            <template slot-scope="scope">
              <div style="font-size: 12px;line-height: 14px;">
                <span style="width: 50px;display: inline-block">pod:</span>{{ scope.row.podIP }} <br/>
                <span style="width: 50px;display: inline-block">node:</span>{{ scope.row.hostIP }} <br/>
                <span v-if="scope.row.hostIP" style="width: 50px;display: inline-block">资源池:</span>{{ scope.row.resourcePool ? scope.row.resourcePool : "Common" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120"
                           align="center" prop="restartCount">
            <template slot-scope="slot" slot="header">
              重启
              <el-tooltip effect="light" placement="top">
                <div slot="content">
                  重启次数<br/>重启原因（ExitCode）
                </div>
                <svg-icon icon-class="question" class-name="question-icon-size"/>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.row.restartCount }}</span>
              <div v-if="scope.row.restartCount > 0" style="font-size: 10px;color: #888;">{{ scope.row.restartReason }} ({{ scope.row.restartCode }})</div>
            </template>
          </el-table-column>
          <el-table-column label="资源使用"
                           width="180">
            <template slot-scope="scope">
              <div style="padding-left: 36px;position: relative;">
                <div style="position: absolute;left:0;top:0;font-size: 13px;line-height: 13px;">CPU</div>
                <el-progress v-if="scope.row.cpuUsage> 0" :percentage="scope.row.cpuUsagePercent" :stroke-width="8" :show-text="true"></el-progress>
                <span v-else>--</span>
              </div>
              <div style="padding-left: 36px;position: relative;">
                <div style="position: absolute;left:0;top:0;font-size: 13px;line-height: 13px;">内存</div>
                <el-progress v-if="scope.row.memoryUsage > 0" :percentage="scope.row.memoryUsagePercent" :stroke-width="8"
                             :show-text="true"></el-progress>
                <span v-else>--</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="100">
          </el-table-column>
          <el-table-column
            label="操作"
            width="220">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" content="进入容器" placement="top">
                <el-button
                  circle
                  size="small"
                  style="padding:5px;margin-left: 3px;"
                  @click="podShell(scope.row)">
                  <svg-icon icon-class="console" style="font-size: 1.5em;"/>
                </el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="启动日志" placement="top">
                <el-button
                  circle
                  size="small"
                  style="padding:5px;margin-left: 3px;"
                  @click="podStdoutLog(scope.row.name)">
                  <svg-icon icon-class="log" style="font-size: 1.5em;"/>
                </el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="日志目录" placement="top">
                <el-button
                  circle
                  size="small"
                  style="padding:5px;margin-left: 3px;"
                  @click="podLogFile(scope.row.name)">
                  <svg-icon icon-class="folder" style="font-size: 1.5em;"/>
                </el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="资源监控" placement="top">
                <el-button
                  circle
                  size="small"
                  style="padding:5px;margin-left: 3px;"
                  @click="podMonitor(scope.row.name)">
                  <svg-icon icon-class="monitor" style="font-size: 1.5em;"/>
                </el-button>
              </el-tooltip>
              <el-dropdown style="margin-left: 10px;" @command="manageDropdownCommand">
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down "></i>
            </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="el-icon-delete" :command="'delete##' + scope.row.name">删除</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-scissors" :command="'deregister##' + scope.row.name">摘除
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-paperclip" :command="'versionRetain##' + scope.row.name">版本保留
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-setting" :command="'detail##' + scope.row.name">k8s部署配置</el-dropdown-item>
                  <hr>
                  <el-dropdown-item icon="el-icon-tickets" :command="'stdout##' + scope.row.name">标准输出</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-tickets" :command="'chAppLog##' + scope.row.name">
                    应用日志
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-tickets" :command="'chTomcatAccess##' + scope.row.name">Tomcat访问日志
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-document" :command="'file##' + scope.row.name">日志文件</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-s-operation" :command="'level##' + scope.row.name">级别调整
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <el-dialog
      title="容器启动日志（标准输出)"
      :visible.sync="podStdoutVisible"
      top="5vh"
      :close-on-click-modal="false"
      @close="podStdout.pod = null"
      width="70%"
      center>
      <div style="margin-top: -30px">
        <pod-stdout :cluster="this.podStdout.cluster" :namespace="this.podStdout.namespace" :pod="this.podStdout.pod" :containers="this.podStdout.containers" ></pod-stdout>
      </div>
    </el-dialog>
    <el-dialog :title="'回滚历史列表 (应用：' + this.app + ')'" :visible.sync="rollbackTableVisible">
      <el-table v-loading="rollbackTableLoading"
                :data="rollbackTableData"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                element-loading-text="Loading">
        <el-table-column property="deployTag" label="版本号" sortable>
          <template slot-scope="scope">
            {{ scope.row.deployTag }}
            <span v-if="scope.row.isCurrReplicaSet" style="color: rgb(32 153 14);font-weight: bold;font-size: 12px;"> (当前版本)</span>
          </template>
        </el-table-column>
        <el-table-column property="revision" label="Revision编号" width="110" align="center">
        </el-table-column>
        <el-table-column property="replicas" label="运行副本" width="100" align="center">
          </el-table-column>
        <el-table-column property="createTime" label="发布时间" sortable width="160">
        </el-table-column>
        <el-table-column label="操作" width="140">
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.isCurrReplicaSet"
              icon="el-icon-refresh-left"
              type="text"
              @click="appRollback(scope.row)">回滚至该版本
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      title="应用扩缩容"
      :visible.sync="scaleVisible"
      width="30%">
      <div>
        <p style="line-height: 1.5em;"><b>提示：</b>
          你将进行手动扩缩容操作。应用发版后会恢复到发布流程所配置到实例数。
        </p>
        <el-form ref="form" label-width="120px">
          <el-form-item label="应用" style="margin-bottom: 0">
            <b>{{ this.scaleForm.app }}</b>
          </el-form-item>
          <el-form-item label="环境" style="margin-bottom: 0">
            <b>{{ this.scaleForm.namespace }} ({{ this.scaleForm.cluster }})</b>
          </el-form-item>
          <el-form-item label="副本数调整为" style="margin-bottom: 0">
            <el-input-number v-model="scaleForm.replicas" :min="0" :max="50"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="scaleVisible = false">取 消</el-button>
        <el-button type="warning" @click="appScale">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="重启应用实例"
      :visible.sync="redeployVisible"
      width="30%">
      <div>
        <p style="line-height: 1.5em;"><b>提示：</b>
          启动一批新实例，等新实例达到健康后再删除对应数量的旧实例，如此循环直到所有旧实例被新实例替换掉。确认继续吗？
        </p>
        <el-form ref="form" label-width="120px">
          <el-form-item label="应用名" style="margin-bottom: 0">
            <b>{{ this.redeployForm.app }}</b>
          </el-form-item>
          <el-form-item label="环境" style="margin-bottom: 0">
            <b>{{ this.redeployForm.namespace }} ({{ this.redeployForm.cluster }})</b>
          </el-form-item>
          <el-form-item label="每批启动实例数" style="margin-bottom: 0">
            <el-input v-model.number="redeployForm.maxSurge" style="width: 180px;" type="number" step="25" min="10"
                      max="100">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="redeployVisible = false">取 消</el-button>
        <el-button type="warning" @click="redeploy" v-loading="redeployLoading">继续操作</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="podDetail.name"
      :visible.sync="podDetailVisible"
      top="5vh">
      <vue-json-pretty
        v-loading="podDetailLoading"
        :data="podDetail.json"
        style="max-height: 600px; overflow-y: auto"
      >
      </vue-json-pretty>
    </el-dialog>
    <el-dialog
      title="选择需要覆盖测试的代码"
      :visible.sync="jacocoVisible"
      top="5vh"
      :close-on-click-modal="false"
      width="650px"
      center>
      <el-form :model="jacocoForm" label-width="140">
        <el-form-item label="部署模块">
          <el-checkbox v-model="jacocoForm.module" disabled></el-checkbox>
        </el-form-item>
        <el-form-item label="依赖Jar">
          <el-select v-model="jacocoForm.jars" filterable clearable multiple
                     placeholder="选择需要进行代码覆盖测试的Jar包，不需要的话保持为空即可" style="width: 500px;">
            <el-option
              v-for="item in this.jacocoJarOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="jacocoReportTaskCreate" :loading="this.jacocoReportDisable"
                   :disabled="this.jacocoReportDisable">确 定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="临时调整应用资源"
      :visible.sync="resourceUpdateVisible"
      width="40%">
      <div>
        <p style="line-height: 1.5em;"><b>提示：</b>
          临时调整应用资源后，重发操作将使资源恢复为发布流程中配置的值。资源调整会触发所有 Pod 的批量重启。
        </p>
        <p style="line-height: 1.5em;"><b>应用：</b>
          {{ resourceUpdateForm.cluster }} / {{ resourceUpdateForm.namespace }} / <span style="color: orangered">{{ resourceUpdateForm.app }}</span>
        </p>
        <el-form ref="resourceUpdateForm" label-width="120px">
          <el-form-item label="资源（最大值）">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;max-width: 260px;">
              <el-input v-model.number="resourceUpdateForm.limitCPU" type="number" step="0.1" min="0.2" max="15">
                <template slot="prepend">CPU</template>
              </el-input>
            </el-col>
            <el-col :span="12" style="max-width: 260px;">
              <el-input v-model.number="resourceUpdateForm.limitMemory" type="number" step="128" min="128" max="20480">
                <template slot="prepend">内存</template>
                <template slot="append">MB</template>
              </el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="资源（最小值）">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;max-width: 260px;">
              <el-input v-model.number="resourceUpdateForm.requestCPU" type="number" step="0.1" min="0.1" max="15">
                <template slot="prepend">CPU</template>
              </el-input>
            </el-col>
            <el-col :span="12" style="max-width: 260px;">
              <el-input v-model.number="resourceUpdateForm.requestMemory" type="number" step="128" min="128" max="20480">
                <template slot="prepend">内存</template>
                <template slot="append">MB</template>
              </el-input>
            </el-col>
          </el-row>
        </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resourceUpdateVisible = false">取 消</el-button>
        <el-button type="warning" @click="resourceUpdate" v-loading="resourceLoading">确定</el-button>
      </span>
    </el-dialog>

    <el-drawer
      :withHeader="false"
      :visible.sync="javaCMSVisible"
      direction="rtl"
      size="40%"
    >
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>Java应用配置项列表 <small>[ {{ javaCMS.podObj ? javaCMS.podObj.name : '' }} ]</small></span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="javaCMSVisible=false">关闭
          </el-button>
        </div>
        <div v-loading="javaCMSLoading">
          <div class="javaCMSTotal" v-if="javaCMS.files && javaCMS.files.length > 0">共 {{ javaCMS.files.length }}条</div>
          <span v-for="item in javaCMS.files" :key="item" class="cms-file-item">
            <!-- TODO： 考虑 profileCandidates 的场景 -->
            <el-link href="" target="_blank">
              {{ item }}
          </el-link>
        </span>
        </div>
      </el-card>
    </el-drawer>

    <el-drawer
      :withHeader="false"
      :destroy-on-close="true"
      :visible.sync="addressVisible"
      direction="btt"
      size="400px">
      <app-address :cluster="address.cluster" :namespace="address.namespace" :app="address.app" icon="el-icon-link"></app-address>
    </el-drawer>

    <el-drawer
      title="实例文件"
      class="file-drawer"
      :visible.sync="podFileVisible"
      size="84%"
      direction="ltr">
      <div>
        <pod-file :cluster="podFile.cluster" :namespace="podFile.namespace" :pod="podFile.pod"></pod-file>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {getPodDetail, getPods, podDelete, podDeregister, podVersionRetain, queryCmsFiles} from '@/api/k8s/pod'
import {deploymentDetail, deploymentRedeploy, queryAppReplicaSet, rollback, scaleReplicas, updateDeploymentResource} from "@/api/k8s/app";
import VueJsonPretty from 'vue-json-pretty'
import {cloneObject, timeFormat} from "@/utils/my-util";
import AppAddress from '@/views/components/app-address'
import {jacocoListJars, jacocoReportTask, jacocoReset} from "@/api/jacoco";
import PodExpand from "@/views/pod/pod-expand";
import AppEvent from "@/views/pod/app-event";
import PodStdout from "@/views/components/pod-stdout";
import PodHistory from "@/views/pod/pod-history";
import PodApp from "@/views/pod/pod-app.vue";
import PodFile from "@/views/pod/pod-file.vue";

export default {
  name: "podList",
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      required: true
    },
    app: {
      type: String,
      required: true
    },
  },
  watch: {
    cluster(val) {
      this.loadPod(true);
    },
    namespace(val) {
      this.loadPod(true);
    },
    app(val) {
      this.loadPod(true);
    }
  },
  components: {
    PodFile,
    PodApp,
    PodHistory,
    AppAddress,
    VueJsonPretty,
    PodExpand,
    PodStdout,
    AppEvent
  },
  data() {
    return {
      pageLoading: false,
      tableData: [],
      // tableDefaultExpandRowKeys: [],
      tableLoading: false,
      tableLoadTime: "",
      javaCMSVisible: false,
      javaCMSLoading: false,
      javaCMS: {
        podObj: null,
        files: []
      },
      podStdoutVisible: false,
      podStdout: {
        cluster: "",
        namespace: "",
        pod: "",
        containers: []
      },
      podDetailVisible: false,
      podDetailLoading: false,
      podDetail: {
        name: "",
        json: "",
      },
      addressVisible: false,
      address: {
        cluster: "",
        namespace: "",
        app: "",
      },
      podFileVisible: false,
      podFile: {
        cluster: "",
        namespace: "",
        pod: "",
      },
      redeployVisible: false,
      redeployLoading: false,
      redeployForm: {
        cluster: "",
        namespace: "",
        app: "",
        maxSurge: 25
      },
      scaleVisible: false,
      scaleForm: {
        cluster: "",
        namespace: "",
        app: "",
        currReplicas: -1,
        replicas: 2
      },
      jacocoVisible: false,
      jacocoForm: {
        module: true,
        jars: [],
      },
      jacocoReportDisable: false,
      jacocoJarOptions: [],
      rollbackTableVisible: false,
      rollbackTableLoading: false,
      rollbackTableData: [],
      podEventsLoading: false,
      cache: {},
      resourceUpdateVisible: false,
      resourceLoading: false,
      resourceUpdateForm: {
        cluster: "",
        namespace: "",
        app: "",
        limitCPU: 0,
        limitMemory: 0,
        requestCPU: 0,
        requestMemory: 0,
      },
    }
  },
  computed: {},
  mounted() {
    this.loadPod(true)
  },
  methods: {
    podStatusClass(podStatus) {
      if (podStatus) {
        if (podStatus === "运行中") {
          return "pod-status-green"
        } else if (["调度中", "准备中", "启动中"].includes(podStatus)) {
          return "pod-status-orange"
        }
      }
      return "pod-status-red"
    },
    tableExpandChange(row, expandedRows) {

    },
    queryPod() {
      this.$refs['searchForm'].validate((valid) => {
        if (!valid) {
          return
        }
      })
    },
    loadPod(tableLoading = false) {
      this.loadPod2(this.cluster, this.namespace, this.app, tableLoading)
    },
    loadPod2(cluster, namespace, app, tableLoading = false) {
      this.tableLoading = tableLoading;
      getPods(cluster, namespace, app).then(response => {
        this.tableData = response.data || []
        this.tableLoading = false;
        this.tableLoadTime = timeFormat(new Date());
      }).catch((e) => {
        this.$message.error(e.message);
        this.tableLoading = false;
      });
    },
    reloadPodScheduler() {
      // let vThis = this;
      //如果启动了setInterval， 一定要在组件关闭时stop掉任务
      // setInterval(function () {
      //   if (vThis.cluster && vThis.namespace && vThis.app) {
      //     vThis.loadPod();
      //   }
      // }, 15000)
    },
    onClearSearchFormApp() {
      //解决clear后无法出现选择框的问题，参考：https://github.com/ElemeFE/element/issues/17568
      this.$refs.searchFormApp.$children
        .find(c => c.$el.className.includes('el-input'))
        .blur();
      this.$refs.searchFormApp.focus();
    },
    findPodByName(name) {
      for (let i in this.tableData) {
        if (this.tableData[i].name === name) {
          return this.tableData[i];
        }
      }
    },
    showAddress() {
      this.address = {
        cluster: this.cluster,
        namespace: this.namespace,
        app: this.app,
      }
      this.addressVisible = true;
    },
    podDetailDialog(pod) {
      this.podDetailVisible = true;
      if (this.podDetail.name === pod && this.podDetail.json) {
        return;
      }
      this.podDetailLoading = true;
      getPodDetail(this.cluster, this.namespace, pod).then(response => {
        this.podDetailLoading = false;
        this.podDetail.name = pod;
        this.podDetail.json = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
        this.podDetailLoading = false;
      })
    },
    showJavaCMS() {
      let podObj = this.tableData && this.tableData.length > 0 ? this.tableData[0] : undefined;
      if (!podObj) {
        this.$message({
          message: "页面没有找到任何Pod！",
          type: 'warning'
        });
        return;
      }
      let cluster = this.cluster;
      this.javaCMSLoading = true;
      this.javaCMSVisible = true;
      let cacheKey = `cmsFile##${cluster}##${podObj.namespace}##${podObj.name}`;
      let data = this.cache[cacheKey];
      if (data) {
        this.javaCMS = data;
        this.javaCMSLoading = false;
        return;
      }

      this.javaCMS.podObj = podObj;
      this.javaCMS.files = [];
      let vThis = this;
      queryCmsFiles(cluster, podObj.namespace, podObj.name).then(response => {
        vThis.javaCMSLoading = false;
        vThis.javaCMS.files = response.data;
        vThis.cache[cacheKey] = cloneObject(vThis.javaCMS);
      }).catch((e) => {
        vThis.javaCMSLoading = false;
        this.$message.error(e.message);
      });
    },
    podLogFile(pod) {
      this.podFile.cluster = this.cluster
      this.podFile.namespace = this.namespace
      this.podFile.pod = pod
      this.podFileVisible = true;
      // let href = this.$router.resolve({name: 'pod-file', query: p}).href
      // window.open(href, '_blank');
    },
    podMonitor(pod) {
      let url = `/api/page/redirect?type=grafana&cluster=${this.cluster}&app=${this.app}&namespace=${this.namespace}&pod=${pod}`
      window.open(url)
    },
    podShell(row) {
      this.webShellPage(this.cluster, this.namespace, this.app, row.name)
    },
    selectedPodShell() {
      if (!this.tableData || this.tableData.length < 1) {
        return;
      }
      let pods = []
      for (let d of this.tableData) {
        pods.push(d.name);
      }
      this.webShellPage(this.cluster, this.namespace, this.app, pods.join(","))
    },
    webShellPage(cluster, namespace, app, pods) {
      let url = `/api/page/redirect?type=webShell&cluster=${cluster}&namespace=${namespace}&app=${app}&pods=${pods}&_t` + Date.now();
      window.open(url)
    },
    chTomcatAccessLogPage(cluster, namespace, app, pod) {
      let url = `/api/page/redirect?type=clickhouse&logName=tomcat_access&cluster=${cluster}&namespace=${namespace}&app=${app}&ip=${pod}&_t` + Date.now();
      window.open(url)
    },
    chLogPage(cluster, namespace, app, pod) {
      let url = `/api/page/redirect?type=clickhouse&logName=app_log&cluster=${cluster}&namespace=${namespace}&app=${app}&ip=${pod}&_t` + Date.now();
      window.open(url)
    },
    chAppLog() {
      this.chLogPage(this.cluster, this.namespace, this.app, "")
    },
    pipelinePage() {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": this.app}});
    },
    chTomcatAccessLog() {
      this.chTomcatAccessLogPage(this.cluster, this.namespace, this.app, "")
    },
    appScaleDialog() {
      this.pageLoading = true;
      this.scaleForm.cluster = this.cluster
      this.scaleForm.namespace = this.namespace
      this.scaleForm.app = this.app
      deploymentDetail(this.scaleForm.cluster, this.scaleForm.namespace, this.scaleForm.app).then(response => {
        this.scaleForm.replicas = response.data.replicas;
        this.scaleForm.currReplicas = this.scaleForm.replicas
        this.scaleVisible = true;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.pageLoading = false;
      });
    },
    appScale() {
      let currentReplicas = this.scaleForm.currReplicas
      let targetReplicas = this.scaleForm.replicas
      let msg = ""
      if (currentReplicas > targetReplicas) {
        msg = `当前为缩容操作，副本数变化： ${currentReplicas} → ${targetReplicas}，是否继续？`
      } else if (currentReplicas < targetReplicas) {
        msg = `当前为扩容操作，副本数变化： ${currentReplicas} → ${targetReplicas}，是否继续？`
      } else {
        this.$message.warning("副本数没有变化")
        return;
      }
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageLoading = true;
        let v = this.scaleForm.replicas
        if (v > 100) {
          this.$message.warning("实例数值只能是[0-100]之间")
        }
        this.scaleVisible = false
        scaleReplicas(this.scaleForm).then(response => {
          this.$message.success('操作成功，3秒后将刷新页面');
          setTimeout(function () {
            window.location.reload();
          }, 3000)
        }).catch((e) => {
          this.$message.error('操作失败：' + e.message);
        }).finally(() => {
          this.pageLoading = false;
        })
      }).catch(() => {
      });

    },
    redeployDialog() {
      this.redeployForm.cluster = this.cluster
      this.redeployForm.namespace = this.namespace
      this.redeployForm.app = this.app
      this.redeployForm.maxSurge = 25
      this.redeployVisible = true;
    },
    redeploy() {
      this.redeployLoading = true;
      let v = this.redeployForm.maxSurge
      if (v < 1 || v > 100) {
        this.$message.warning("每批重启实例百分数值只能是[1-100]之间")
      }
      this.redeployVisible = false
      deploymentRedeploy(this.redeployForm).then(response => {
        this.$message.success('操作成功，请等待一段时间再刷新页面数据');
        this.loadPod();
      }).catch((e) => {
        this.$message.error('操作失败：' + e.message);
      }).finally(() => {
        this.redeployLoading = false;
      })
    },
    rollbackDialog() {
      this.rollbackTableVisible = true;
      this.rollbackTableLoading = true;
      queryAppReplicaSet(this.cluster, this.namespace, this.app).then(response => {
        let replicaSets = response.data;
        deploymentDetail(this.cluster, this.namespace, this.app).then(response => {
          for (let rs of replicaSets) {
            if (rs.revision === response.data.revision) {
              rs.isCurrReplicaSet = true;
            } else {
              rs.isCurrReplicaSet = false;
            }
          }
          this.rollbackTableData = replicaSets;
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.rollbackTableLoading = false;
        });
      }).catch((e) => {
        this.rollbackTableLoading = false;
        this.$message.error(e.message);
      });
    },
    appRollback(row) {
      this.$confirm('确定要回滚到 ' + row.deployTag + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.rollbackTableLoading = true;
        let cluster = this.cluster;
        let namespace = this.namespace;
        let app = this.app;
        rollback(cluster, namespace, app, row.revision, row.deployTag).then(response => {
          this.$message.success('操作成功！');
          this.loadPod();
          this.rollbackTableVisible = false;
        }).catch((e) => {
          this.$message.error('操作失败：' + e.message);
        }).finally(() => {
          this.rollbackTableLoading = false;
        })
      }).catch((e) => {
        console.error(e);
      });
    },
    appLogDropdownCommand(command) {
      console.log("app log dropdown command: " + command);
      if (command === "chAppLog") {
        this.chAppLog();
      } else if (command === "chTomcatAccess") {
        this.chTomcatAccessLog();
      } else {
        console.error("unknown command: " + command);
      }
    },
    jacocoDropdownCommand(command) {
      console.log("jacoco dropdown command: " + command);
      if (!this.tableData || this.tableData.length !== 1) {
        this.$message.error("Jacoco覆盖率测试只支持单实例应用")
        return;
      }
      let pod = this.tableData[0].name;
      let cluster = this.cluster;
      let namespace = this.namespace;
      if (command === "reset") {
        this.$confirm('是否确认要重置覆盖率测试?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.pageLoading = true;
          jacocoReset(cluster, namespace, pod).then(response => {
            this.$message.success('操作成功！');
          }).catch((e) => {
            this.$message.error('操作失败：' + e.message);
          }).finally(() => {
            this.pageLoading = false;
          })
        }).catch(() => {
        });
      } else if (command === "reportTask") {
        this.pageLoading = true;
        jacocoListJars(cluster, namespace, pod).then(response => {
          this.jacocoJarOptions = response.data
          this.jacocoVisible = true;
        }).catch((e) => {
          this.$message.error('操作失败：' + e.message);
        }).finally(() => {
          this.pageLoading = false;
        })
      } else {
        console.error("unknown command: " + command);
      }
    },
    jacocoReportTaskCreate() {
      let pod = this.tableData[0].name;
      let cluster = this.cluster;
      let namespace = this.namespace;
      let jars = ""
      if (this.jacocoForm.jars && this.jacocoForm.jars.length > 0) {
        jars = this.jacocoForm.jars.join(",")
      }
      this.jacocoReportDisable = true;
      jacocoReportTask(cluster, namespace, pod, jars).then(response => {
        this.$message.success('操作成功！');
        this.jacocoVisible = false;
        window.open(response.data)
      }).catch((e) => {
        this.$message.error('操作失败：' + e.message);
      }).finally(() => {
        this.jacocoReportDisable = false;
      })
    },
    appBtnDropdownCommand(command) {
      console.log("app button dropdown command: " + command);
      if (command === "scale") {
        this.appScaleDialog();
      } else if (command === "redeploy") {
        this.redeployDialog();
      } else if (command === "rollback") {
        this.rollbackDialog();
      } else if (command === "java_cms") {
        this.showJavaCMS();
      } else if (command === "resourceUpdate") {
        this.resourceUpdateDialog();
      } else {
        console.error("unknown command: " + command);
      }
    },
    manageDropdownCommand(command) {
      console.log("manage dropdown command: " + command);
      let parts = command.split("##");
      if (parts.length !== 2) {
        return
      }
      let cmd = parts[0];
      let podName = parts[1];
      if (cmd === "detail") {
        this.podDetailDialog(podName);
      } else if (cmd === "delete") {
        this.podRemove(podName);
      } else if (cmd === "deregister") {
        this.podDeregister(podName);
      } else if (cmd === "versionRetain") {
        this.podVersionRetain(podName);
      } else if (cmd === "file") {
        this.podLogFile(podName);
      } else if (cmd === "stdout") {
        this.podStdoutLog(podName);
      } else if (cmd === "level") {
        let pod = this.findPodByName(podName);
        let url = `/api/page/redirect?type=logConf&cluster=${this.cluster}&keyword=${pod.podIP}`
        window.open(url)
      } else if (cmd === "chAppLog") {
        this.chLogPage(this.cluster, this.namespace, this.app, podName)
      } else if (cmd === "chTomcatAccess") {
        this.chTomcatAccessLogPage(this.cluster, this.namespace, this.app, podName)
      } else {
        this.$message.error('未知操作：' + command);
      }
    },
    podRemove(pod) {
      this.$confirm(`删除后会重新创建一个新的实例，是否要继续删除实例？ 实例名：${pod}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableLoading = true;
        podDelete(this.cluster, this.namespace, pod).then(response => {
          this.tableLoading = false;
          this.$message.success('操作成功,请等待一段时间再刷新页面数据');
          this.loadPod();
        }).catch((e) => {
          this.$message.error(e.message);
          this.tableLoading = false;
        });
      }).catch((e) => {
        console.error(e);
      });
    },
    podDeregister(pod) {
      this.$confirm("摘除后，实例的HTTP流量、Dubbo流量将从服务提供者列表删除，RocketMQ消费者将停止(生产者不受影响），实例不会被删除。注意Dubbo服务必须等新实例启动后才会真正摘除。是否继续？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableLoading = true;
        podDeregister(this.cluster, this.namespace, pod).then(response => {
          this.tableLoading = false;
          this.$message.success('操作成功！');
          this.loadPod();
        }).catch((e) => {
          this.tableLoading = false;
          this.$message.error('操作失败：' + e.message);
        });
      }).catch((e) => {
        console.error(e);
      });
    },
    podVersionRetain(pod) {
      this.$confirm("实例版本被保留后，实例会继续接受流量，但不计入副本数，不受发版影响（发版时不会被删除，不需要的话请手动删除）。是否继续？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableLoading = true;
        podVersionRetain(this.cluster, this.namespace, pod).then(response => {
          this.tableLoading = false;
          this.$message.success('操作成功！');
          this.loadPod();
        }).catch((e) => {
          this.tableLoading = false;
          this.$message.error('操作失败：' + e.message);
        });
      }).catch((e) => {
        console.error(e);
      });
    },
    podStdoutLog(pod) {
      this.podStdoutVisible = true
      this.podStdout.cluster = this.cluster
      this.podStdout.namespace = this.namespace
      this.podStdout.pod = pod
      let podObj = this.tableData.find(item => item.name === pod)
      if (podObj) {
        this.podStdout.containers = [podObj.container0Name, ...podObj.initContainersName]
      }else {
        this.podStdout.containers = []
      }

    },
    resourceUpdateDialog() {
      this.resourceUpdateForm.cluster = this.cluster
      this.resourceUpdateForm.namespace = this.namespace
      this.resourceUpdateForm.app = this.app
      let firstPod = this.tableData[0]
      if (!firstPod) {
        this.$message.error("无任何副本，不支持资源调整")
        return
      }
      this.resourceUpdateForm.limitCPU = parseFloat((firstPod.limitCpu / 1000).toFixed(1))
      this.resourceUpdateForm.limitMemory = parseInt(firstPod.limitMemory / 1024 / 1024)
      this.resourceUpdateForm.requestCPU = parseFloat((this.tableData[0].requestCpu / 1000).toFixed(1))
      this.resourceUpdateForm.requestMemory = parseInt(this.tableData[0].requestMemory / 1024 / 1024)
      this.resourceUpdateVisible = true;
    },
    resourceUpdate() {
      this.resourceLoading = true;
      updateDeploymentResource(this.resourceUpdateForm).then(response => {
        this.$message.success('操作成功，3秒后将自动刷新页面');
        this.resourceUpdateVisible = false
        setTimeout(function () {
          window.location.reload();
        }, 3000)
      }).catch((e) => {
        this.$message.error('操作失败：' + e.message);
      }).finally(() => {
        this.resourceLoading = false;
      })
    }
  }
}
</script>

<style>

/*dropdown button*/
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin-bottom: 20px;
}

.cms-file-item {
  padding-left: 20px;
  display: inline-block;
  margin: 4px 0;
}

.el-drawer__body {
  overflow-y: auto;
}

.el-divider--horizontal {
  margin: 10px 0;
}

.app-info .el-form-item__label {
  line-height: 30px;
  width: auto;
}

.app-info .el-form-item__content {
  line-height: 30px;
  color: #606266;
}

.javaCMSTotal {
  margin-bottom: 10px;
  text-align: center
}

.divider-blockquote > span {
  color: #409EFF;
  margin-left: 5px;
  font-weight: bold;
}

.divider-blockquote > .el-divider {
  background-color: #409EFF;
  margin: 3px 0 10px;
}

.divider-blockquote.qa > span {
  color: #999;
}

.divider-blockquote.qa > .el-divider {
  background-color: #bbb;
}

.divider-blockquote.qa span.new-line {
  padding: 5px 10px 0;
  display: block
}

.app-info-tab {
  padding: 5px 0;
  font-size: 14px;
}

.app-info-tab > label {
  color: #99a9bf;
}

.app-info-tab > span {
  color: #606266;
  word-break: break-all;
}

.el-table td, .el-table th {
  padding: 8px 0;
}

.pod-status-green {
  background: #69c993;
  box-shadow: 0 4px 10px rgb(87 177 126 / 30%);
  margin-right: 3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block
}

.pod-status-red {
  background: #d15352;
  box-shadow: 0 4px 10px rgb(209 83 82 / 30%);
  margin-right: 3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block
}

.pod-status-orange {
  background: #E6A23C;
  box-shadow: 0 4px 10px rgb(230 162 60 / 30%);
  margin-right: 3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block
}

.pod-list-container .el-card__body, .el-main {
  padding: 5px;
}

.app-btn-group .el-button {
  padding: 10px 0 0;
  margin: 0 10px;
}

.file-drawer .el-drawer__header {
  margin-bottom: 5px;
}
</style>
