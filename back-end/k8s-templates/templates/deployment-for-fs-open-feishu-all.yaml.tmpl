apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{.App}}
  namespace: {{.Namespace}}
  labels:
    app: {{.App}}
    {{- range $key, $value := .Labels }}
    "{{$key}}": "{{$value}}"
    {{- end }}
  annotations:
    {{- range $key, $value := .Annotations}}
    "{{$key}}": "{{$value}}"
    {{- end}}
spec:
  replicas: {{.Replicas}}
  revisionHistoryLimit: {{.RevisionHistoryLimit}}
  selector:
    matchLabels:
      app: {{.App}}
      version: v0
  strategy:
    rollingUpdate:
      maxSurge: {{.MaxSurge}}
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: {{.App}}
        version: v0
      annotations:
        fxiaoke.com/deploy-id: "{{.DeployId}}"
        prometheus.io/metrics-type: jvm
        prometheus.io/path: /metrics
        prometheus.io/port: "8090"
        prometheus.io/scrape: "true"
        {{- range $key, $value := .PodAnnotations }}
        "{{$key}}": "{{$value}}"
        {{- end }}
    spec:
      {{- if .SchedulerName}}
      schedulerName: "{{.SchedulerName}}"
      {{- end}}
      dnsConfig:
        options:
          - name: single-request-reopen
      affinity:
        nodeAffinity:
        {{- if .Schedule.Node}}
          {{- if eq .Schedule.Strategy "REQUIRED"}}
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: fxiaoke.com/dedicated
                    operator: In
                    values:
                      - "{{.Schedule.Node}}"
          {{- else}}
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: fxiaoke.com/dedicated
                    operator: In
                    values:
                      - "{{.Schedule.Node}}"
              weight: 100
          {{- end}}
        {{- else}}
          {}
        {{- end}}
        {{- if .PartnerApps}}
        podAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        {{- range .PartnerApps}}
                        - {{.}}
                        {{- end}}
                topologyKey: kubernetes.io/hostname
              weight: 10
        {{- end}}
        {{- if .ExclusiveApps}}
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        {{- range .ExclusiveApps}}
                        - {{.}}
                        {{- end}}
                topologyKey: kubernetes.io/hostname
              weight: 10
        {{- end}}
        {{- if .Schedule.Node}}
      tolerations:
        - effect: NoSchedule
          operator: Exists
        {{- end}}
        {{- if .AddSysctlKeepalive}}
      securityContext:
        sysctls:
          - name: "net.ipv4.tcp_keepalive_time"
            value: "600"
          - name: "net.ipv4.tcp_keepalive_intvl"
            value: "20"
          - name: "net.ipv4.tcp_keepalive_probes"
            value: "3"
        {{- end}}
      volumes:
        - name: webapps
          emptyDir: {}
        - name: shared-ext
          emptyDir: {}
        {{- if .TomcatLogEmptyDir}}
        - name: logs
          emptyDir: {}
        {{- end}}
        {{- if .AppLogsEmptyDir}}
        - name: applogs
          emptyDir: {}
        {{- end}}
        {{- if .PVC.Enable}}
        - name: "nfs"
          persistentVolumeClaim:
            claimName: "{{.PVC.Name}}"
        {{- end}}
      restartPolicy: Always
      terminationGracePeriodSeconds: {{ add .PreStopRetainSeconds 20 }}
      initContainers:
      {{- if .Port80To8080}}
        - name: "iptables-80-to-8080"
          image: "reg.firstshare.cn/base/fs-iptables-80-to-8080:v1.0"
          imagePullPolicy: {{$.ImagePullPolicy}}
          securityContext:
            privileged: true
            runAsGroup: 0
            runAsUser: 0
            capabilities:
              add:
                - NET_ADMIN
       {{- end}}
      {{- range .Artifacts }}
        - name: "artifact-{{ randAlpha 6 | lower }}"
          image: "{{.ArtifactImage}}"
          imagePullPolicy: {{$.ImagePullPolicy}}
          env:
            - name: "GIT_URL"
              value: "{{.GitUrl}}"
            - name: "GIT_MODULE"
              value: "{{.Module}}"
            - name: "GIT_TAG"
              value: "{{.Tag}}"
            - name: "COMMIT_ID"
              value: "{{.CommitID}}"
            - name: "CONTEXT_PATH"
              value: "{{.ContextPath}}"
            - name: "ARTIFACT_PATH_SRC"
              value: "{{.ArtifactSrc}}"
            - name: "ARTIFACT_PATH_DST"
              value: "{{.ArtifactDst}}"
          volumeMounts:
            - mountPath: /opt/tomcat/webapps
              name: webapps
      {{- end }}
      {{- range .ExtInitContainers }}
        - name: "artifact-{{ randAlpha 6 | lower }}"
          image: "{{.ArtifactImage}}"
          imagePullPolicy: {{$.ImagePullPolicy}}
          env:
            - name: "SHARED_EXT_HOME"
              value: "/opt/tomcat/ext"
          volumeMounts:
            - mountPath: /opt/tomcat/ext
              name: shared-ext
      {{- end }}
      containers:
        - name: {{.App}}
          image: {{.AppImage}}
          imagePullPolicy: {{$.ImagePullPolicy}}
          securityContext:
            {{- if .Privileged}}
            privileged: true
            {{- end}}
            capabilities:
              add:
                - SYS_PTRACE
          ports:
            {{- range .Ports}}
            - containerPort: {{.Value}}
              name: {{.Name}}
            {{- end}}
          env:
            - name: K8S_CLUSTER
              value: {{.Cluster}}
            - name: K8S_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: K8S_APP
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['app']
            - name: K8S_POD
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: K8S_NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            {{- range .Envs}}
            - name: {{.Name}}
              value: "{{.Value}}"
            {{- end}}
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh","-c","fs-service.sh --operate=deregister --webhook='{{.PreStopWebhook}}' --sleep={{.PreStopRetainSeconds}}"]
          {{- if .LivenessProbe.Enable}}
          livenessProbe:
            exec:
              command:
                - health-check.sh
                - liveness
            initialDelaySeconds: {{.LivenessProbe.InitialDelaySeconds}}
            periodSeconds: {{.LivenessProbe.PeriodSeconds}}
            timeoutSeconds: {{.LivenessProbe.TimeoutSeconds}}
            failureThreshold: {{.LivenessProbe.FailureThreshold}}
            successThreshold: {{.LivenessProbe.SuccessThreshold}}
          {{- end}}
          {{- if .ReadinessProbe.Enable}}
          readinessProbe:
            exec:
              command:
                - health-check.sh
                - readiness
            initialDelaySeconds: {{.ReadinessProbe.InitialDelaySeconds}}
            periodSeconds: {{.ReadinessProbe.PeriodSeconds}}
            timeoutSeconds: {{.ReadinessProbe.TimeoutSeconds}}
            failureThreshold: {{.ReadinessProbe.FailureThreshold}}
            successThreshold: {{.ReadinessProbe.SuccessThreshold}}
          {{- end}}
          {{- if .StartupProbe.Enable}}
          startupProbe:
            exec:
              command:
                - health-check.sh
                - readiness
            initialDelaySeconds: {{.StartupProbe.InitialDelaySeconds}}
            periodSeconds: {{.StartupProbe.PeriodSeconds}}
            timeoutSeconds: {{.StartupProbe.TimeoutSeconds}}
            failureThreshold: {{.StartupProbe.FailureThreshold}}
            successThreshold: {{.StartupProbe.SuccessThreshold}}
          {{- end}}
          resources:
            limits:
              cpu: {{.Resources.LimitCPU}}
              memory: {{.Resources.LimitMemory}}Mi
            requests:
              cpu: {{.Resources.RequestCPU}}
              memory: {{.Resources.RequestMemory}}Mi
          volumeMounts:
            - mountPath: /opt/tomcat/webapps
              name: webapps
            - mountPath: /opt/tomcat/ext
              name: shared-ext
            {{- if .TomcatLogEmptyDir}}
            - mountPath: /opt/tomcat/logs
              name: logs
            {{- end}}
            {{- if .AppLogsEmptyDir}}
            - mountPath: /opt/applogs
              name: applogs
            {{- end}}
            {{- if .PVC.Enable}}
            - name: "nfs"
              mountPath: "{{.PVC.MountPath}}"
            {{- end}}


