package log_service

import (
	"encoding/json"
	"fs-k8s-app-manager/models"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"testing"
)

// setupTestDB creates an in-memory SQLite database for testing
func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// Auto migrate the schema
	err = db.AutoMigrate(&models.Log{})
	if err != nil {
		panic("failed to migrate database")
	}

	return db
}

func TestCreate(t *testing.T) {
	tests := []struct {
		name     string
		author   string
		operate  string
		target   string
		jsonAble interface{}
	}{
		{
			name:     "Create with string content",
			author:   "test-user",
			operate:  "CREATE",
			target:   "test-app",
			jsonAble: "Simple string content",
		},
		{
			name:     "Create with struct content",
			author:   "admin",
			operate:  "UPDATE",
			target:   "production-app",
			jsonAble: map[string]interface{}{
				"replicas": 3,
				"image":    "nginx:latest",
				"env":      "production",
			},
		},
		{
			name:     "Create with slice content",
			author:   "developer",
			operate:  "SCALE",
			target:   "test-service",
			jsonAble: []string{"pod1", "pod2", "pod3"},
		},
		{
			name:     "Create with complex struct",
			author:   "operator",
			operate:  "DEPLOY",
			target:   "microservice",
			jsonAble: struct {
				Name     string            `json:"name"`
				Version  string            `json:"version"`
				Replicas int               `json:"replicas"`
				Labels   map[string]string `json:"labels"`
			}{
				Name:     "test-service",
				Version:  "v1.0.0",
				Replicas: 5,
				Labels:   map[string]string{"env": "test", "team": "backend"},
			},
		},
		{
			name:     "Create with nil content",
			author:   "system",
			operate:  "CLEANUP",
			target:   "temp-resources",
			jsonAble: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// This test will fail in the actual environment without proper DB setup
			// But it tests that the function doesn't panic and handles different data types
			Create(tt.author, tt.operate, tt.target, tt.jsonAble)
			
			// The function should not panic regardless of input
			// In a real test environment, you would verify the log was actually saved
		})
	}
}

func TestCreateBySys(t *testing.T) {
	tests := []struct {
		name     string
		operate  string
		target   string
		jsonAble interface{}
	}{
		{
			name:     "System log with string",
			operate:  "SYSTEM_CLEANUP",
			target:   "old-logs",
			jsonAble: "Cleaned up 100 old log entries",
		},
		{
			name:     "System log with data",
			operate:  "SYSTEM_BACKUP",
			target:   "database",
			jsonAble: map[string]interface{}{
				"backup_size": "1.2GB",
				"duration":    "5 minutes",
				"status":      "success",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			CreateBySys(tt.operate, tt.target, tt.jsonAble)
			
			// The function should not panic
			// In a real test, you would verify the author is set to "system"
		})
	}
}

func TestSave(t *testing.T) {
	// Test the Save function with various log entities
	tests := []struct {
		name   string
		entity *models.Log
	}{
		{
			name: "Valid log entity",
			entity: &models.Log{
				Author:  "test-user",
				Operate: "CREATE",
				Target:  "test-app",
				Content: "Test log content",
			},
		},
		{
			name: "Log with empty content",
			entity: &models.Log{
				Author:  "user",
				Operate: "DELETE",
				Target:  "resource",
				Content: "",
			},
		},
		{
			name: "Log with JSON content",
			entity: &models.Log{
				Author:  "admin",
				Operate: "UPDATE",
				Target:  "config",
				Content: `{"key": "value", "number": 42}`,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := Save(tt.entity)
			
			// In test environment without proper DB, this will likely fail
			if err != nil {
				t.Logf("Save returned error (expected in test environment): %v", err)
			}
			
			// Test with nil entity
			err = Save(nil)
			if err == nil {
				t.Errorf("Save should return error for nil entity")
			}
		})
	}
}

func TestSearch(t *testing.T) {
	tests := []struct {
		name    string
		user    string
		operate string
		target  string
		page    int
		limit   int
	}{
		{
			name:    "Search all logs",
			user:    "",
			operate: "",
			target:  "",
			page:    1,
			limit:   10,
		},
		{
			name:    "Search by user",
			user:    "test-user",
			operate: "",
			target:  "",
			page:    1,
			limit:   20,
		},
		{
			name:    "Search by operation",
			user:    "",
			operate: "CREATE",
			target:  "",
			page:    1,
			limit:   15,
		},
		{
			name:    "Search by target",
			user:    "",
			operate: "",
			target:  "test-app",
			page:    1,
			limit:   25,
		},
		{
			name:    "Search with all filters",
			user:    "admin",
			operate: "UPDATE",
			target:  "production",
			page:    2,
			limit:   5,
		},
		{
			name:    "Search with pagination",
			user:    "",
			operate: "",
			target:  "",
			page:    3,
			limit:   50,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logs, err := Search(tt.user, tt.operate, tt.target, tt.page, tt.limit)
			
			// In test environment without proper DB, this will likely fail
			if err != nil {
				t.Logf("Search returned error (expected in test environment): %v", err)
			}
			
			// logs should be a slice (even if empty)
			if logs == nil {
				t.Errorf("Search should return a non-nil slice")
			}
		})
	}
}

func TestJSONSerialization(t *testing.T) {
	// Test how different types are serialized to JSON for log content
	tests := []struct {
		name     string
		input    interface{}
		expected bool // whether JSON serialization should succeed
	}{
		{
			name:     "String input",
			input:    "simple string",
			expected: false, // strings are handled specially, not JSON serialized
		},
		{
			name: "Map input",
			input: map[string]interface{}{
				"key1": "value1",
				"key2": 42,
				"key3": true,
			},
			expected: true,
		},
		{
			name:     "Slice input",
			input:    []string{"item1", "item2", "item3"},
			expected: true,
		},
		{
			name: "Struct input",
			input: struct {
				Name  string `json:"name"`
				Value int    `json:"value"`
			}{
				Name:  "test",
				Value: 123,
			},
			expected: true,
		},
		{
			name:     "Nil input",
			input:    nil,
			expected: true,
		},
		{
			name:     "Function input (should fail)",
			input:    func() {},
			expected: false,
		},
		{
			name:     "Channel input (should fail)",
			input:    make(chan int),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test JSON marshaling directly
			_, err := json.Marshal(tt.input)
			
			if tt.expected && err != nil {
				t.Errorf("Expected JSON marshaling to succeed for %T, but got error: %v", tt.input, err)
			}
			
			if !tt.expected && err == nil {
				// Some inputs might unexpectedly succeed, that's okay
				t.Logf("JSON marshaling unexpectedly succeeded for %T", tt.input)
			}
			
			// Test the actual Create function with this input
			Create("test-user", "TEST", "test-target", tt.input)
			// Should not panic regardless of input type
		})
	}
}

func TestLogContentHandling(t *testing.T) {
	// Test how the Create function handles different content types
	testCases := []struct {
		name        string
		content     interface{}
		expectError bool
	}{
		{
			name:        "Valid JSON object",
			content:     map[string]string{"status": "success", "message": "operation completed"},
			expectError: false,
		},
		{
			name:        "Valid JSON array",
			content:     []int{1, 2, 3, 4, 5},
			expectError: false,
		},
		{
			name:        "String content",
			content:     "This is a simple string log message",
			expectError: false,
		},
		{
			name:        "Empty string",
			content:     "",
			expectError: false,
		},
		{
			name:        "Numeric content",
			content:     42,
			expectError: false,
		},
		{
			name:        "Boolean content",
			content:     true,
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test that Create doesn't panic with various content types
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("Create panicked with content type %T: %v", tc.content, r)
				}
			}()
			
			Create("test-user", "TEST_OPERATION", "test-target", tc.content)
		})
	}
}

// Integration test example (would require proper test database setup)
func TestLogServiceIntegration(t *testing.T) {
	t.Skip("Integration test requires proper database setup")
	
	// Example of what an integration test might look like:
	/*
	// Setup test database
	testDB := setupTestDB()
	defer testDB.Close()
	
	// Mock the global DB
	// (In practice, you'd need dependency injection or a way to override the global DB)
	
	// Test Create and Save
	testLog := &models.Log{
		Author:  "integration-test-user",
		Operate: "CREATE",
		Target:  "test-resource",
		Content: "Integration test log entry",
	}
	
	err := Save(testLog)
	if err != nil {
		t.Fatalf("Failed to save log: %v", err)
	}
	
	// Test Search
	logs, err := Search("integration-test-user", "", "", 1, 10)
	if err != nil {
		t.Fatalf("Failed to search logs: %v", err)
	}
	
	if len(logs) == 0 {
		t.Errorf("Expected to find at least one log entry")
	}
	
	// Verify the log content
	found := false
	for _, log := range logs {
		if log.Author == "integration-test-user" && log.Content == "Integration test log entry" {
			found = true
			break
		}
	}
	
	if !found {
		t.Errorf("Could not find the created log entry")
	}
	*/
}

// Benchmark tests
func BenchmarkCreate(b *testing.B) {
	testData := map[string]interface{}{
		"operation": "benchmark_test",
		"timestamp": "2023-12-25T10:30:00Z",
		"details":   []string{"item1", "item2", "item3"},
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Create("benchmark-user", "BENCHMARK", "benchmark-target", testData)
	}
}

func BenchmarkCreateBySys(b *testing.B) {
	testData := "System benchmark test message"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CreateBySys("BENCHMARK", "system-target", testData)
	}
}

func BenchmarkJSONMarshal(b *testing.B) {
	testData := map[string]interface{}{
		"name":     "benchmark test",
		"value":    42,
		"items":    []string{"a", "b", "c"},
		"metadata": map[string]string{"key": "value"},
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		json.Marshal(testData)
	}
}
