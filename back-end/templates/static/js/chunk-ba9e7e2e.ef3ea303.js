(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ba9e7e2e"],{"1e42":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)},n=[],o=(a("a481"),a("25ca")),i=a("21a6"),l=a.n(i),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var e=this.tableRef.$el,t=o["a"].table_to_book(e,{raw:!0}),a=o["b"](t,{bookType:"xlsx",bookSST:!0,type:"array"});try{var r="export-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";l.a.saveAs(new Blob([a],{type:"application/octet-stream"}),r)}catch(n){this.$message.error("导出失败, err: "+n.message),console.error(n)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,u=a("2877"),p=Object(u["a"])(c,r,n,!1,null,null,null);t["a"]=p.exports},"28a5":function(e,t,a){"use strict";var r=a("aae3"),n=a("cb7c"),o=a("ebd6"),i=a("0390"),l=a("9def"),s=a("5f1b"),c=a("520a"),u=a("79e5"),p=Math.min,d=[].push,m="split",f="length",b="lastIndex",v=4294967295,g=!u((function(){RegExp(v,"y")}));a("214f")("split",2,(function(e,t,a,u){var h;return h="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[f]||2!="ab"[m](/(?:ab)*/)[f]||4!="."[m](/(.?)(.?)/)[f]||"."[m](/()()/)[f]>1||""[m](/.?/)[f]?function(e,t){var n=String(this);if(void 0===e&&0===t)return[];if(!r(e))return a.call(n,e,t);var o,i,l,s=[],u=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),p=0,m=void 0===t?v:t>>>0,g=new RegExp(e.source,u+"g");while(o=c.call(g,n)){if(i=g[b],i>p&&(s.push(n.slice(p,o.index)),o[f]>1&&o.index<n[f]&&d.apply(s,o.slice(1)),l=o[0][f],p=i,s[f]>=m))break;g[b]===o.index&&g[b]++}return p===n[f]?!l&&g.test("")||s.push(""):s.push(n.slice(p)),s[f]>m?s.slice(0,m):s}:"0"[m](void 0,0)[f]?function(e,t){return void 0===e&&0===t?[]:a.call(this,e,t)}:a,[function(a,r){var n=e(this),o=void 0==a?void 0:a[t];return void 0!==o?o.call(a,n,r):h.call(String(n),a,r)},function(e,t){var r=u(h,e,this,t,h!==a);if(r.done)return r.value;var c=n(e),d=String(this),m=o(c,RegExp),f=c.unicode,b=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(g?"y":"g"),_=new m(g?c:"^(?:"+c.source+")",b),F=void 0===t?v:t>>>0;if(0===F)return[];if(0===d.length)return null===s(_,d)?[d]:[];var w=0,y=0,S=[];while(y<d.length){_.lastIndex=g?y:0;var x,k=s(_,g?d:d.slice(y));if(null===k||(x=p(l(_.lastIndex+(g?0:y)),d.length))===w)y=i(d,y,f);else{if(S.push(d.slice(w,y)),S.length===F)return S;for(var C=1;C<=k.length-1;C++)if(S.push(k[C]),S.length===F)return S;y=w=x}}return S.push(d.slice(w)),S}]}))},8843:function(e,t,a){},aae3:function(e,t,a){var r=a("d3f4"),n=a("2d95"),o=a("2b4c")("match");e.exports=function(e){var t;return r(e)&&(void 0!==(t=e[o])?!!t:"RegExp"==n(e))}},b562:function(e,t,a){"use strict";a.d(t,"p",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return i})),a.d(t,"l",(function(){return l})),a.d(t,"j",(function(){return s})),a.d(t,"d",(function(){return c})),a.d(t,"i",(function(){return u})),a.d(t,"h",(function(){return p})),a.d(t,"m",(function(){return d})),a.d(t,"o",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"e",(function(){return b})),a.d(t,"c",(function(){return v})),a.d(t,"k",(function(){return g})),a.d(t,"q",(function(){return h})),a.d(t,"n",(function(){return _})),a.d(t,"g",(function(){return F}));var r=a("b775");function n(e){return Object(r["a"])({url:"/v1/app/search",method:"get",params:e})}function o(){return Object(r["a"])({url:"/v1/app/apps-with-env",method:"get"})}function i(){return Object(r["a"])({url:"/v1/app/all",method:"get"})}function l(){return Object(r["a"])({url:"/v1/app/names",method:"get"})}function s(e){return Object(r["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function c(e){return Object(r["a"])({url:"/v1/app",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/v1/app",method:"put",data:e})}function p(e){return Object(r["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function d(e,t,a){return Object(r["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:a}})}function m(e){return Object(r["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function f(e){return Object(r["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function v(e){return Object(r["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function g(e,t){return Object(r["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function h(e,t){return Object(r["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function _(e,t){return Object(r["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function F(e){return Object(r["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:e}})}},bb0b:function(e,t,a){"use strict";a.d(t,"k",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return i})),a.d(t,"l",(function(){return l})),a.d(t,"b",(function(){return s})),a.d(t,"f",(function(){return c})),a.d(t,"n",(function(){return u})),a.d(t,"o",(function(){return p})),a.d(t,"m",(function(){return d})),a.d(t,"c",(function(){return m})),a.d(t,"d",(function(){return f})),a.d(t,"g",(function(){return b})),a.d(t,"i",(function(){return v})),a.d(t,"h",(function(){return g})),a.d(t,"j",(function(){return h}));var r=a("b775");function n(e){return Object(r["a"])({url:"/v1/k8s/scale/auto",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/v1/k8s/scale/auto",method:"post",data:e})}function i(e){return Object(r["a"])({url:"/v1/k8s/scale/auto",method:"delete",params:{id:e}})}function l(e){return Object(r["a"])({url:"/v1/k8s/scale/cron",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/v1/k8s/scale/cron",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/v1/k8s/scale/cron",method:"delete",params:{id:e}})}function u(e){return Object(r["a"])({url:"/v1/k8s/scale/log",method:"get",params:e})}function p(e){return Object(r["a"])({url:"/v1/k8s/scale/monitor/log",method:"get",params:e})}function d(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler",method:"get",params:e})}function m(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler/create-for-core-app",method:"post",params:e})}function b(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler",method:"delete",data:e})}function v(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler/migrate?cluster="+e,method:"post"})}function g(){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler/all-cluster-autoscaler-v2",method:"get"})}function h(e,t,a){return Object(r["a"])({url:"/v1/k8s/scale/all-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}},c425:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"集群"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:e.clusterChange},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",filterable:""},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},[a("el-option",{key:"",attrs:{label:"所有",value:""}}),e._v(" "),e._l(e.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用名"}},[a("el-select",{staticStyle:{width:"280px"},attrs:{filterable:"",placeholder:"请选择应用",clearable:""},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app",t)},expression:"searchForm.app"}},e._l(e.apps,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")])],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:function(t){return e.showEditDialog(null)}}},[e._v("新建")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}}),e._v(" "),a("el-button",{staticStyle:{"margin-left":"40px",color:"#ccc"},attrs:{type:"text"},on:{click:e.migrateConfig}},[e._v("迁移")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"10px",color:"#ccc"},attrs:{type:"text"},on:{click:e.addScaleForCoreAppDialog}},[e._v("补充核心服务配置")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"10px",color:"#ccc"},attrs:{type:"text"},on:{click:e.scaleUpAllHours}},[e._v("修改扩容时间为全天")])],1)],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",prop:"metadata.name"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"metadata.namespace"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-weight":"bold"}},[e._v("\n          "+e._s(t.row.metadata.namespace)+"\n        ")]),e._v(" "),a("div",{staticStyle:{"font-size":"10px",color:"#999"}},[e._v("\n          集群："+e._s(t.row.cluster)+"\n        ")]),e._v(" "),t.row.metadata.annotations["fxiaoke.com/last-modify-user"]?a("div",{staticStyle:{"font-size":"10px",color:"#999"}},[e._v("\n          最后修改人 "+e._s(t.row.metadata.annotations["fxiaoke.com/last-modify-user"])+"\n        ")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"状态",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.spec.paused?a("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",size:"small"}},[e._v("\n          停用\n        ")]):a("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"success",size:"mini"}},[e._v("\n          启用\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"扩容触发器",width:"180",prop:"cpuTargetPercent"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"table-item"},[e._v("\n          CPU使用率："+e._s(t.row.spec.triggers[0].metadata.value)+"%\n        ")]),e._v(" "),a("div",{staticClass:"table-item"},[e._v("\n          持续时间(秒)："+e._s(t.row.spec.scaleUp.stabilizationWindowSeconds)+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"扩缩配置"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"table-item"},[e._v("\n          最大副本："+e._s(t.row.spec.maxReplicaCount)+"\n        ")]),e._v(" "),a("div",{staticClass:"table-item"},[e._v("\n          扩容步长："+e._s(t.row.spec.scaleUp.replicaStep)+"\n        ")]),e._v(" "),a("div",{staticClass:"table-item"},[e._v("\n          时间窗口："+e._s(t.row.spec.scaleUp.hourWindowDesc)+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"缩容配置"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"table-item"},[e._v("\n          最小副本："+e._s(t.row.spec.minReplicaCount)+"\n        ")]),e._v(" "),a("div",{staticClass:"table-item"},[e._v("\n          缩容步长："+e._s(t.row.spec.scaleDown.replicaStep)+"\n        ")]),e._v(" "),a("div",{staticClass:"table-item"},[e._v("\n          时间窗口："+e._s(t.row.spec.scaleDown.hourWindowDesc)+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"执行状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        当前副本："+e._s(t.row.status.currentReplica||"0")),a("br"),e._v("\n        扩容次数："+e._s(t.row.status.scaleUpCount||"0")),a("br"),e._v("\n        缩容次数："+e._s(t.row.status.scaleDownCount||"0")+"\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{"font-size":"16px",color:"#67c23a"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.monitorGrafanaPage(e.searchForm.cluster,t.row.metadata.namespace,t.row.metadata.name)}}},[e._v("监控分析\n        ")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"0"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.showEditDialog(t.row)}}},[e._v("编辑\n        ")]),e._v(" "),a("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(a){return e.deleteItem(t.row)}}},[a("el-button",{staticStyle:{color:"orangered"},attrs:{slot:"reference",type:"text",size:"mini"},slot:"reference"},[e._v("删除\n          ")])],1),e._v(" "),a("br"),e._v(" "),a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.showDetailDialog(t.row)}}},[e._v("配置详情\n        ")]),e._v(" "),a("router-link",{attrs:{to:{name:"pipeline-index",query:{app:t.row.metadata.name}},target:"_blank"}},[a("i",{staticStyle:{color:"#409EFF","font-weight":"500","font-size":"12px","font-style":"normal"}},[e._v("发布流程")])]),e._v(" "),a("router-link",{attrs:{to:{name:"pod-index",query:{cluster:t.row.cluster,namespace:t.row.metadata.namespace,app:t.row.metadata.name}},target:"_blank"}},[a("i",{staticStyle:{color:"#409EFF","font-weight":"500","font-size":"12px","font-style":"normal"}},[e._v("实例管理")])])]}}])})],1),e._v(" "),a("el-dialog",{attrs:{title:"扩缩容编辑",visible:e.dialogVisible,width:"700px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"dialogEditForm",staticClass:"pod-auto-scaler-edit-form",attrs:{model:e.editForm,"label-width":"120px",rules:e.editFormRules}},[a("el-form-item",{attrs:{label:"禁用"}},[a("el-switch",{model:{value:e.editForm.paused,callback:function(t){e.$set(e.editForm,"paused",t)},expression:"editForm.paused"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"集群"}},[a("el-form-item",{attrs:{prop:"cluster"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",disabled:e.editFormAppEditDisable},model:{value:e.editForm.cluster,callback:function(t){e.$set(e.editForm,"cluster",t)},expression:"editForm.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"命名空间",prop:"namespace"}},[a("el-form-item",{attrs:{prop:"namespace"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",disabled:e.editFormAppEditDisable},model:{value:e.editForm.namespace,callback:function(t){e.$set(e.editForm,"namespace",t)},expression:"editForm.namespace"}},e._l(e.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",disabled:e.editFormAppEditDisable},model:{value:e.editForm.app,callback:function(t){e.$set(e.editForm,"app",t)},expression:"editForm.app"}},e._l(e.appOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"cpu使用率阈值"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.editForm.cpuTargetPercent,callback:function(t){e.$set(e.editForm,"cpuTargetPercent",t)},expression:"editForm.cpuTargetPercent"}},e._l(e.cpuPercentOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"持续时间",prop:"scaleUpStabilizationWindowSeconds"}},[a("el-input-number",{attrs:{min:30,max:300},model:{value:e.editForm.scaleUpStabilizationWindowSeconds,callback:function(t){e.$set(e.editForm,"scaleUpStabilizationWindowSeconds",t)},expression:"editForm.scaleUpStabilizationWindowSeconds"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"扩容步长",prop:"scaleUpReplicaStep"}},[a("el-input-number",{attrs:{min:2,max:20},model:{value:e.editForm.scaleUpReplicaStep,callback:function(t){e.$set(e.editForm,"scaleUpReplicaStep",t)},expression:"editForm.scaleUpReplicaStep"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"最小副本数",prop:"maxReplicas"}},[a("el-input-number",{attrs:{min:1},model:{value:e.editForm.minReplicas,callback:function(t){e.$set(e.editForm,"minReplicas",e._n(t))},expression:"editForm.minReplicas"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"最大副本数",prop:"maxReplicas"}},[a("el-input-number",{attrs:{min:4,max:20},model:{value:e.editForm.maxReplicas,callback:function(t){e.$set(e.editForm,"maxReplicas",e._n(t))},expression:"editForm.maxReplicas"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"缩容步长"}},[a("el-input-number",{attrs:{min:1,max:5,disabled:""},model:{value:e.editForm.scaleDownReplicaStep,callback:function(t){e.$set(e.editForm,"scaleDownReplicaStep",t)},expression:"editForm.scaleDownReplicaStep"}}),e._v(" "),a("span",{staticStyle:{color:"orangered"}},[e._v("（暂由系统分配）")])],1),e._v(" "),a("el-form-item",{attrs:{label:"扩容时间窗口"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{disabled:""},model:{value:e.editForm.scaleUpHourWindow,callback:function(t){e.$set(e.editForm,"scaleUpHourWindow",t)},expression:"editForm.scaleUpHourWindow"}}),e._v(" "),a("span",{staticStyle:{color:"orangered"}},[e._v("（暂由系统分配）")])],1),e._v(" "),a("el-form-item",{attrs:{label:"缩容时间窗口"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{disabled:""},model:{value:e.editForm.scaleDownHourWindow,callback:function(t){e.$set(e.editForm,"scaleDownHourWindow",t)},expression:"editForm.scaleDownHourWindow"}}),e._v(" "),a("span",{staticStyle:{color:"orangered"}},[e._v("（暂由系统分配）")])],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],attrs:{type:"primary"},on:{click:function(t){return e.createItem()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:e.detailDialog.title,visible:e.detailDialog.visible,width:"50%",top:"5vh"},on:{"update:visible":function(t){return e.$set(e.detailDialog,"visible",t)}}},[a("div",{staticStyle:{"margin-top":"-20px",overflow:"auto"}},[a("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[e._v(e._s(e.detailDialog.content))])])]),e._v(" "),a("el-dialog",{attrs:{title:"修改时间窗口为全天的命令",visible:e.scaleUpAllHoursDialogVisible,width:"50%"},on:{"update:visible":function(t){e.scaleUpAllHoursDialogVisible=t}}},[a("div",[a("p",[e._v("1. 修改单个服务")]),e._v(" "),a("div",{staticStyle:{padding:"10px","background-color":"#eee"}},[e._v('\n       kubectl patch podautoscaler [app] -n [namesapce] --type=\'json\' -p=\'[{"op":"replace","path":"/spec/scaleUp/hourWindow","value":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]}]\'\n     ')]),e._v(" "),a("p",[e._v("2. 生成某个集群的批量修改命令")]),e._v(" "),a("div",{staticStyle:{padding:"10px","background-color":"#eee"}},[e._v("\n       kubectl get pas --all-namespaces --no-headers | while read ns name _; do"),a("br"),e._v('\n       echo "kubectl patch podautoscaler -n $ns $name --type=\'json\' -p=\'[{\\"op\\":\\"replace\\",\\"path\\":\\"/spec/scaleUp/hourWindow\\",\\"value\\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]}]\'"'),a("br"),e._v("\n       done\n     ")])])]),e._v(" "),a("el-dialog",{attrs:{title:"增加核心服务的自动扩缩容",visible:e.addScaleForCoreAppDialogVisible,width:"40%"},on:{"update:visible":function(t){e.addScaleForCoreAppDialogVisible=t}}},[a("div",[a("p",{staticStyle:{"line-height":"20px",color:"orangered","margin-top":"-30px","margin-left":"30px"}},[e._v("\n        说明: 如果满足服务级别的应用在集群中没有自动扩缩容配置，则使用默认配置初始化扩缩容配置。只有管理员才有权限操作\n      ")]),e._v(" "),a("el-form",{ref:"addScaleForCoreAppForm",attrs:{model:e.addScaleForCoreAppForm,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"集群"}},[a("el-input",{attrs:{disabled:""},model:{value:e.addScaleForCoreAppForm.cluster,callback:function(t){e.$set(e.addScaleForCoreAppForm,"cluster",t)},expression:"addScaleForCoreAppForm.cluster"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"命名空间"}},[a("el-input",{attrs:{disabled:""},model:{value:e.addScaleForCoreAppForm.namespace,callback:function(t){e.$set(e.addScaleForCoreAppForm,"namespace",t)},expression:"addScaleForCoreAppForm.namespace"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"服务级别"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.addScaleForCoreAppForm.serviceLevel,callback:function(t){e.$set(e.addScaleForCoreAppForm,"serviceLevel",t)},expression:"addScaleForCoreAppForm.serviceLevel"}},[a("el-option",{attrs:{label:"L0",value:"L0"}}),e._v(" "),a("el-option",{attrs:{label:"L1",value:"L1"}})],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.addScaleForCoreAppDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.addScaleForCoreAppBtnLoading,expression:"addScaleForCoreAppBtnLoading"}],attrs:{type:"primary"},on:{click:e.addScaleForCoreApp}},[e._v("确 定")])],1)],1)])],1)},n=[],o=(a("28a5"),a("2d63")),i=(a("7f7f"),a("b562")),l=a("bb0b"),s=a("1e42"),c=a("da37"),u=(a("27aa"),{components:{ClipboardIcon:c["a"],ExportButton:s["a"]},mounted:function(){var e=this;this.$route.query.cluster?this.searchForm.cluster=this.$route.query.cluster:this.searchForm.cluster=this.clusterOptions[0].name,this.$route.query.namespace&&(this.searchForm.namespace=this.$route.query.namespace),this.$route.query.app&&(this.searchForm.app=this.$route.query.app),this.searchForm.app&&this.loadTableData(),Object(i["l"])().then((function(t){e.apps=t.data})).catch((function(t){e.$message.error("加载应用数据出错！ "+t.message)}))},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(r){t.e(r)}finally{t.f()}}return[]},cpuPercentOptions:function(){for(var e=[],t=50;t<=100;t+=5)e.push({name:t+"%",value:t});return e}},data:function(){return{searchEnv:"",apps:[],searchForm:{cluster:"",namespace:"",app:""},editForm:{},detailDialog:{visible:!1,title:"配置详情",content:"--"},editFormAppEditDisable:!1,editFormRules:{app:[{required:!0,message:"值不能为空",trigger:"blur"}],cluster:[{required:!0,message:"值不能为空",trigger:"blur"}],namespace:[{required:!0,message:"值不能为空",trigger:"blur"}],minReplicas:[{required:!0,message:"值不能为空",trigger:"blur"}],maxReplicas:[{required:!0,message:"值不能为空",trigger:"blur"}],cpuTargetPercent:[{required:!0,message:"值不能为空",trigger:"blur"}],scaleUpStabilizationWindowSeconds:[{required:!0,message:"值不能为空",trigger:"blur"}],scaleUpReplicaStep:[{required:!0,message:"值不能为空",trigger:"blur"}]},tableData:[],tableLoading:!1,dialogVisible:!1,pageLoading:!1,appOptions:[],monitorLogVisible:!1,monitorLogTableData:[],scaleUpAllHoursDialogVisible:!1,addScaleForCoreAppDialogVisible:!1,addScaleForCoreAppBtnLoading:!1,addScaleForCoreAppForm:{cluster:"",namespace:"",serviceLevel:""}}},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,Object(l["m"])(this.searchForm).then((function(t){var a,r=t.data,n=Object(o["a"])(r);try{for(n.s();!(a=n.n()).done;){var i=a.value;i.cluster=e.searchForm.cluster}}catch(l){n.e(l)}finally{n.f()}e.tableData=r})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},editFormReset:function(){this.editForm={app:"",cluster:this.searchForm.cluster,namespace:this.searchForm.namespace,paused:this.searchForm.paused,cpuTargetPercent:70,scaleUpStabilizationWindowSeconds:60,scaleUpReplicaStep:3,scaleDownReplicaStep:1,scaleUpHourWindow:[],scaleDownHourWindow:[],minReplicas:2,maxReplicas:4}},showDetailDialog:function(e){var t=JSON.stringify(e,null,2);this.detailDialog.content=t,this.detailDialog.visible=!0},monitorLogPage:function(e,t,a){var r="/api/page/redirect?type=clickhouse&logName=fs_k8s_app_scaler_metrics_dist&cluster=".concat(e,"&namespace=").concat(t,"&app=").concat(a,"&_t")+Date.now();window.open(r)},monitorGrafanaPage:function(e,t,a){var r="/api/page/redirect?type=grafana&dashboard=pod-auto-scaler&cluster=".concat(e,"&app=").concat(a,"&namespace=").concat(t);window.open(r)},showEditDialog:function(e){var t=this;this.appOptions.length<1&&Object(i["l"])().then((function(e){t.appOptions=e.data})).catch((function(e){t.$message.error(e.message)})),e?(this.editForm={app:e.metadata.name,cluster:e.cluster,namespace:e.metadata.namespace,paused:e.spec.paused,cpuTargetPercent:parseInt(e.spec.triggers[0].metadata.value),scaleUpStabilizationWindowSeconds:e.spec.scaleUp.stabilizationWindowSeconds,scaleUpReplicaStep:e.spec.scaleUp.replicaStep,minReplicas:e.spec.minReplicaCount,maxReplicas:e.spec.maxReplicaCount,scaleDownReplicaStep:e.spec.scaleDown.replicaStep,scaleUpHourWindow:e.spec.scaleUp.hourWindow,scaleDownHourWindow:e.spec.scaleDown.hourWindow},this.editFormAppEditDisable=!0):(this.editFormReset(),this.editFormAppEditDisable=!1),this.dialogVisible=!0},deleteItem:function(e){var t=this,a={cluster:e.cluster,namespace:e.metadata.namespace,app:e.metadata.name};Object(l["g"])(a).then((function(e){t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))},migrateConfig:function(){var e=this,t=this.searchForm.cluster;this.$prompt("请输入集群","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t}).then((function(t){var a=t.value;e.pageLoading=!0,Object(l["i"])(a).then((function(t){e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.pageLoading=!1}))})).catch((function(){}))},scaleUpAllHours:function(){this.scaleUpAllHoursDialogVisible=!0},addScaleForCoreAppDialog:function(){this.addScaleForCoreAppForm.cluster=this.searchForm.cluster,this.addScaleForCoreAppForm.namespace=this.searchForm.namespace,this.addScaleForCoreAppForm.cluster&&this.addScaleForCoreAppForm.namespace?(this.addScaleForCoreAppForm.serviceLevel="L0",this.addScaleForCoreAppDialogVisible=!0):this.$message.error("集群和环境不能为空")},addScaleForCoreApp:function(){var e=this;this.addScaleForCoreAppBtnLoading=!0,Object(l["d"])(this.addScaleForCoreAppForm).then((function(t){e.addScaleForCoreAppDialogVisible=!1,e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.addScaleForCoreAppBtnLoading=!1}))},clusterChange:function(){this.searchForm.namespace=""},createItem:function(){var e=this;this.$refs["dialogEditForm"].validate((function(t){if(!t)return!1;if("string"===typeof e.editForm.scaleUpHourWindow&&""!==e.editForm.scaleUpHourWindow.trim())try{e.editForm.scaleUpHourWindow=e.editForm.scaleUpHourWindow.split(",").map((function(e){return parseInt(e.trim(),10)})).filter((function(e){return!isNaN(e)}))}catch(a){return e.$message.warning("扩容时间窗口格式不正确，请使用逗号分隔的数字"),!1}if("string"===typeof e.editForm.scaleDownHourWindow&&""!==e.editForm.scaleDownHourWindow.trim())try{e.editForm.scaleDownHourWindow=e.editForm.scaleDownHourWindow.split(",").map((function(e){return parseInt(e.trim(),10)})).filter((function(e){return!isNaN(e)}))}catch(a){return e.$message.warning("缩容时间窗口格式不正确，请使用逗号分隔的数字"),!1}e.pageLoading=!0,Object(l["c"])(e.editForm).then((function(t){e.dialogVisible=!1,e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.pageLoading=!1}))}))}}}),p=u,d=(a("c7751"),a("2877")),m=Object(d["a"])(p,r,n,!1,null,null,null);t["default"]=m.exports},c7751:function(e,t,a){"use strict";a("8843")},da37:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(t){return e.copyToClipboard()}}},[a("i",{staticClass:"el-icon-document-copy"}),e._v(" "),this.buttonText?a("span",[e._v(e._s(this.buttonText))]):e._e()])},n=[],o={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var e=this,t=this.text;t?navigator.clipboard.writeText(t).then((function(){e.$message.success("复制成功")})).catch((function(){var a=document.createElement("input");document.body.appendChild(a),a.setAttribute("value",t),a.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(a),e.$message.success("复制成功")})):this.$message.warning("内容为空")}}},i=o,l=a("2877"),s=Object(l["a"])(i,r,n,!1,null,null,null);t["a"]=s.exports}}]);