package docker

import (
	"testing"
)

func TestGitTag2DockerTag(t *testing.T) {
	tests := []struct {
		name     string
		gitTag   string
		expected string
	}{
		{
			name:     "Simple tag without slash",
			gitTag:   "v1.0.0",
			expected: "v1.0.0",
		},
		{
			name:     "Tag with single slash",
			gitTag:   "feature/new-feature",
			expected: "feature---new-feature",
		},
		{
			name:     "Tag with multiple slashes",
			gitTag:   "release/v1.0.0/hotfix",
			expected: "release---v1.0.0---hotfix",
		},
		{
			name:     "Empty string",
			gitTag:   "",
			expected: "",
		},
		{
			name:     "Tag with only slashes",
			gitTag:   "///",
			expected: "---------",
		},
		{
			name:     "Complex branch name",
			gitTag:   "feature/user-management/auth",
			expected: "feature---user-management---auth",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GitTag2DockerTag(tt.gitTag)
			if result != tt.expected {
				t.<PERSON><PERSON>rf("GitTag2DockerTag(%q) = %q, expected %q", tt.gitTag, result, tt.expected)
			}
		})
	}
}

func TestDockerTag2GitTag(t *testing.T) {
	tests := []struct {
		name      string
		dockerTag string
		expected  string
	}{
		{
			name:      "Simple tag without dashes",
			dockerTag: "v1.0.0",
			expected:  "v1.0.0",
		},
		{
			name:      "Tag with triple dashes",
			dockerTag: "feature---new-feature",
			expected:  "feature/new-feature",
		},
		{
			name:      "Tag with multiple triple dashes",
			dockerTag: "release---v1.0.0---hotfix",
			expected:  "release/v1.0.0/hotfix",
		},
		{
			name:      "Empty string",
			dockerTag: "",
			expected:  "",
		},
		{
			name:      "Tag with only triple dashes",
			dockerTag: "---------",
			expected:  "///",
		},
		{
			name:      "Complex docker tag",
			dockerTag: "feature---user-management---auth",
			expected:  "feature/user-management/auth",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DockerTag2GitTag(tt.dockerTag)
			if result != tt.expected {
				t.Errorf("DockerTag2GitTag(%q) = %q, expected %q", tt.dockerTag, result, tt.expected)
			}
		})
	}
}

func TestGetDockerImageTag(t *testing.T) {
	tests := []struct {
		name     string
		image    string
		expected string
	}{
		{
			name:     "Simple image with tag",
			image:    "nginx:latest",
			expected: "latest",
		},
		{
			name:     "Image with registry and tag",
			image:    "registry.example.com/nginx:v1.0.0",
			expected: "v1.0.0",
		},
		{
			name:     "Image with multiple colons",
			image:    "registry.example.com:5000/nginx:latest",
			expected: "latest",
		},
		{
			name:     "Image without tag (no colon)",
			image:    "nginx",
			expected: "nginx",
		},
		{
			name:     "Complex image path with tag",
			image:    "harbor.example.com/project/app:feature---branch",
			expected: "feature---branch",
		},
		{
			name:     "Image with port and tag",
			image:    "localhost:5000/myapp:dev",
			expected: "dev",
		},
		{
			name:     "Empty string",
			image:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetDockerImageTag(tt.image)
			if result != tt.expected {
				t.Errorf("GetDockerImageTag(%q) = %q, expected %q", tt.image, result, tt.expected)
			}
		})
	}
}

func TestGetImageSimpleName(t *testing.T) {
	tests := []struct {
		name     string
		image    string
		expected string
	}{
		{
			name:     "Simple image name",
			image:    "nginx",
			expected: "nginx",
		},
		{
			name:     "Image with registry",
			image:    "registry.example.com/nginx",
			expected: "nginx",
		},
		{
			name:     "Image with registry and project",
			image:    "registry.example.com/project/nginx",
			expected: "nginx",
		},
		{
			name:     "Image with tag",
			image:    "nginx:latest",
			expected: "nginx:latest",
		},
		{
			name:     "Complex image path",
			image:    "harbor.example.com/library/project/app:v1.0.0",
			expected: "app:v1.0.0",
		},
		{
			name:     "Image with multiple slashes",
			image:    "registry.com/org/team/project/app",
			expected: "app",
		},
		{
			name:     "Empty string",
			image:    "",
			expected: "",
		},
		{
			name:     "Image with trailing slash",
			image:    "registry.com/nginx/",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetImageSimpleName(tt.image)
			if result != tt.expected {
				t.Errorf("GetImageSimpleName(%q) = %q, expected %q", tt.image, result, tt.expected)
			}
		})
	}
}

// Test round-trip conversion
func TestGitTagDockerTagRoundTrip(t *testing.T) {
	testTags := []string{
		"feature/new-feature",
		"release/v1.0.0/hotfix",
		"develop",
		"feature/user-auth/login",
		"hotfix/critical-bug",
	}

	for _, tag := range testTags {
		t.Run("RoundTrip_"+tag, func(t *testing.T) {
			dockerTag := GitTag2DockerTag(tag)
			gitTag := DockerTag2GitTag(dockerTag)
			
			if gitTag != tag {
				t.Errorf("Round trip failed: %q -> %q -> %q", tag, dockerTag, gitTag)
			}
		})
	}
}

// Benchmark tests
func BenchmarkGitTag2DockerTag(b *testing.B) {
	gitTag := "feature/user-management/authentication"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GitTag2DockerTag(gitTag)
	}
}

func BenchmarkDockerTag2GitTag(b *testing.B) {
	dockerTag := "feature---user-management---authentication"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		DockerTag2GitTag(dockerTag)
	}
}

func BenchmarkGetDockerImageTag(b *testing.B) {
	image := "harbor.example.com/project/app:feature---branch"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetDockerImageTag(image)
	}
}

func BenchmarkGetImageSimpleName(b *testing.B) {
	image := "harbor.example.com/library/project/app:v1.0.0"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetImageSimpleName(image)
	}
}
