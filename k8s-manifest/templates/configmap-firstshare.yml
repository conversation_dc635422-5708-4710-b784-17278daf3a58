apiVersion: v1
kind: ConfigMap
metadata:
  name: {{DEPLOY_APP_NAME}}-config
  namespace: {{DEPLOY_K8S_NAMESPACE}}
  annotations:
    fxiaoke.com/managed-by: gitlab-ci
data:
  config.json: |-
    {
      "app": {
        "runMode": "release",
        "httpPort": 80,
        "readTimeout": 7200,
        "writeTimeout": 7200,
        "cacheCategory": "redis",
        "runtimeDir": "runtime",
        "uploadDir": "runtime/upload",
        "downloadDir": "runtime/download",
        "kubeConfDir": "conf/kubeconf"
      },
      "jenkins": {
        "host": "https://jenkins2.firstshare.cn",
        "username": "FSSvcA053",
        "password": "w8KOC5Q007F&",
        "jobBuildImage": "k8s-app-image-build",
        "jobBuildPackageToImage": "k8s-app-package-build-to-image",
        "jobJacoco": "k8s-jacoco",
        "mavenImage": "reg.firstshare.cn/base/fs-maven3.9",
        "enableCRSF": true
      },
      "gitlab": {
        "host": "https://git.firstshare.cn",
        "token": "**************************"
      },
      "cms": {
        "webHost": "https://oss.firstshare.cn/cms",
        "apiHost": "https://oss.firstshare.cn/cs/api",
        "token": "k8s-manager-jnM2ShB5aE6BRBAtGSDc",
        "cmdbConfigPath": "firstshare/cmdb-mark-v2.json",
        "serviceConfigPath": "firstshare/k8s-service-list.csv"
      },
      "harbor": {
        "host": "reg.firstshare.cn",
        "appProject": "app",
        "helmChartProject": "chartrepo",
        "https": true,
        "username": "robot$k8s-app-manager",
        "password": "MrBNqpKtYAuBt8SvW8KLEmHeRTjSKx8S",
        "artifactBaseImage": "reg.firstshare.cn/base/fs-artifact-repo:v2.0",
        "artifactProject": "artifact"
      },
      "postgres": {
        "host": "*************",
        "port": 5432,
        "user": "fs_pgdb_a_u_k8sapp",
        "password": "d7QanjikTW09J2BOrebS",
        "database": "fs_k8s_app_manager"
      },
      "cas": {
        "loginPath": "http://oss.firstshare.cn/cas/login",
        "logoutPath": "http://oss.firstshare.cn/cas/logout",
        "validatePath": "http://oss.firstshare.cn/cas/p3/serviceValidate"
      },
      "qiXin": {
        "host": "",
        "publishAppId": "publish",
        "importantAlertAppId": "FSAID_131809b",
        "receiverEI": [
          5548
        ]
      },
      "sms": {
        "host": ""
      },
      "fsPaas": {
        "host": "",
        "objectId": "object_op_deployRec__c",
        "enterpriseId": 1,
        "sysUserId": -10000,
        "qiXinSessionFieldId": "5e845a05da7f1c0001ae82ac"
      },
      "k8sAppManager": {
        "host": "https://k8s-app.firstshare.cn"
      },
      "war": {
        "host": "https://war.firstshare.cn",
        "username": "jviCkGFDrOszjKri",
        "password": "ECvpVYQ56cm1MivKVJSPkA"
      },
      "eolinker": {
        "enable": false,
        "host": "https://eolinker.foneshare.cn",
        "eoSecretKey": "mcb1MkL1821ccf52f63fe1ec701f30c14329a54a2a85ab0",
        "spaceIdDefault": "Ns7aIIHd8ab5307649b503da006ccd3a1ec58709b7090a2",
        "reportHost": ""
      },
      "es": {
        "hosts": [
          "http://*************:9200",
          "http://*************:9200"
        ],
        "index": "k8s-app-pipeline",
        "username": "elastic",
        "password": "x1PwhDdvZkpOQBIO"
      },
      "redis": {
        "addr": "{{DEPLOY_APP_NAME}}-redis:6379",
        "password": "",
        "db": 0
      },
      "kafka": {
        "addr": [
          "**************:9092",
          "**************:9092",
          "**************:9092"
        ]
      },
      "clickhouse": {
        "addr": [
          "*************:9000",
          "*************:9000",
          "*************:9000"
        ],
        "user": "",
        "password": ""
      },
      "openApiTokens": [
        "function__B2rvuWCLtQSOUEao"
      ]
    }
  settings.json: |-
    {
      "maintain": {
        "ci": {
          "open": false,
          "desc": "容器镜像库维护，预计恢复时间：2025-05-22 13:00"
        },
        "cd": {
          "open": false,
          "desc": "系统维护"
        }
      },
      "timeWindow": {
        "open": true,
        "excludeNamespaces": [
          "jacoco"
        ]
      },
      "envConfirmText": "",
      "maxSurgeForceFull": true,
      "eolinkerTestDefault": true,
      "ingressReloadAllowInDay": true,
      "helmChart": {
        "versionPrefix": "9.40."
      },
      "mavenImages": [
        "reg.firstshare.cn/base/fs-maven3.9:openjdk8",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk11",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk17",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk21",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk23",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk24",
        "reg.firstshare.cn/base/fs-maven3.9:dragonwell8",
        "reg.firstshare.cn/base/fs-maven3.9:dragonwell11",
        "reg.firstshare.cn/base/fs-maven3.9:dragonwell17"
      ],
      "clusters": [    
        {
          "name": "k8s1",
          "description": "纷享云",
          "version": "1.29",
          "nodeVIP": "************",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress2.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "autoScale": false,
          "cronScale": true,
          "showNodeVipAddr": true,
          "showIngressAddr": true,
          "scaleMaxReplicas": 10,
          "cloudCategory": "fxiaokeCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "",
          "imagePullPolicy": "Always",
          "labels": [],
          "namespaces": [
            "fstest",
            "fstest01",
            "fstest02",
            "fstest03",
            "fstest04",
            "fstest-gray",
            "fstest-stage",
            "fstest-urgent",
            "fstest-vip",
            "fstest-metadata",
            "firstshare",
            "firstshare-gray",
            "sandbox",
            "jacoco"
          ],
          "baseImages": [
            "reg.firstshare.cn/base/fs-tomcat8:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8:openjdk11",
            "reg.firstshare.cn/base/fs-tomcat8:openjdk17",
            "reg.firstshare.cn/base/fs-tomcat8:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat8:openjdk24",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk11",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk17",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk23",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk24",
            "reg.firstshare.cn/base/fs-tomcat10:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat10:openjdk23",
            "reg.firstshare.cn/base/fs-tomcat10:openjdk24",
            "reg.firstshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.firstshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.firstshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.firstshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.firstshare.cn/base/fs-tomcat8-pdf2html:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-pdf2html:openjdk17",
            "reg.firstshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.firstshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.firstshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk17-agent",
            "reg.firstshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.firstshare.cn/base/fs-web-shell:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8:ali-dragonwell8",
            "reg.firstshare.cn/base/fs-tong-web7:openjdk8",
            "reg.firstshare.cn/base/fs-tong-web7:openjdk21",
            "reg.firstshare.cn/base/fs-tong-web7:ali-dragonwell8",
            "reg.firstshare.cn/base/fs-tong-web7-python3:openjdk8",
            "reg.firstshare.cn/base/fs-tong-web7-function:dragonwell8",
            "reg.firstshare.cn/base/fs-tong-web7-wkhtmltopdf:openjdk8",
            "reg.firstshare.cn/base/fs-tong-web7-beefly1.0.7:openjdk8",
            "reg.firstshare.cn/base/fs-tong-web7-doc-converter:openjdk21",
            "reg.firstshare.cn/base/fs-tong-web7-ffmpeg3.3.4:openjdk8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            },
            {
              "name": "GPU",
              "value": "GPU",
              "remark": ""
            },
            {
              "name": "坏孩子",
              "value": "BadBoy",
              "remark": ""
            },
            {
              "name": "外网访问权限专用",
              "value": "EgressNode",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 4
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": "https://skywalking.firstshare.cn"
          },
          "prometheusMonitor": {
            "enable": true,
            "prometheus": "eye2",
            "metricsInterval": "30s",
            "scrapeTimeout": "5s",
            "metricsPath": "/actuator/prometheus"
          },
          "thirdServices": {
            "oomReportUrl": "https://k8s-app.firstshare.cn/api/pod/event/report",
            "dubboHelperUrl": "http://fs-dubbo-helper.firstshare/fs-dubbo-helper",
            "nacosHelperUrl": "http://fs-envoy-server.firstshare",
            "nacosServerUrl": "nacos://apiuser:1E3E40FABB53EDF1E9738EA798FAA3EAB7B19953AE3654AD@*************:8848,*************:8848,*************:8848",
            "prometheusHost": "https://prometheus.firstshare.cn",
            "grafana": {
              "host": "https://grafana.firstshare.cn",
              "prometheusDS": "prometheus",
              "clickHouseDS": "firstshare-clickHouse"
            },
            "clickVisual": {
              "host": "https://log.firstshare.cn"
            },
            "logConfHost": "https://oss.firstshare.cn/logconf"
          }
        }
      ],
      "thirdServices": {
        "webShellHost": "https://oss.firstshare.cn/fs-k8s-web-shell",
        "devopsEventUrl": "https://grafana.firstshare.cn/d/a10c8008-2deb-4ea5-863b-7c9d5c1c1f14/e4ba8b-e4bbb6-e4b8ad-e5bf83"
      },
      "appSuffix": {
        "jacoco": "-jacoco",
        "sandbox": "-sandbox",
        "fstest-gray": "-gray",
        "fstest-stage": "-stage",
        "fstest-urgent": "-urgent",
        "fstest-vip": "-vip"
      },
      "deployStrategies": [
        {
          "name": "滚动",
          "value": "ROLL_UPDATE"
        },
        {
          "name": "重建",
          "value": "RECREATE"
        }
      ],
      "parentPoms": [
        {
          "name": "测试版 (Alpha)",
          "value": "fxiaoke-parent-pom-alpha",
          "desc": "包含需要进行测试的依赖包，只在线下环境或线上Jacoco可用",
          "enable": true
        },
        {
          "name": "候选版 (RC)",
          "value": "fxiaoke-parent-pom-rc",
          "desc": "通过了线下环境测试或者一些紧急Bug修复的依赖包版本",
          "enable": true
        },
        {
          "name": "稳定版 (Release)",
          "value": "fxiaoke-parent-pom-release",
          "desc": "在候选版通过充分测试后，需要进入到全网的依赖包版本",
          "enable": true
        }
      ],
      "oncallScaleUp": {
        "appList": [
          "*/*/fs-k8s-tomcat-test",
          "*/*/checkins-office-v2-server",
          "*/*/data-auth-service",
          "*/*/fast-notifier",
          "*/*/fs-apibus-global",
          "*/*/fs-apibus-ncrm",
          "*/*/fs-apibus-paas",
          "*/*/fs-appserver-checkins-v2",
          "*/*/fs-bi-crm-report-web",
          "*/*/fs-bi-sqlengine",
          "*/*/fs-bi-stat",
          "*/*/fs-bi-udf-report",
          "*/*/fs-bi-uitype",
          "*/*/fs-bpm",
          "*/*/fs-bpm-after-action",
          "*/*/fs-checkins-biz",
          "*/*/fs-crm",
          "*/*/fs-crm-fmcg-service",
          "*/*/fs-crm-fmcg-wq",
          "*/*/fs-crm-import",
          "*/*/fs-crm-import-sfa",
          "*/*/fs-crm-manufacturing",
          "*/*/fs-crm-sfa",
          "*/*/fs-crm-workflow",
          "*/*/fs-erp-sync-data",
          "*/*/fs-erp-sync-data-web",
          "*/*/fs-feeds-biz",
          "*/*/fs-feeds-provider",
          "*/*/fs-flow",
          "*/*/fs-fmcg-customized-excel",
          "*/*/fs-fmcg-sales-cgi",
          "*/*/fs-fmcg-service",
          "*/*/fs-metadata-option",
          "*/*/fs-metadata-rest",
          "*/*/fs-open-app-center-provider",
          "*/*/fs-organization-adapter",
          "*/*/fs-organization-biz",
          "*/*/fs-organization-provider",
          "*/*/fs-organization-provider-4data-auth",
          "*/*/fs-paas-app-udobj",
          "*/*/fs-paas-app-udobj-rest",
          "*/*/fs-paas-app-udobj-rest4flow",
          "*/*/fs-paas-app-udobj-rest4realtime",
          "*/*/fs-paas-auth-provider",
          "*/*/fs-paas-bizconf-web",
          "*/*/fs-paas-function-service-debug",
          "*/*/fs-paas-function-service-runtime",
          "*/*/fs-paas-function-service-runtime-provider",
          "*/*/fs-paas-org",
          "*/*/fs-paas-rule",
          "*/*/fs-paas-workflow",
          "*/*/fs-paas-workflow-processor",
          "*/*/fs-plat-auth-biz",
          "*/*/fs-plat-org-management",
          "*/*/fs-plat-service-biz",
          "*/*/fs-plat-service-provider",
          "*/*/fs-scheduler-task-provider",
          "*/*/fs-social-feeds",
          "*/*/fs-stage-propeller",
          "*/*/fs-stone-dataserver",
          "*/*/fs-stone-fileserver",
          "*/*/fs-stone-metaserver",
          "*/*/fs-stone-proxy",
          "*/*/fs-sync-data-all",
          "*/*/fs-user-extension-biz",
          "*/*/fs-webpage-customer-provider"
        ]
      },
      "baseImageGray":  {
        "imageTagSuffix": "-v250527",
        "appList": [
           "*/*/*"
        ]
      },
      "pipelineDefault": {
        "status": "audit",
        "cluster": "k8s1",
        "namespace": "fstest",
        "baseImage": "reg.firstshare.cn/base/fs-tomcat8:openjdk8",
        "replicas": 1,
        "deployStrategy": "ROLL_UPDATE",
        "resources": {
          "requestCPU": 0.2,
          "requestMemory": 256,
          "limitCPU": 0.4,
          "limitMemory": 512
        },
        "livenessProbe": {
          "enable": true,
          "initialDelaySeconds": 1800,
          "periodSeconds": 30,
          "timeoutSeconds": 5,
          "failureThreshold": 4,
          "successThreshold": 1
        },
        "readinessProbe": {
          "enable": true,
          "initialDelaySeconds": 10,
          "periodSeconds": 10,
          "timeoutSeconds": 5,
          "failureThreshold": 3,
          "successThreshold": 1
        },
        "startupProbe": {
          "enable": true,
          "initialDelaySeconds": 10,
          "periodSeconds": 10,
          "timeoutSeconds": 5,
          "failureThreshold": 180,
          "successThreshold": 1
        },
        "schedule": {
          "strategy": "PREFERRED",
          "node": ""
        },
        "pvc": {
          "enable": false,
          "name": "",
          "mountPath": ""
        },
        "envs": [
          {
            "name": "K8S_APP_NAME",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "K8S_PROCESS_NAME",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "ENVIRONMENT_TYPE",
            "value": "firstshare",
            "type": "SYSTEM"
          },
          {
            "name": "MACHINE_TYPE",
            "value": "DOCKER",
            "type": "SYSTEM"
          },
          {
            "name": "CMS_ENV_TYPE",
            "value": "firstshare",
            "type": "SYSTEM"
          },
          {
            "name": "CATALINA_OPTS",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "JAVA_OPTS",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          }
        ],
        "ports": [
          {
            "name": "http",
            "value": 80,
            "type": "SYSTEM"
          }
        ],
        "eolinkerIDs": [],
        "webhook": {
          "url": ""
        },
        "options": {
          "isCoreApp": false,
          "onlyDeployTag": false,
          "addSysctlKeepalive": false,
          "skyWalkingAgent": false,
          "appLogToKafka": true,
          "buildUseRuntimeJDK": false,
          "jvmGcLog": true
        },
        "partnerApps": [],
        "exclusiveApps": [],
        "preStopWebhook": "",
        "preStopRetainSeconds": 20,
        "remark": ""
      },
      "checkVersionOfDedicatedCloud": []
    }
  k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJS3pTdHdzaVVxdHd3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TkRBNU1UUXhNak15TWpoYUZ3MHpOREE1TVRJeE1qTTNNamhhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUN6OUtlTC9DY005SjZEU2hiZUZBb0xFZjN3YXdpOVlmMGpCZHVlV1YzSHJ3UmRObjNvMWdBL1RGMG4KMmcwYUdYckhuY0JSQlY2MHVEWGpvY0p3d0padHF5c3BXa2hEaGtFRzZ5UkpWd3ZtZ214dFgxa0k1M1BBa0hscwo5Q29EZ3hvaUVFQVZHQTU5SHJYZFlmVnFjUzN2L0x6WHdaYjQ2SWhLbXc2OVlBaGhmNFNJYlZNQXlYcTE1NFB0CmNQSnZpQllGNFM0VW9BbzFzdEZGdzJSWjJ4S1NJYi9VOFZtNjJzWGg1bjV5Qk9wVHAycjhZR2dkRVJ2N3hwZzYKY0d3aG5rUXJXN3BpM1hBTENWc2czMkZRZ1ZFZk02ZmpBR0lTMjJxMzkzV2pGcWNFOUt3WmxzNktvUlRjaUhyZwpyY3Ruazk4cUYzVVNNU25wa0hmcjlISHZiNm5SQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJRblR0OVdWSlorc0hWN1o2UG1OV1I4ckcxYWxUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQUd1dENncjExUQpxblpMQldGYjd5ZEdseEkwaEsvcDVmeHgxSi9FUFBWbkZOek1TcEhHdFpwSDFBcFQ3S0JBUGZvdVJkYXg1bDlUCi92Q3pxNFRmK0J6U3huRzBSSGxYcW9SajNpRFIreGp1dWVQdGJ4VlQxQ0VDS1pJTlhPRHlpUUNhSk9Ock9ac1AKOGxKdEd6SnRqSitJQmFLV2RCS3ZXOXlITlh6V2Y3bmk5UFV5WnZmclhxSC84ak50c2xudGVGYjMvZHZYU3JlbAprYWhjTmVxeXE2c2d3VFRRbmdjQTlHc2paSXRGU1hLS1VEZ0xmU2JlSmswOG5kS0pqTU4xVmkvR05JYmw1RUl4CjVncm5Wd2thaDArbmZNb1pmWVA1LzhSQWpKZUpDa0gyWFpoSCtFTWRvaDdaanNKYWRJTG44Yk9sMTR5Ri9JQ20KZE5Ob1dzVWlTVFVBCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://firstshare-k8s1.firstshare.cn:6443
    contexts:
      - name: fs-k8s-app-manager@k8s1
        context:
          cluster: k8s1
          namespace: default
          user: fs-k8s-app-manager@k8s1@user
    users:
      - name: fs-k8s-app-manager@k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@k8s1
  k8s2-arm: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: arm64-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJQUo0NUVRcmp4dmN3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TkRBNU1qVXdOREl3TlRWYUZ3MHpOREE1TWpNd05ESTFOVFZhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUM0RmhCdm5uM0dxa1ZtUW1QbmtHK0ZxUUZraHVtVW5EcHZoYUxxSEtoaW5XT2NoOGxIcVUvcm9qdDkKRlFoSDRsUkFvZk1nZXRKNlVETThjdnByY01DcjJ6Qi9rUzk1Z01ackF2QndRUjBGQkhOTi9wRkx6cWc5b0k5Tgp2aG53T0ZZUHd6aXlLU3UxNTdlR01iT1VxN2d4OGN4ZStXNUQrL3p5YTM0N1dldnFPdjV1dW1ldWhyenVYaVB5CmFJNVUwTWV3ZVY3R2hSWkFVanBsRG1JSmZhaHBIaU14UmwvL3lPMGorTXNvb2xPSlJhNSt1RXJ6VkdnSEJETi8Ka0dmNmFqaVV0QWRKc0dLbFNNdVYvUmt1SG5CT0pkb0ZQQjRDZVp6UDdSVkdmVzZ1Y01UM3FRd2FuaS9RYVJVVQpFY29pbzJnWmNKUWUyb2VCS3NJMENDVWVNOGF2QWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJSVHMxOGg2N2x0WEZIY3JDOC9QcGdFNndhSnREQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQmRIdTFHa0ZaVAp1bmlPNitTT3IzWlljTjZlZ0V4SEVnVUNvUlNkbmdqdk1Xclk1QkVKMHhleGZKSGx1b1dPMkRLb2hlM01mNkxhCnNmS2lDT0Zjd2FUMDFhekJGWTBuOHdHdHBGVFNhNFB1QkZIdG9EcTZ1MUNTYnBzSFJvVCtXZTdmaUlLVEdsaUUKZHcrTG4zaEYyL3JGL0llSHlZYXFZYU9FeitWaTZuZldSY0VEeERGc2tmRGVtbEZLTVVZN05hQTVlWWM5L3B4cgpZS3FoQkNjdmFjVVV4THo2ZlFtY1lveVVzUFFjOHp2eTlWbjFKV0syUFgrd1UyeTZtejVtV2laN05oM2VwamNLCjhLbk82ZStjRG5IcVhyZ0grTytuT0xXVVVVcEZzUkVEZ3F4ZnhrOTh2SG91QTlFZ1FLbVZpTE9VZnJKbnFzUDIKbXRjSkYweXFRdzB6Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://arm64-k8s1.firstshare.cn:6443
    contexts:
      - name: fs-k8s-app-manager@arm64-k8s1
        context:
          cluster: arm64-k8s1
          namespace: default
          user: fs-k8s-app-manager@arm64-k8s1@user
    users:
      - name: fs-k8s-app-manager@arm64-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@arm64-k8s1