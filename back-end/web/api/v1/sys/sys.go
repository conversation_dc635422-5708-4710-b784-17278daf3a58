package sys

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/util/exec"
	file2 "fs-k8s-app-manager/pkg/util/file"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"os"
	"path/filepath"
	"strconv"
)

func SysLog(c *gin.Context) {
	tailLines := c.Default<PERSON>y("tailLines", "2000")
	lines, _ := strconv.Atoi(tailLines)
	logPath := filepath.Join(config.Conf.App.RuntimeDir, "logs", "app.log")
	logPath = file2.AbsPath(logPath)
	_, err := os.Stat(logPath)
	if err != nil {
		message := "can't find log file, err: " + err.Error()
		web.FailJson(c, web.CODE_SERVER_ERROR, message)
		return
	}
	command := fmt.Sprintf("tail -n %d %s | tac", lines, logPath)
	output, err := exec.CommandExec(command)
	if err != nil {
		message := "read log from file fail, err: " + err.Error()
		web.FailJson(c, web.CODE_SERVER_ERROR, message)
		return
	}
	web.SuccessJson(c, output)
}
func Settings(c *gin.Context) {
	//从conf下获取相关配置返回给前端
	web.SuccessJson(c, settings.GetSetting())
}

func ReloadConfigMap(c *gin.Context) {
	err := config.Reload()
	if err != nil {
		web.FailJsonWithFailStatus(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	err = settings.Reload()
	if err != nil {
		web.FailJsonWithFailStatus(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.CreateBySys("ConfigMap-Reload", "ConfigMap Reload Success", nil)
	web.SuccessJson(c, nil)
}
func SettingsEdit(c *gin.Context) {
	if true {
		web.FailJson(c, web.CODE_SERVER_ERROR, "该功能已弃用，请直接去修改configmap文件，系统会自动动态加载配置")
		return
	}
	//user, _ := auth.GetUser(c)
	//if !perm_service.IsAdmin(user) {
	//	web.FailJson(c, web.CODE_SERVER_ERROR, "只有系统管理员才能执行当前操作")
	//	return
	//}
	//jsonData, err := io.ReadAll(c.Request.Body)
	//log_service.Create(user.RealName, "系统配置-修改", "SysSettings", string(jsonData))
	//if err != nil {
	//	web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
	//	return
	//}
	//if err := settings.UpdateSetting(string(jsonData)); err != nil {
	//	web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
	//	return
	//}
	//web.SuccessJson(c, nil)
}

func CacheClear(c *gin.Context) {
	cache.ClearAll()
	web.SuccessJson(c, nil)
}
