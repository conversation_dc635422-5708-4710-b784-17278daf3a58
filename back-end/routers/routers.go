package routers

import (
	"fs-k8s-app-manager/docs"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/app"
	"fs-k8s-app-manager/web/api/v1/artifact"
	"fs-k8s-app-manager/web/api/v1/cas"
	"fs-k8s-app-manager/web/api/v1/dashboard"
	"fs-k8s-app-manager/web/api/v1/eolinker"
	"fs-k8s-app-manager/web/api/v1/event"
	"fs-k8s-app-manager/web/api/v1/gitlab"
	"fs-k8s-app-manager/web/api/v1/grafana"
	"fs-k8s-app-manager/web/api/v1/image"
	"fs-k8s-app-manager/web/api/v1/job"
	"fs-k8s-app-manager/web/api/v1/k8s/cluster"
	"fs-k8s-app-manager/web/api/v1/k8s/deployment"
	k8s_event "fs-k8s-app-manager/web/api/v1/k8s/event"
	"fs-k8s-app-manager/web/api/v1/k8s/pod"
	"fs-k8s-app-manager/web/api/v1/log"
	"fs-k8s-app-manager/web/api/v1/notify"
	"fs-k8s-app-manager/web/api/v1/operation"
	"fs-k8s-app-manager/web/api/v1/org"
	"fs-k8s-app-manager/web/api/v1/page"
	"fs-k8s-app-manager/web/api/v1/pipeline"
	podautoscaler "fs-k8s-app-manager/web/api/v1/podscalescaler"
	"fs-k8s-app-manager/web/api/v1/reboot"
	"fs-k8s-app-manager/web/api/v1/scale"
	"fs-k8s-app-manager/web/api/v1/sys"
	"fs-k8s-app-manager/web/api/v1/temp_auth"
	"fs-k8s-app-manager/web/api/v1/tool"
	"fs-k8s-app-manager/web/api/v1/tool/deploy_app"
	"fs-k8s-app-manager/web/api/v1/tool/restart_app"
	"fs-k8s-app-manager/web/api/v1/user"
	"fs-k8s-app-manager/web/openapi"
	"net/http"

	"github.com/gin-contrib/gzip"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"github.com/gin-gonic/gin"
)

// InitRouter initialize routing information
func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(gzip.Gzip(gzip.DefaultCompression))
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	docs.SwaggerInfo.BasePath = "/api"
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	r.StaticFS("/static", http.Dir("templates/static"))
	r.StaticFS("/images", http.Dir("images"))
	r.StaticFile("favicon.ico", "templates/kubernetes-logo.png")

	r.LoadHTMLFiles("templates/index.html")

	r.GET("/", web.Index)

	api := r.Group("/api")
	api.Use(auth.BuildAuthRequired(
		"/api/shiro-cas/",
		"/api/shiro-cas/easter-egg",
		"/api/shiro-cas/redirect-path",
		"/api/shiro-cas/redirect-path",
		"/api/logout",
		"/api/setting", //获取系统配置，不能有授权
		"/api/operation/port/dump",
		"/api/operation/log/clear",
		"/api/operation/app-version-history/dump",
		"/api/operation/monitor/log/clear",
		"/api/operation/harbor/gc",
		"/api/operation/war/gc",
		"/api/operation/pipeline/all",
		"/api/operation/app/update",
		"/api/operation/tomcat/traffic/count",
		"/api/page/redirect",
		"/api/jacoco/pod-name",
		"/api/jacoco/reset",
		"/api/jacoco/list/jars",
		"/api/jacoco/report/task",
		"/api/jacoco/dump/download",
		"/api/jacoco/coverage/reset",
		"/api/jacoco/coverage/download",
		"/api/k8s/app/scale-up",
		"/api/k8s/app/node-drain",
		"/api/k8s/app/thread-dump",
		"/api/openapi/app/module/version",
		"/api/openapi/app/git-project/version",
		"/api/openapi/app/server/detail",
		"/api/openapi/app/restart",
		"/api/pod/event/report",
		"/api/pod/shell",
		"/api/pod/boot-time/report",
		"/api/configmap/reload",
		"/api/v1/tool/find-app-by-address",
		"/api/v1/app/git-modules",
		"/api/v1/tool/find-pod-by-ip",
		"/api/v1/tool/check-version",
		"/api/v1/grafana/alert",
		"/api/v1/grafana/phone-call-test",
		"/api/v1/notify/qixin",
		"/api/v1/dashboard/pod-draft",
		"/api/v1/k8s/pod/stdout",
	))
	{
		//用于接收上报的pod事件（比如业务pod的java进程发生oom时会上报JvmOOM事件），不能有授权
		api.POST("/pod/event/report", pod.EventReport)
		api.GET("/pod/shell", pod.WebShell)
		api.POST("/pod/boot-time/report", pod.BootTimeReport)

		api.GET("/shiro-cas/", cas.ShiroCas)
		api.GET("/shiro-cas/easter-egg", cas.EasterEgg)
		api.POST("/shiro-cas/redirect-path", cas.AddRedirectPath)
		api.GET("/logout", cas.Logout)

		api.GET("/setting", sys.Settings)

		api.POST("/operation/port/dump", operation.ServicePortDump)
		api.POST("/operation/log/clear", operation.LogClear)
		api.POST("/operation/monitor/log/clear", operation.MonitorLogClear)

		api.POST("/operation/harbor/gc", operation.HarborGC)
		api.POST("/operation/harbor/app-image-gc", operation.HarborAppImageGC)
		api.POST("/operation/war/gc", operation.WarGC)
		api.POST("/operation/pipeline/all", operation.AllPipelines)
		api.POST("/operation/app/update", operation.AppUpdate)
		api.POST("/operation/pipeline/update", operation.PipelineUpdate)
		api.POST("/operation/tomcat/traffic/count", operation.TomcatTrafficCount)
		api.POST("/operation/auto-scale-fix", operation.AutoScaleFix)

		api.GET("/operation/app-version-history", operation.SearchAppVersionHistory)
		api.POST("/operation/app-version-history/dump", operation.DumpAppVersionHistory)
		api.GET("/operation/cmdb-owner", operation.CmdbOwner)

		api.GET("/page/redirect", page.Redirect)

		api.GET("/jacoco/pod-name", openapi.PodName)
		api.POST("/jacoco/coverage/reset", openapi.ResetCoverage)
		api.POST("/jacoco/reset", openapi.Reset)
		api.POST("/jacoco/coverage/download", openapi.DownloadCoverage)
		api.POST("/jacoco/report/task", openapi.ReportTask)
		api.GET("/jacoco/dump/download", openapi.DumpDownload)
		api.GET("/jacoco/list/jars", openapi.ListJars)

		api.POST("/k8s/app/scale-up", openapi.ScaleUp)
		api.POST("/k8s/app/node-drain", openapi.NodeDrain)
		api.POST("/k8s/app/thread-dump", openapi.ThreadDump)
		api.POST("/configmap/reload", sys.ReloadConfigMap)

		api.GET("/openapi/app/module/version", openapi.AppModuleVersion)
		api.GET("/openapi/app/git-project/version", openapi.GitProjectVersion)
		api.GET("/openapi/app/server/detail", openapi.AppServerDetail)
		api.POST("/openapi/app/restart", openapi.AppRestart)
	}

	apiV1 := api.Group("/v1")
	{
		apiV1.GET("/user/info", user.GetInfo)
		apiV1.GET("/user/is-admin", user.IsAdmin)
		apiV1.GET("/user/list-by-real-name", user.ListByRealName)
		apiV1.GET("/user/names", user.AllNames)
		apiV1.PUT("/user/update-history-app", user.UpdateHistoryApp)
		//apiv1.GET("/page/redirect", page.Redirect)
		apiV1.POST("/gitlab/ci-file/create", gitlab.CreateGitlabCIFile)
		apiV1.GET("/gitlab/ci-file/download", gitlab.DownloadGitlabCIFile)
		apiV1.GET("/log", log.Search)
		apiV1.POST("/gitlab/tags", gitlab.GetGitTag)
	}

	sysApi := apiV1.Group("/sys")
	{
		sysApi.GET("/log", sys.SysLog)
		sysApi.GET("/setting", sys.Settings)
		sysApi.POST("/setting/edit", sys.SettingsEdit)
		sysApi.DELETE("/setting/cache", sys.CacheClear)
	}

	k8sApi := apiV1.Group("/k8s")
	{
		k8sApi.GET("/pod/list", pod.List)
		k8sApi.GET("/pod/deregister/list-by-app", pod.ListDeregisterByApp)
		k8sApi.GET("/pod/list-by-env", pod.ListByEnv)
		k8sApi.GET("/pod/metric", pod.Metric)
		k8sApi.GET("/pod/stdout", pod.StdoutLog)
		k8sApi.GET("/pod/stdout/download", pod.StdoutLogDownload)
		k8sApi.GET("/pod/detail", pod.Detail)
		k8sApi.DELETE("/pod/delete", pod.Delete)
		k8sApi.PUT("/pod/deregister", pod.Deregister)
		k8sApi.GET("/pod/deregister/list-by-cluster", pod.DeregisterListByCluster)
		k8sApi.PUT("/pod/version/retain", pod.VersionRetain)
		k8sApi.GET("/pod/file/list", pod.Files)
		k8sApi.POST("/pod/file/ready", pod.ReadyFile)
		k8sApi.POST("/pod/file/archive", pod.Archive)
		k8sApi.GET("/pod/file/download", pod.DownloadFile)
		k8sApi.GET("/pod/file/preview", pod.PreviewFile)
		k8sApi.POST("/pod/file/upload", pod.UploadFile)
		k8sApi.GET("/pod/cms/list", pod.ListCMSFile)
		k8sApi.GET("/pod/events", k8s_event.PodEvents)
		k8sApi.GET("/deployment/detail", deployment.Detail)
		k8sApi.GET("/deployment/detail/whole", deployment.DetailWhole)
		k8sApi.PUT("/deployment/redeploy", deployment.Redeploy)
		k8sApi.PUT("/deployment/use-recreate-deploy-strategy", deployment.UseRecreateDeployStrategy)
		k8sApi.PUT("/deployment/scale", deployment.Scale)
		k8sApi.PUT("/deployment/rollback", deployment.Rollback)
		k8sApi.GET("/deployment/replicaSet/list", deployment.ListAppReplicaSet)
		k8sApi.GET("/deployment/list", deployment.List)
		k8sApi.POST("/deployment/update-resource", deployment.UpdateResource)
		k8sApi.POST("/image/preheat", deployment.ImagePreheat)
		k8sApi.GET("/image/preheat/jobs", deployment.ImagePreheatJobs)
		k8sApi.DELETE("/image/preheat/jobs", deployment.DeleteImagePreheatJobs)

		k8sApi.GET("/scale/hpa", scale.ListHpa)
		k8sApi.POST("/scale/hpa", scale.CreateHpa)
		k8sApi.DELETE("/scale/hpa", scale.DeleteHpa)

		k8sApi.GET("/scale/cron", scale.ListCronScale)
		k8sApi.POST("/scale/cron", scale.CreateCronScale)
		k8sApi.DELETE("/scale/cron", scale.DeleteCronScale)

		k8sApi.GET("/scale/auto", scale.ListAutoScale)
		k8sApi.GET("/scale/all-by-app", scale.GetAllScaleByApp)
		k8sApi.POST("/scale/auto", scale.CreateAutoScale)
		k8sApi.DELETE("/scale/auto", scale.DeleteAutoScale)
		k8sApi.GET("/scale/log", scale.SearchScaleLog)
		k8sApi.GET("/scale/monitor/log", scale.SearchScaleMonitorLog)
		k8sApi.GET("/scale/podautoscaler", podautoscaler.Search)
		k8sApi.POST("/scale/podautoscaler", podautoscaler.Create)
		k8sApi.POST("/scale/podautoscaler/create-for-core-app", podautoscaler.CreateForCoreApp)
		k8sApi.DELETE("/scale/podautoscaler", podautoscaler.Delete)
		k8sApi.POST("/scale/podautoscaler/migrate", podautoscaler.Migrate)
		k8sApi.GET("/scale/podautoscaler/all-cluster-autoscaler-v2", podautoscaler.AllClustersAutoScalerV2)

		k8sApi.GET("/event/search", k8s_event.Search)
		k8sApi.GET("/cluster/capacity", cluster.Capacity)
		k8sApi.GET("/cluster/pod/capacity", cluster.PodCapacity)
	}

	appApi := apiV1.Group("/app")
	{
		appApi.GET("/search", app.List)
		appApi.GET("/group-by-namespace", app.GroupByNamespace)
		appApi.GET("/apps-with-env", app.AppsWithEnv)
		appApi.GET("/all", app.All)
		appApi.GET("/names", app.AllNames)
		appApi.GET("/detail", app.FindByName)
		appApi.DELETE("/", app.DeleteByName)
		appApi.POST("/", app.Create)
		appApi.POST("/create-health-review-in-crm", app.CreateAppHealthReviewInCRM)
		appApi.GET("/address", app.Address)
		appApi.PUT("", app.Edit)
		appApi.PUT("/permission", app.EditPermission)
		appApi.GET("/git-tag", app.GetGitTag)
		appApi.GET("/git-tag/find", app.FindTagByGitUrl)
		appApi.POST("/git-tag", app.CreateGitTag)
		appApi.POST("/create-bugfix-branch", app.CreateBugfixBranch)
		appApi.DELETE("/git-tag", app.BatchDeleteGitTagAndBranch)
		appApi.GET("/git-modules", app.GitModules)
	}

	artifactApi := apiV1.Group("/artifact")
	{
		artifactApi.GET("/all", artifact.All)
		artifactApi.GET("/search", artifact.Search)
		artifactApi.GET("/analysis", artifact.Analysis)
		artifactApi.DELETE("/", artifact.Delete)
		artifactApi.POST("/", artifact.Create)
	}
	pipelineApi := apiV1.Group("/pipeline")
	{
		pipelineApi.GET("/app/:app", pipeline.FindByApp)
		pipelineApi.GET("/find-by-env", pipeline.FindByEnv)
		pipelineApi.POST("/init", pipeline.InitNew)
		pipelineApi.GET("/status", pipeline.FindByStatus)
		pipelineApi.POST("/status", pipeline.UpdateStatus)
		pipelineApi.GET("/search", pipeline.Search)
		pipelineApi.POST("/search-by-properties", pipeline.SearchByProperties)
		pipelineApi.GET("/all", pipeline.All)
		pipelineApi.GET("/", pipeline.FindById)
		pipelineApi.POST("/", pipeline.CreateOrUpdate)
		//pipelineApi.POST("/migrate", pipeline.Migrate)
		pipelineApi.DELETE("/offline", pipeline.Offline)
		pipelineApi.POST("/sync", pipeline.Sync)
		pipelineApi.GET("/clone/by-namespace", pipeline.GetClonePipeline)
		pipelineApi.POST("/clone/by-namespace", pipeline.ClonePipelineByNamespace)
		pipelineApi.GET("/publish-dedicated-cloud", pipeline.GetDedicatedCloudPublishPipeline)
	}

	jobApi := apiV1.Group("/job")
	{
		jobApi.GET("/detail", job.FindById)
		jobApi.GET("/tasks", job.FindTasks)
		jobApi.PUT("/cancel", job.Cancel)
		jobApi.PUT("/update-status", job.ModifyJobStatus)
		jobApi.POST("/redo", job.Redo)
		jobApi.POST("/build-image", job.BuildImage)
		jobApi.POST("/deploy-app", job.DeployApp)
		jobApi.POST("/build-and-deploy", job.BuildAndDeploy)
		jobApi.POST("/deploy-app-with-current-version", job.DeployAppWithCurrVersion)
		jobApi.POST("/build-image-with-current-version", job.BuildImageWithCurrVersion)
		jobApi.POST("/search", job.Search)
		jobApi.GET("/image-options", job.ImageOptions)
	}
	toolApi := apiV1.Group("/tool")
	{
		toolApi.GET("/scan-jar", tool.ScanJar)
		toolApi.GET("/scan-tomcat-version", tool.ScanTomcatVersion)
		toolApi.GET("/find-app-by-address", tool.FindAppByAddress)
		toolApi.GET("/find-pod-by-ip", tool.FindPodByIP)
		toolApi.POST("/app-restart/create", restart_app.Create)
		toolApi.POST("/app-restart/start", restart_app.Start)
		toolApi.POST("/app-restart/stop", restart_app.Stop)
		toolApi.POST("/app-restart/remove", restart_app.Remove)
		toolApi.GET("/app-restart/output", restart_app.Output)
		toolApi.POST("/app-deploy/create", deploy_app.Create)
		toolApi.POST("/app-deploy/start", deploy_app.Start)
		toolApi.POST("/app-deploy/stop", deploy_app.Stop)
		toolApi.POST("/app-deploy/remove", deploy_app.Remove)
		toolApi.GET("/app-deploy/output", deploy_app.Output)
		toolApi.POST("/pipeline-batch-clone", tool.PipelineBatchClone)
		toolApi.POST("/pipeline-replica-query", tool.PipelineReplicaQuery)
		toolApi.POST("/pipeline-replica-update", tool.PipelineReplicaUpdate)
		toolApi.POST("/pipeline-batch-resource-update", tool.PipelineBatchResourceUpdate)
		toolApi.POST("/autoscale-batch-create", tool.AutoscaleBatchCreate)
		toolApi.POST("/pipeline-batch-update-status", tool.PipelineBatchUpdateStatus)
		toolApi.POST("/migrate-addr-query", tool.MigrateAddrQuery)
		toolApi.POST("/migrate-addr-query2", tool.MigrateAddrQuery2)
		toolApi.POST("/yaml-export", tool.YamlExport)
		toolApi.POST("/helm-chart-build", tool.BuildHelmChart)
		toolApi.POST("/migrate/pipeline-search", tool.SearchPipeline)
		toolApi.POST("/migrate/traffic-analysis", tool.TrafficAnalysis)
		toolApi.GET("/check-version", tool.CheckVersionOfDedicatedCloudAppApi)
		toolApi.POST("/app-version-snapshot", tool.AppVersionSnapshot)
		toolApi.GET("/all-app-owners", tool.AllAppOwners)
		toolApi.GET("/app-version-snapshot/search", tool.AppVersionSnapshotSearch)
		toolApi.GET("/app-version-snapshot/detail", tool.GetAppVersionSnapshotById)
		toolApi.DELETE("/app-version-snapshot/delete", tool.AppVersionSnapshotDelete)
		toolApi.GET("/load-cms-profile-configs", tool.LoadCmsProfileConfigs)
	}
	eolinkerApi := apiV1.Group("/eolinker")
	{
		eolinkerApi.GET("/project", eolinker.ListProjects)
		eolinkerApi.GET("/timed-task", eolinker.ListTimedTask)
	}
	notifyApi := apiV1.Group("/notify")
	{
		notifyApi.POST("/qixin", notify.SendQiXin)
	}
	tempAuthApi := apiV1.Group("/temp-auth")
	{
		tempAuthApi.POST("/", temp_auth.Create)
		tempAuthApi.DELETE("/", temp_auth.DeleteById)
		tempAuthApi.GET("/search", temp_auth.Search)
		tempAuthApi.GET("/admins", temp_auth.TempAuthAdmin)
		tempAuthApi.POST("/audit", temp_auth.Audit)
	}
	orgApi := apiV1.Group("/org")
	{
		orgApi.GET("/all", org.All)
		orgApi.POST("/", org.Create)
		orgApi.PUT("/", org.Edit)
	}
	grafanaApi := apiV1.Group("/grafana")
	{
		grafanaApi.POST("/alert", grafana.Alert)
		grafanaApi.POST("/phone-call-test", grafana.PhoneCallTest)
	}
	dashboardApi := apiV1.Group("/dashboard")
	{
		dashboardApi.GET("/data", dashboard.Data)
		dashboardApi.GET("/resource-pool", dashboard.ResourcePool)
		dashboardApi.GET("/pod-draft", dashboard.PodDrift)
		dashboardApi.GET("/app-deploy-status", dashboard.AppDeployStatus)
	}
	rebootApi := apiV1.Group("/reboot")
	{
		rebootApi.GET("/", reboot.List)
		rebootApi.POST("/", reboot.Create)
		rebootApi.DELETE("/", reboot.Delete)
	}
	eventApi := apiV1.Group("/event")
	{
		eventApi.GET("/query", event.Query)
		eventApi.GET("/query-by-app", event.QueryByApp)
	}
	imageApi := apiV1.Group("/image")
	{
		imageApi.GET("/search", image.Search)
	}
	return r
}
