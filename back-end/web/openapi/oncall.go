package openapi

import (
	"fmt"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/constant"
	k8s_util "fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// scaleUpLock 添加应用扩容锁
func scaleUpLock(cluster, namespace, app string, expire time.Duration) {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("scale-up-locker:%s/%s/%s", cluster, namespace, app))
	_ = cache.SetStruct(cacheKey, "true", expire)
}

// scaleUpIsLocked 检查应用扩容锁
func scaleUpIsLocked(cluster, namespace, app string) bool {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("scale-up-locker:%s/%s/%s", cluster, namespace, app))
	if _, found := cache.GetStruct(cacheKey, interface{}(nil)); found {
		return true
	}
	return false
}

// nodeDrainLock 添加节点驱逐锁
func nodeDrainLock(cluster, node string, expire time.Duration) {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("node-drain-locker:%s/%s", cluster, node))
	_ = cache.SetStruct(cacheKey, "true", expire)
}

// nodeDrainIsLocked 检查节点驱逐锁
func nodeDrainIsLocked(cluster, node string) bool {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("node-drain-locker:%s/%s", cluster, node))
	if _, found := cache.GetStruct(cacheKey, interface{}(nil)); found {
		return true
	}
	return false
}

// OncallWebhookParam
// @Description Oncall服务Webhook参数
type OncallWebhookParam struct {
	// 告警环境
	AlertEnv string `json:"alert_env" binding:"required" example:"k8s0"`
	// 告警等级
	AlertLevel string `json:"alert_level" binding:"-" example:"WARN"`
	// 告警来源
	AlertSource string `json:"alert_source" binding:"-" example:"GRAFANA"`
	// 告警状态
	AlertStatus string `json:"alert_status" binding:"required" example:"FIRING"`
	// 告警名称
	AlertName string `json:"alertname" binding:"required" example:"pod-cpu-throttled"`
	// 运行环境
	Namespace string `json:"namespace" binding:"required" example:"foneshare"`
	// 资源ID
	ResourceID string `json:"resource_id" binding:"required" example:"fs-k8s-tomcat-test-5d8874c9dd-6bg6q"`
	// 资源名称
	ResourceName string `json:"resource_name" binding:"required" example:"fs-k8s-tomcat-test"`
	// 资源类型
	ResourceType string `json:"resource_type" binding:"required" example:"app"`
}

// ScaleUp godoc
// @Summary 应用副本数扩容
// @Description 对应用的副本数进行扩容操作
// @Tags oncall
// @Accept json
// @Produce octet-stream
// @Param RequestBody	body		OncallWebhookParam	true "json格式的请求体"
// @Param dryrun	query		string	false	"是否试运行（只记录日志，不实际操作。可选值：true | false ）" default(false)
// @Param podDeregister	query		string	false	"是否摘除Pod（当告警资源为pod时有效。可选值：true | false ）" default(false)
// @Success 200 {object} web.Response "success"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /k8s/app/scale-up [post]
func ScaleUp(c *gin.Context) {
	var p OncallWebhookParam
	var response web.Response
	defer func(request *OncallWebhookParam, response *web.Response) {
		title := "OnCall-服务扩容"
		if strings.EqualFold(request.AlertStatus, "RESOLVED") {
			title = "OnCall-告警恢复"
		}

		target := fmt.Sprintf("%s/%s(%s)", p.ResourceName, p.Namespace, p.AlertStatus)
		log_service.Create(p.AlertName, title, target, map[string]interface{}{
			"request":  *request,
			"response": *response,
		})
	}(&p, &response)
	if err := c.ShouldBindBodyWith(&p, binding.JSON); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	if !strings.EqualFold(p.AlertStatus, "FIRING") {
		response = web.SuccessJson2(c, "skipped, because alert_status is not FIRING")
		return
	}

	if strings.EqualFold(p.Namespace, "jacoco") {
		response = web.SuccessJson2(c, "skipped, jacoco namespace ignore")
		return
	}

	if strings.EqualFold(c.DefaultQuery("dryrun", "false"), "true") {
		response = web.SuccessJson2(c, "skipped, dry run")
		return
	}

	cluster := p.AlertEnv
	namespace := p.Namespace
	app := p.ResourceName
	addReplica := int32(3)

	if pipe, err2 := pipeline_service.FirstInEnv(cluster, namespace, app); err2 != nil || pipe.Status != constant.PIPELINE_STATUS_ENABLED {
		//todo: debug日志，等cluster问题解决后删除
		log_service.Create(p.AlertName, "OnCall-服务扩容-Debug", app, p)
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, "skipped, pipeline is not enabled")
		return
	}

	if !settings.GetSetting().OncallScaleUp.AppContains(cluster, namespace, app) {
		response = web.SuccessJson2(c, "app is not in scale up white list, name: "+app)
		return
	}

	//如果告警资源是pod，且pod的创建时间小于15分钟，则跳过扩容
	isPodResource := false
	if pod, err2 := k8s_service.PodDetail(cluster, namespace, p.ResourceID); pod != nil && err2 == nil {
		isPodResource = true
		if time.Now().Unix()-pod.CreationTimestamp.Unix() < 900 {
			response = web.SuccessJson2(c, "skipped, pod is created less than 10 minutes")
			return
		}
	}
	if scaleUpIsLocked(cluster, namespace, app) {
		response = web.SuccessJson2(c, "skipped, scale up is locked")
		return
	}

	clu := settings.GetSetting().GetCluster(cluster)
	if clu == nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, "cluster is not exist, name: "+cluster)
		return
	}

	//if !clu.AutoScale {
	//	response = web.SuccessJson2(c, "skipped, auto scale is not open")
	//	return
	//}

	dep, err := k8s_service.GetDeploymentDTO(cluster, p.Namespace, app)
	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if dep.Replicas == 0 {
		//todo: debug日志，等cluster问题解决后删除
		log_service.Create(p.AlertName, "OnCall-服务扩容-Debug", app, p)
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, "skipped, current replicas is 0")
		return
	}

	if dep.Replicas >= clu.ScaleMaxReplicas {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("%s集群可允许扩容的最大副本数：%d, 当前运行副本数：%d",
			cluster, clu.ScaleMaxReplicas, dep.Replicas))
		return
	}
	newReplicas := dep.Replicas + addReplica

	if err := kubectl.Scale(cluster, namespace, app, newReplicas); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	//该日志给 scaleDownOncallScaledApps 定时任务使用。参考：jobs.go 下的 scaleDownOncallScaledApps
	log_service.Create(p.AlertName, "oncall-scale-up", fmt.Sprintf("%s/%s/%s", cluster, namespace, app), "")
	scaleUpLock(cluster, namespace, app, 20*time.Minute)
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      cluster,
		Profile:      namespace,
		Creator:      "告警自愈/" + p.AlertName,
		ResourceType: "app",
		ResourceId:   app,
		Title:        "服务自动扩容",
		Message:      fmt.Sprintf("触发告警：%s | 副本数：%d → %d", p.AlertName, dep.Replicas, newReplicas),
		Level:        "warn",
		Extra:        "",
	})
	title := fmt.Sprintf("服务扩容-(Alert: %s)", p.AlertName)
	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app),
		fmt.Sprintf("%s，副本数: %d → %d", title, dep.Replicas, newReplicas))

	podDeregister := strings.EqualFold(c.Query("podDeregister"), "true")
	if isPodResource && podDeregister {
		go func() {
			time.Sleep(2 * time.Minute)
			_ = kubectl.DeregisterService(cluster, namespace, p.ResourceID)
			_ = k8s_service.PodDeregister(cluster, namespace, p.ResourceID)
			//待改进： 记录摘除状态
			event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app), "pod摘除，pod名："+p.ResourceID)
			_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
				Cluster:      cluster,
				Profile:      namespace,
				Creator:      "告警自愈/" + p.AlertName,
				ResourceType: "app",
				ResourceId:   app,
				Title:        "pod摘除",
				Message:      fmt.Sprintf("触发告警：%s | 摘除pod：%s", p.AlertName, p.ResourceID),
				Level:        "warn",
				Extra:        "",
			})
		}()
	}

	response = web.SuccessJson2(c, "success")
}

// NodeDrain godoc
// @Summary 节点驱逐
// @Description 对节点下的pod进行驱逐操作，只会对服务等级为L2和L3的pod进行驱逐
// @Tags oncall
// @Accept json
// @Produce json
// @Param RequestBody	body		OncallWebhookParam	true "json格式的请求体"
// @Param dryrun	query		string	true	"是否试运行（只记录日志，不实际操作。可选值：true | false ）" default(false)
// @Success 200 {object} web.Response "success"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /k8s/app/node-drain [post]
func NodeDrain(c *gin.Context) {
	var p OncallWebhookParam
	var response web.Response
	dryRun := strings.EqualFold(c.DefaultQuery("dryrun", "false"), "true")
	remark := ""
	defer func(request *OncallWebhookParam, response *web.Response, dryRun *bool, remark *string) {
		title := "OnCall-节点驱逐"
		if strings.EqualFold(request.AlertStatus, "RESOLVED") {
			title = "OnCall-告警恢复"
		}

		target := fmt.Sprintf("%s/%s(%s)", p.ResourceName, p.Namespace, p.AlertStatus)
		log_service.Create(p.AlertName, title, target, map[string]interface{}{
			"request":  *request,
			"response": *response,
			"dryRun":   *dryRun,
			"remark":   *remark,
		})
	}(&p, &response, &dryRun, &remark)
	if err := c.ShouldBindBodyWith(&p, binding.JSON); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	if !strings.EqualFold(p.AlertStatus, "FIRING") {
		response = web.SuccessJson2(c, "skipped, because alert_status is not FIRING")
		return
	}
	if strings.EqualFold(p.Namespace, "jacoco") {
		response = web.SuccessJson2(c, "skipped, jacoco namespace ignore")
		return
	}

	node := p.ResourceID
	if strings.EqualFold(p.AlertEnv, "k8s1") {
		node = ipToVlnxName(node)
	}
	remark = fmt.Sprintf("ipToVlnxName：%s -> %s", p.ResourceID, node)

	podList, err := k8s.ListPodByNode(p.AlertEnv, node)
	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	drainPodList := make([]corev1.Pod, 0, 5)
	//只对L3和L2级别的pod进行驱逐，优先驱逐L3级别的pod。
	for _, serverLevel := range []string{"L3", "L2"} {
		for _, pod := range podList.Items {
			if pod.Annotations["fxiaoke.com/server-level"] == serverLevel {
				drainPodList = append(drainPodList, pod)
			}
		}
	}
	//最多驱逐3个pod
	if len(drainPodList) > 3 {
		drainPodList = drainPodList[:3]
	}

	if len(drainPodList) > 0 {
		if nodeDrainIsLocked(p.AlertEnv, node) {
			response = web.SuccessJson2(c, "skipped, node drain is locked")
			return
		}
		nodeDrainLock(p.AlertEnv, node, 10*time.Minute)
	}

	data := make([]map[string]interface{}, 0, len(drainPodList))
	for _, pod := range drainPodList {
		status := "skipped"
		if !dryRun {
			if err := k8s_service.PodDelete(p.AlertEnv, pod.Namespace, pod.Name); err != nil {
				status = "delete failed, err: " + err.Error()
			} else {
				status = "deleted"
				_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
					Cluster:      p.AlertEnv,
					Profile:      pod.Namespace,
					Creator:      "告警自愈/" + p.AlertName,
					ResourceType: "app",
					ResourceId:   p.ResourceID,
					Title:        "pod驱逐",
					Message:      fmt.Sprintf("触发告警：%s | pod：%s", p.AlertName, pod.Name),
					Level:        "warn",
					Extra:        "",
				})
			}
		}
		data = append(data, map[string]interface{}{
			"cluster":      p.AlertEnv,
			"namespace":    pod.Namespace,
			"node":         node,
			"pod":          pod.Name,
			"server_level": pod.Annotations["fxiaoke.com/server-level"],
			"status":       status,
		})
	}
	response = web.SuccessJson2(c, data)
}

// ThreadDump godoc
// @Summary dump pod下的Java线程
// @Description 对pod下的Java线程进行dump操作
// @Tags oncall
// @Accept json
// @Produce octet-stream
// @Param RequestBody	body		OncallWebhookParam	true "json格式的请求体"
// @Success 200 {object} web.Response "success"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /k8s/app/thread-dump [post]
func ThreadDump(c *gin.Context) {
	var response web.Response
	p := OncallWebhookParam{}

	defer func(request *OncallWebhookParam, response *web.Response) {
		title := "OnCall-线程Dump"
		if strings.EqualFold(p.AlertStatus, "RESOLVED") {
			title = "OnCall-告警恢复"
		}
		target := fmt.Sprintf("%s/%s(%s)", p.ResourceID, p.Namespace, p.AlertStatus)
		log_service.Create(p.AlertName, title, target, map[string]interface{}{
			"request":  *request,
			"response": *response,
		})
	}(&p, &response)

	if err := c.ShouldBindBodyWith(&p, binding.JSON); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	if !strings.EqualFold(p.AlertStatus, "FIRING") {
		response = web.SuccessJson2(c, "skipped, because alert_status is not FIRING")
		return
	}

	cluster := p.AlertEnv
	namespace := p.Namespace
	pod := p.ResourceID

	_, err := k8s_service.PodDetail(cluster, namespace, pod)

	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, fmt.Sprintf("pod is not exist, name: %s, err: %s", pod, err.Error()))
		return
	}

	dumpFile, err := k8s_service.ThreadDump(cluster, namespace, pod, false)
	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	go func() {
		_, _ = k8s_service.ThreadDump(cluster, namespace, pod, true)
		_, _ = k8s_service.ThreadDumpByGstack(cluster, namespace, pod)
		title := fmt.Sprintf("线程Dump-(Alert: %s)", p.AlertName)
		event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, k8s_util.GetAppName(pod)),
			fmt.Sprintf("%s，filepath: %s", title, dumpFile))
		//todo: 增加pod摘除能力

		_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
			Cluster:      cluster,
			Profile:      namespace,
			Creator:      "告警自愈/" + p.AlertName,
			ResourceType: "app",
			ResourceId:   pod,
			Title:        "线程Dump",
			Message:      fmt.Sprintf("触发告警：%s | pod：%s", p.AlertName, pod),
			Level:        "warn",
			Extra:        "",
		})
	}()
	response = web.SuccessJson2(c, "filepath: "+dumpFile)
}

func ipToVlnxName(ip string) string {
	if !strings.HasPrefix(ip, "172.17") {
		return ip
	}

	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return ip
	}

	third, err1 := strconv.Atoi(parts[2])
	fourth, err2 := strconv.Atoi(parts[3])
	if err1 != nil || err2 != nil {
		return ip
	}

	return fmt.Sprintf("vlnx%03d%03d", third, fourth)
}
