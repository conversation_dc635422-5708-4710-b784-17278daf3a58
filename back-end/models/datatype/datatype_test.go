package datatype

import (
	"testing"
)

func TestStrList_Basic(t *testing.T) {
	// Test StrList creation and basic operations
	strList := StrList{"item1", "item2", "item3"}
	
	if len(strList) != 3 {
		t.<PERSON><PERSON><PERSON>("Expected StrList length to be 3, got %d", len(strList))
	}
	
	if strList[0] != "item1" {
		t.<PERSON>("Expected first item to be 'item1', got %q", strList[0])
	}
	
	if strList[1] != "item2" {
		t.<PERSON><PERSON>("Expected second item to be 'item2', got %q", strList[1])
	}
	
	if strList[2] != "item3" {
		t.<PERSON><PERSON><PERSON>("Expected third item to be 'item3', got %q", strList[2])
	}
}

func TestStrList_Empty(t *testing.T) {
	// Test empty StrList
	emptyList := StrList{}
	
	if len(emptyList) != 0 {
		t.<PERSON>("Expected empty StrList length to be 0, got %d", len(emptyList))
	}
	
	// Test nil StrList
	var nilList StrList
	if len(nilList) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected nil StrList length to be 0, got %d", len(nilList))
	}
}

func TestStrList_Append(t *testing.T) {
	// Test appending to StrList
	strList := StrList{"item1", "item2"}
	
	strList = append(strList, "item3")
	if len(strList) != 3 {
		t.Errorf("Expected StrList length to be 3 after append, got %d", len(strList))
	}
	
	if strList[2] != "item3" {
		t.Errorf("Expected third item to be 'item3', got %q", strList[2])
	}
	
	// Test appending multiple items
	strList = append(strList, "item4", "item5")
	if len(strList) != 5 {
		t.Errorf("Expected StrList length to be 5 after multiple append, got %d", len(strList))
	}
}

func TestStrList_WithSpecialCharacters(t *testing.T) {
	// Test StrList with special characters and Unicode
	strList := StrList{
		"item-with-dashes",
		"item_with_underscores",
		"item with spaces",
		"item@with#special$chars%",
		"中文项目",
		"项目with混合characters",
		"emoji😀🎉",
	}
	
	if len(strList) != 7 {
		t.Errorf("Expected StrList length to be 7, got %d", len(strList))
	}
	
	// Test that all items are preserved correctly
	expectedItems := []string{
		"item-with-dashes",
		"item_with_underscores",
		"item with spaces",
		"item@with#special$chars%",
		"中文项目",
		"项目with混合characters",
		"emoji😀🎉",
	}
	
	for i, expected := range expectedItems {
		if strList[i] != expected {
			t.Errorf("Expected item at index %d to be %q, got %q", i, expected, strList[i])
		}
	}
}

func TestStrList_LargeList(t *testing.T) {
	// Test StrList with many items
	const numItems = 1000
	strList := make(StrList, numItems)
	
	for i := 0; i < numItems; i++ {
		strList[i] = "item" + string(rune(i))
	}
	
	if len(strList) != numItems {
		t.Errorf("Expected StrList length to be %d, got %d", numItems, len(strList))
	}
	
	// Test random access
	if strList[500] != "item"+string(rune(500)) {
		t.Errorf("Expected item at index 500 to be correct")
	}
}

func TestStrList_Iteration(t *testing.T) {
	// Test iterating over StrList
	strList := StrList{"apple", "banana", "cherry", "date"}
	
	count := 0
	for i, item := range strList {
		count++
		if i == 0 && item != "apple" {
			t.Errorf("Expected first item to be 'apple', got %q", item)
		}
		if i == 3 && item != "date" {
			t.Errorf("Expected last item to be 'date', got %q", item)
		}
	}
	
	if count != 4 {
		t.Errorf("Expected to iterate over 4 items, got %d", count)
	}
}

func TestStrList_Slicing(t *testing.T) {
	// Test slicing operations on StrList
	strList := StrList{"item1", "item2", "item3", "item4", "item5"}
	
	// Test slice from beginning
	slice1 := strList[:3]
	if len(slice1) != 3 {
		t.Errorf("Expected slice length to be 3, got %d", len(slice1))
	}
	if slice1[0] != "item1" || slice1[2] != "item3" {
		t.Errorf("Slice from beginning not correct")
	}
	
	// Test slice from middle
	slice2 := strList[1:4]
	if len(slice2) != 3 {
		t.Errorf("Expected slice length to be 3, got %d", len(slice2))
	}
	if slice2[0] != "item2" || slice2[2] != "item4" {
		t.Errorf("Slice from middle not correct")
	}
	
	// Test slice to end
	slice3 := strList[2:]
	if len(slice3) != 3 {
		t.Errorf("Expected slice length to be 3, got %d", len(slice3))
	}
	if slice3[0] != "item3" || slice3[2] != "item5" {
		t.Errorf("Slice to end not correct")
	}
}

func TestStrList_Copy(t *testing.T) {
	// Test copying StrList
	original := StrList{"item1", "item2", "item3"}
	
	// Test copy by assignment (shallow copy)
	copy1 := original
	if len(copy1) != len(original) {
		t.Errorf("Copy should have same length as original")
	}
	
	// Test that modifying copy affects original (since it's a slice)
	copy1[0] = "modified"
	if original[0] != "modified" {
		t.Errorf("Modifying copy should affect original (slice behavior)")
	}
	
	// Test deep copy using append
	original = StrList{"item1", "item2", "item3"}
	copy2 := append(StrList(nil), original...)
	copy2[0] = "modified"
	if original[0] == "modified" {
		t.Errorf("Deep copy should not affect original")
	}
}

func TestStrList_EdgeCases(t *testing.T) {
	// Test various edge cases
	
	// Test with empty strings
	strList := StrList{"", "item2", "", "item4", ""}
	if len(strList) != 5 {
		t.Errorf("Expected StrList with empty strings to have length 5, got %d", len(strList))
	}
	
	// Test with very long strings
	longString := ""
	for i := 0; i < 10000; i++ {
		longString += "a"
	}
	strList = StrList{longString, "short"}
	if len(strList) != 2 {
		t.Errorf("Expected StrList with long string to have length 2, got %d", len(strList))
	}
	if len(strList[0]) != 10000 {
		t.Errorf("Expected long string to be preserved")
	}
}

// Benchmark tests
func BenchmarkStrList_Creation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		strList := StrList{"item1", "item2", "item3", "item4", "item5"}
		_ = strList
	}
}

func BenchmarkStrList_Append(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		strList := StrList{}
		for j := 0; j < 100; j++ {
			strList = append(strList, "item")
		}
	}
}

func BenchmarkStrList_Access(b *testing.B) {
	strList := make(StrList, 1000)
	for i := 0; i < 1000; i++ {
		strList[i] = "item" + string(rune(i))
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = strList[i%1000]
	}
}

func BenchmarkStrList_Iteration(b *testing.B) {
	strList := make(StrList, 1000)
	for i := 0; i < 1000; i++ {
		strList[i] = "item" + string(rune(i))
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, item := range strList {
			_ = item
		}
	}
}
