package file

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

// Standalone tests that don't depend on global config

func TestGetExtStandalone(t *testing.T) {
	tests := []struct {
		name     string
		fileName string
		expected string
	}{
		{
			name:     "Simple file with extension",
			fileName: "test.txt",
			expected: ".txt",
		},
		{
			name:     "File with multiple dots",
			fileName: "test.backup.sql",
			expected: ".sql",
		},
		{
			name:     "File without extension",
			fileName: "README",
			expected: "",
		},
		{
			name:     "Hidden file with extension",
			fileName: ".gitignore",
			expected: "",
		},
		{
			name:     "Empty string",
			fileName: "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetExt(tt.fileName)
			if result != tt.expected {
				t.Errorf("GetExt(%q) = %q, expected %q", tt.fileName, result, tt.expected)
			}
		})
	}
}

func TestCheckNotExistStandalone(t *testing.T) {
	// Test with non-existent file
	nonExistentFile := "/tmp/non_existent_file_" + time.Now().Format("20060102150405")
	if !CheckNotExist(nonExistentFile) {
		t.Errorf("CheckNotExist should return true for non-existent file")
	}

	// Create a temporary file
	tempFile, err := os.CreateTemp("", "test_file_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	tempFile.Close()

	// Test with existing file
	if CheckNotExist(tempFile.Name()) {
		t.Errorf("CheckNotExist should return false for existing file")
	}
}

func TestWriteReadStandalone(t *testing.T) {
	// Create a temporary file
	tempFile, err := os.CreateTemp("", "test_write_read_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	tempFile.Close()

	// Test writing content
	testContent := "Hello, World!\nThis is a test."
	err = Write(tempFile.Name(), testContent)
	if err != nil {
		t.Errorf("Write failed: %v", err)
	}

	// Test reading content
	content, err := Read(tempFile.Name())
	if err != nil {
		t.Errorf("Read failed: %v", err)
	}

	if content != testContent {
		t.Errorf("Content doesn't match. Expected: %q, Got: %q", testContent, content)
	}
}

func TestGetSizeStandalone(t *testing.T) {
	// Create a temporary file with known content
	testContent := "Hello, World!"
	tempFile, err := os.CreateTemp("", "test_size_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())

	_, err = tempFile.WriteString(testContent)
	if err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	tempFile.Close()

	// Test getting file size
	size, err := GetSize(tempFile.Name())
	if err != nil {
		t.Errorf("GetSize failed: %v", err)
	}

	expectedSize := int64(len(testContent))
	if size != expectedSize {
		t.Errorf("GetSize returned wrong size. Expected: %d, Got: %d", expectedSize, size)
	}

	// Test with non-existent file
	_, err = GetSize("/tmp/non_existent_file_" + time.Now().Format("20060102150405"))
	if err == nil {
		t.Errorf("GetSize should fail for non-existent file")
	}
}

func TestAbsPathStandalone(t *testing.T) {
	// Test with relative path
	relativePath := "test/path"
	absPath := AbsPath(relativePath)
	
	if !filepath.IsAbs(absPath) {
		t.Errorf("AbsPath should return absolute path, got: %q", absPath)
	}

	// Test with empty string
	emptyPath := AbsPath("")
	if emptyPath == "" {
		t.Errorf("AbsPath should not return empty string")
	}
}

func TestIsNotExistMkDirStandalone(t *testing.T) {
	// Create a unique directory name
	testDir := filepath.Join(os.TempDir(), "test_mkdir_"+time.Now().Format("20060102150405"))
	defer os.RemoveAll(testDir)

	// Test creating non-existent directory
	err := IsNotExistMkDir(testDir)
	if err != nil {
		t.Errorf("IsNotExistMkDir failed: %v", err)
	}

	// Check if directory was created
	if _, err := os.Stat(testDir); os.IsNotExist(err) {
		t.Errorf("Directory was not created")
	}

	// Test with existing directory (should not return error)
	err = IsNotExistMkDir(testDir)
	if err != nil {
		t.Errorf("IsNotExistMkDir should not fail for existing directory: %v", err)
	}
}

func TestMustOpenStandalone(t *testing.T) {
	// Create a temporary directory
	tempDir, err := os.MkdirTemp("", "test_mustopen_")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Test opening file in existing directory
	testFile := filepath.Join(tempDir, "test.txt")
	file, err := MustOpen(testFile, tempDir)
	if err != nil {
		t.Errorf("MustOpen failed: %v", err)
	}
	if file != nil {
		file.Close()
		defer os.Remove(testFile)
	}

	// Test opening file in non-existent directory
	nonExistentDir := filepath.Join(tempDir, "subdir")
	testFile2 := filepath.Join(nonExistentDir, "test2.txt")
	file2, err := MustOpen(testFile2, nonExistentDir)
	if err != nil {
		t.Errorf("MustOpen should create directory and file: %v", err)
	}
	if file2 != nil {
		file2.Close()
	}
}

// Test file operations with edge cases
func TestFileOperationsEdgeCases(t *testing.T) {
	// Test reading non-existent file
	_, err := Read("/tmp/non_existent_file_" + time.Now().Format("20060102150405"))
	if err == nil {
		t.Errorf("Read should fail for non-existent file")
	}

	// Test writing to invalid path (should fail gracefully)
	err = Write("/invalid/path/that/does/not/exist/file.txt", "content")
	if err == nil {
		t.Errorf("Write should fail for invalid path")
	}
}

// Test concurrent file operations
func TestConcurrentFileOperations(t *testing.T) {
	const numGoroutines = 5
	const numOperations = 3
	
	tempDir, err := os.MkdirTemp("", "concurrent_test_")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	done := make(chan bool, numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()
			
			for j := 0; j < numOperations; j++ {
				filename := filepath.Join(tempDir, "file_"+string(rune(id))+"_"+string(rune(j))+".txt")
				content := "content_" + string(rune(id)) + "_" + string(rune(j))
				
				// Write file
				err := Write(filename, content)
				if err != nil {
					t.Errorf("Goroutine %d, operation %d: Write failed: %v", id, j, err)
					return
				}
				
				// Read file
				readContent, err := Read(filename)
				if err != nil {
					t.Errorf("Goroutine %d, operation %d: Read failed: %v", id, j, err)
					return
				}
				
				if readContent != content {
					t.Errorf("Goroutine %d, operation %d: Content mismatch", id, j)
				}
				
				// Get file size
				size, err := GetSize(filename)
				if err != nil {
					t.Errorf("Goroutine %d, operation %d: GetSize failed: %v", id, j, err)
					return
				}
				
				if size != int64(len(content)) {
					t.Errorf("Goroutine %d, operation %d: Size mismatch", id, j)
				}
			}
		}(i)
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		select {
		case <-done:
			// Goroutine completed
		case <-time.After(10 * time.Second):
			t.Fatalf("Timeout waiting for goroutine %d to complete", i)
		}
	}
}

// Benchmark tests
func BenchmarkWriteStandalone(b *testing.B) {
	tempFile, err := os.CreateTemp("", "bench_write_*.txt")
	if err != nil {
		b.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	tempFile.Close()

	content := "This is benchmark test content"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Write(tempFile.Name(), content)
	}
}

func BenchmarkReadStandalone(b *testing.B) {
	// Setup
	tempFile, err := os.CreateTemp("", "bench_read_*.txt")
	if err != nil {
		b.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	
	content := "This is benchmark test content"
	tempFile.WriteString(content)
	tempFile.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Read(tempFile.Name())
	}
}
