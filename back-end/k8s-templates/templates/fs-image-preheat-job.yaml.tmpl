apiVersion: batch/v1
kind: Job
metadata:
  name: {{.JobName}}
spec:
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: {{.App}}
        recentHours: "{{.RecentHours}}"
    spec:
      restartPolicy: OnFailure
      containers:
        - name: {{.App}}
          image: {{.Image}}
          imagePullPolicy: Always
          command:
            - sh
            - -c
            - |
              echo "add harbor host record in /etc/hosts to solve the problem of harbor access. For the reason, please refer https://www.fxiaoke.com/XV/UI/Home#stream/showfeed2019/=/id-7781165" 
              node_ip=$(echo $NODE_IP)
              echo "$node_ip apdb-harbor.foneshare.cn" >> /etc/hosts
              cat /etc/hosts
              echo ""
              echo "===== start to preheat images ====="
              entrypoint.sh
          env:
            - name: TZ
              value: Asia/Shanghai
            - name: NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            {{- range .Envs}}
            - name: {{.Name}}
              value: "{{.Value}}"
            {{- end}}
          resources:
            limits:
              cpu: 2000m
              memory: 400Mi
            requests:
              cpu: 500m
              memory: 100Mi


