package models

import (
	"fs-k8s-app-manager/models/datatype"
	"testing"
)

func TestApp_Structure(t *testing.T) {
	// Test App struct creation and field access
	app := App{
		Name:       "test-app",
		Owner:      "test-owner",
		Remark:     "Test application",
		Orgs:       datatype.StrList{"org1", "org2"},
		Admins:     datatype.StrList{"admin1", "admin2"},
		Level:      "production",
		TimeWindow: datatype.TimePeriods{},
	}

	if app.Name != "test-app" {
		t.<PERSON><PERSON><PERSON>("Expected Name to be 'test-app', got %q", app.Name)
	}

	if app.Owner != "test-owner" {
		t.<PERSON><PERSON>("Expected Owner to be 'test-owner', got %q", app.Owner)
	}

	if len(app.Orgs) != 2 {
		t.<PERSON><PERSON>("Expected 2 orgs, got %d", len(app.Orgs))
	}

	if app.Orgs[0] != "org1" {
		t.<PERSON><PERSON><PERSON>("Expected first org to be 'org1', got %q", app.Orgs[0])
	}

	if len(app.Admins) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 admins, got %d", len(app.Admins))
	}

	if app.Level != "production" {
		t.<PERSON><PERSON>rf("Expected Level to be 'production', got %q", app.Level)
	}
}

func TestUser_Structure(t *testing.T) {
	// Test User struct creation and field access
	user := User{
		Username:   "testuser",
		RealName:   "测试用户",
		PinYin:     "ceshiyonghu",
		Email:      "<EMAIL>",
		EmployeeId: 12345,
		Roles:      datatype.StrList{"user", "developer"},
		RecentApps: datatype.StrList{"app1", "app2"},
		RecentPods: datatype.StrList{"pod1", "pod2"},
	}

	if user.Username != "testuser" {
		t.Errorf("Expected Username to be 'testuser', got %q", user.Username)
	}

	if user.RealName != "测试用户" {
		t.Errorf("Expected RealName to be '测试用户', got %q", user.RealName)
	}

	if user.Email != "<EMAIL>" {
		t.Errorf("Expected Email to be '<EMAIL>', got %q", user.Email)
	}

	if user.EmployeeId != 12345 {
		t.Errorf("Expected EmployeeId to be 12345, got %d", user.EmployeeId)
	}

	if len(user.Roles) != 2 {
		t.Errorf("Expected 2 roles, got %d", len(user.Roles))
	}

	if len(user.RecentApps) != 2 {
		t.Errorf("Expected 2 recent apps, got %d", len(user.RecentApps))
	}
}

func TestLog_Structure(t *testing.T) {
	// Test Log struct creation and field access
	log := Log{
		Author:  "test-author",
		Operate: "CREATE",
		Target:  "test-target",
		Content: "Test log content",
	}

	if log.Author != "test-author" {
		t.Errorf("Expected Author to be 'test-author', got %q", log.Author)
	}

	if log.Operate != "CREATE" {
		t.Errorf("Expected Operate to be 'CREATE', got %q", log.Operate)
	}

	if log.Target != "test-target" {
		t.Errorf("Expected Target to be 'test-target', got %q", log.Target)
	}

	if log.Content != "Test log content" {
		t.Errorf("Expected Content to be 'Test log content', got %q", log.Content)
	}

	// CreatedAt is managed by gorm.Model, so we don't set it manually
	t.Logf("Log CreatedAt: %v", log.CreatedAt)
}

// Test basic model functionality without specific Pipeline/Event models
func TestModelBasics(t *testing.T) {
	// Test that we can create basic model instances
	app := App{}
	user := User{}
	log := Log{}

	// Test that models have expected zero values
	if app.Name != "" {
		t.Errorf("Expected empty app name, got %q", app.Name)
	}

	if user.Username != "" {
		t.Errorf("Expected empty username, got %q", user.Username)
	}

	if log.Author != "" {
		t.Errorf("Expected empty log author, got %q", log.Author)
	}

	t.Logf("Basic model creation successful")
}

func TestAppWithComplexData(t *testing.T) {
	// Test App with complex nested data
	app := App{
		Name:   "complex-app",
		Owner:  "complex-owner",
		Orgs:   datatype.StrList{"org1", "org2", "org3", "org4", "org5"},
		Admins: datatype.StrList{"admin1", "admin2", "admin3"},
		Remark: "This is a complex application with multiple organizations and administrators",
		Level:  "production",
	}

	// Test that complex data is handled correctly
	if len(app.Orgs) != 5 {
		t.Errorf("Expected 5 orgs, got %d", len(app.Orgs))
	}

	if len(app.Admins) != 3 {
		t.Errorf("Expected 3 admins, got %d", len(app.Admins))
	}

	// Test that we can access all orgs
	expectedOrgs := []string{"org1", "org2", "org3", "org4", "org5"}
	for i, expectedOrg := range expectedOrgs {
		if app.Orgs[i] != expectedOrg {
			t.Errorf("Expected org at index %d to be %q, got %q", i, expectedOrg, app.Orgs[i])
		}
	}
}

func TestUserWithEmptyFields(t *testing.T) {
	// Test User with some empty fields
	user := User{
		Username:   "user-with-empty-fields",
		RealName:   "",
		Email:      "",
		EmployeeId: 0,
		Roles:      datatype.StrList{},
	}

	if user.Username != "user-with-empty-fields" {
		t.Errorf("Expected Username to be 'user-with-empty-fields', got %q", user.Username)
	}

	if user.RealName != "" {
		t.Errorf("Expected RealName to be empty, got %q", user.RealName)
	}

	if len(user.Roles) != 0 {
		t.Errorf("Expected 0 roles, got %d", len(user.Roles))
	}
}

func TestLogWithLongContent(t *testing.T) {
	// Test Log with very long content
	longContent := ""
	for i := 0; i < 1000; i++ {
		longContent += "This is a very long log content that might test the limits of the content field. "
	}

	log := Log{
		Author:  "test-author",
		Operate: "LONG_OPERATION",
		Target:  "test-target",
		Content: longContent,
	}

	if log.Author != "test-author" {
		t.Errorf("Expected Author to be 'test-author', got %q", log.Author)
	}

	if len(log.Content) != len(longContent) {
		t.Errorf("Expected Content length to be %d, got %d", len(longContent), len(log.Content))
	}
}

func TestModelValidation(t *testing.T) {
	// Test various edge cases and validation scenarios

	// Test App with special characters
	app := App{
		Name:   "app-with-特殊字符-and-symbols!@#$%",
		Owner:  "owner-with-特殊字符",
		Remark: "Application with special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?",
	}

	if app.Name == "" {
		t.Errorf("App name should not be empty")
	}

	// Test User with Unicode characters
	user := User{
		Username:   "unicode-user",
		RealName:   "用户名包含中文字符和Emoji😀🎉",
		Email:      "unicode@测试.com",
		EmployeeId: 999999,
	}

	if user.RealName == "" {
		t.Errorf("User RealName should not be empty")
	}

	// Test Log with edge case values
	log := Log{
		Author:  "edge-case-author",
		Operate: "VERY_LONG_OPERATION_NAME_THAT_MIGHT_CAUSE_ISSUES",
		Target:  "target-with-特殊字符",
		Content: "Log content with special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?",
	}

	if log.Author == "" {
		t.Errorf("Log Author should not be empty")
	}
}

func TestModelDefaults(t *testing.T) {
	// Test that models have appropriate default values
	
	var app App
	if app.Name != "" {
		t.Errorf("Default App Name should be empty, got %q", app.Name)
	}
	
	if app.Orgs == nil {
		t.Logf("Default App Orgs is nil (expected)")
	}
	
	var user User
	if user.Username != "" {
		t.Errorf("Default User Username should be empty, got %q", user.Username)
	}
	
	var log Log
	if !log.CreatedAt.IsZero() {
		t.Errorf("Default Log CreatedAt should be zero time, got %v", log.CreatedAt)
	}
}

func TestDataTypeStrList(t *testing.T) {
	// Test datatype.StrList functionality
	strList := datatype.StrList{"item1", "item2", "item3"}
	
	if len(strList) != 3 {
		t.Errorf("Expected StrList length to be 3, got %d", len(strList))
	}
	
	if strList[0] != "item1" {
		t.Errorf("Expected first item to be 'item1', got %q", strList[0])
	}
	
	// Test appending to StrList
	strList = append(strList, "item4")
	if len(strList) != 4 {
		t.Errorf("Expected StrList length to be 4 after append, got %d", len(strList))
	}
	
	// Test empty StrList
	emptyList := datatype.StrList{}
	if len(emptyList) != 0 {
		t.Errorf("Expected empty StrList length to be 0, got %d", len(emptyList))
	}
}

// Benchmark tests
func BenchmarkAppCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		app := App{
			Name:   "benchmark-app",
			Owner:  "benchmark-owner",
			Orgs:   datatype.StrList{"org1", "org2", "org3"},
			Admins: datatype.StrList{"admin1", "admin2"},
			Level:  "production",
		}
		_ = app
	}
}

func BenchmarkUserCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		user := User{
			Username:   "benchmark-user",
			RealName:   "基准测试用户",
			Email:      "<EMAIL>",
			EmployeeId: 12345,
		}
		_ = user
	}
}

func BenchmarkLogCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		log := Log{
			Author:  "benchmark-author",
			Operate: "BENCHMARK",
			Target:  "benchmark-target",
			Content: "Benchmark log content",
		}
		_ = log
	}
}
