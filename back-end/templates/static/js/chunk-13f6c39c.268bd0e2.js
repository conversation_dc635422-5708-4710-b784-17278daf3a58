(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-13f6c39c"],{"02f4":function(e,t,a){var r=a("4588"),n=a("be13");e.exports=function(e){return function(t,a){var o,l,c=String(n(t)),i=r(a),u=c.length;return i<0||i>=u?e?"":void 0:(o=c.charCodeAt(i),o<55296||o>56319||i+1===u||(l=c.charCodeAt(i+1))<56320||l>57343?e?c.charAt(i):o:e?c.slice(i,i+2):l-56320+(o-55296<<10)+65536)}}},"0390":function(e,t,a){"use strict";var r=a("02f4")(!0);e.exports=function(e,t,a){return t+(a?r(e,t).length:1)}},"0bfb":function(e,t,a){"use strict";var r=a("cb7c");e.exports=function(){var e=r(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},"214f":function(e,t,a){"use strict";a("b0c5");var r=a("2aba"),n=a("32e9"),o=a("79e5"),l=a("be13"),c=a("2b4c"),i=a("520a"),u=c("species"),s=!o((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),p=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var a="ab".split(e);return 2===a.length&&"a"===a[0]&&"b"===a[1]}();e.exports=function(e,t,a){var d=c(e),f=!o((function(){var t={};return t[d]=function(){return 7},7!=""[e](t)})),m=f?!o((function(){var t=!1,a=/a/;return a.exec=function(){return t=!0,null},"split"===e&&(a.constructor={},a.constructor[u]=function(){return a}),a[d](""),!t})):void 0;if(!f||!m||"replace"===e&&!s||"split"===e&&!p){var v=/./[d],b=a(l,d,""[e],(function(e,t,a,r,n){return t.exec===i?f&&!n?{done:!0,value:v.call(t,a,r)}:{done:!0,value:e.call(a,t,r)}:{done:!1}})),h=b[0],g=b[1];r(String.prototype,e,h),n(RegExp.prototype,d,2==t?function(e,t){return g.call(e,this,t)}:function(e){return g.call(e,this)})}}},"520a":function(e,t,a){"use strict";var r=a("0bfb"),n=RegExp.prototype.exec,o=String.prototype.replace,l=n,c="lastIndex",i=function(){var e=/a/,t=/b*/g;return n.call(e,"a"),n.call(t,"a"),0!==e[c]||0!==t[c]}(),u=void 0!==/()??/.exec("")[1],s=i||u;s&&(l=function(e){var t,a,l,s,p=this;return u&&(a=new RegExp("^"+p.source+"$(?!\\s)",r.call(p))),i&&(t=p[c]),l=n.call(p,e),i&&l&&(p[c]=p.global?l.index+l[0].length:t),u&&l&&l.length>1&&o.call(l[0],a,(function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(l[s]=void 0)})),l}),e.exports=l},"5f1b":function(e,t,a){"use strict";var r=a("23c6"),n=RegExp.prototype.exec;e.exports=function(e,t){var a=e.exec;if("function"===typeof a){var o=a.call(e,t);if("object"!==typeof o)throw new TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==r(e))throw new TypeError("RegExp#exec called on incompatible receiver");return n.call(e,t)}},"6b5c":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"pod-deregister-manage"}}),e._v(" "),a("div",[a("el-form",{attrs:{inline:!0,model:e.form}},[a("el-form-item",{attrs:{label:"环境"}},[a("el-select",{staticStyle:{width:"360px"},attrs:{filterable:""},model:{value:e.form.cluster,callback:function(t){e.$set(e.form,"cluster",t)},expression:"form.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster,value:e}})})),1),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[e._v("查询")])],1)],1)],1),e._v(" "),a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.data}},[a("el-table-column",{attrs:{prop:"metadata.name",label:"Pod"}}),e._v(" "),a("el-table-column",{attrs:{prop:"cluster",label:"集群"}}),e._v(" "),a("el-table-column",{attrs:{prop:"namespace",label:"命名空间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"app",label:"应用"}}),e._v(" "),a("el-table-column",{attrs:{label:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.podPage(t.row.cluster,t.row.namespace,t.row.app)}}},[e._v("实例管理页")])]}}])})],1)],1)],1)},n=[],o=(a("a481"),a("7f7f"),a("2d63")),l=a("a527"),c=a("a68b"),i={components:{appManageTab:c["a"]},data:function(){return{form:{cluster:null},data:[],loading:!1}},computed:{clusterOptions:function(){var e,t=[],a=Object(o["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var r=e.value;t.push(r.name)}}catch(n){a.e(n)}finally{a.f()}return t}},methods:{loadData:function(){var e=this;this.form.cluster?(this.loading=!0,Object(l["l"])(this.form.cluster).then((function(t){e.data=t.data;var a,r=Object(o["a"])(e.data);try{for(r.s();!(a=r.n()).done;){var n=a.value;n.cluster=e.form.cluster,n.namespace=n.metadata.namespace,n.app=n.metadata.labels.app.replace("-close","")}}catch(l){r.e(l)}finally{r.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))):this.$message.error("请选择环境")},podPage:function(e,t,a){var r=this.$router.resolve({name:"pod-index",query:{cluster:e,namespace:t,app:a}});window.open(r.href,"_blank")}}},u=i,s=a("2877"),p=Object(s["a"])(u,r,n,!1,null,"ce07606e",null);t["default"]=p.exports},a481:function(e,t,a){"use strict";var r=a("cb7c"),n=a("4bf8"),o=a("9def"),l=a("4588"),c=a("0390"),i=a("5f1b"),u=Math.max,s=Math.min,p=Math.floor,d=/\$([$&`']|\d\d?|<[^>]*>)/g,f=/\$([$&`']|\d\d?)/g,m=function(e){return void 0===e?e:String(e)};a("214f")("replace",2,(function(e,t,a,v){return[function(r,n){var o=e(this),l=void 0==r?void 0:r[t];return void 0!==l?l.call(r,o,n):a.call(String(o),r,n)},function(e,t){var n=v(a,e,this,t);if(n.done)return n.value;var p=r(e),d=String(this),f="function"===typeof t;f||(t=String(t));var h=p.global;if(h){var g=p.unicode;p.lastIndex=0}var x=[];while(1){var y=i(p,d);if(null===y)break;if(x.push(y),!h)break;var k=String(y[0]);""===k&&(p.lastIndex=c(d,o(p.lastIndex),g))}for(var w="",$=0,_=0;_<x.length;_++){y=x[_];for(var j=String(y[0]),O=u(s(l(y.index),d.length),0),S=[],E=1;E<y.length;E++)S.push(m(y[E]));var T=y.groups;if(f){var R=[j].concat(S,O,d);void 0!==T&&R.push(T);var C=String(t.apply(void 0,R))}else C=b(j,d,O,S,T,t);O>=$&&(w+=d.slice($,O)+C,$=O+j.length)}return w+d.slice($)}];function b(e,t,r,o,l,c){var i=r+e.length,u=o.length,s=f;return void 0!==l&&(l=n(l),s=d),a.call(c,s,(function(a,n){var c;switch(n.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(i);case"<":c=l[n.slice(1,-1)];break;default:var s=+n;if(0===s)return a;if(s>u){var d=p(s/10);return 0===d?a:d<=u?void 0===o[d-1]?n.charAt(1):o[d-1]+n.charAt(1):a}c=o[s-1]}return void 0===c?"":c}))}}))},a527:function(e,t,a){"use strict";a.d(t,"h",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"i",(function(){return l})),a.d(t,"e",(function(){return c})),a.d(t,"g",(function(){return i})),a.d(t,"c",(function(){return u})),a.d(t,"j",(function(){return s})),a.d(t,"k",(function(){return p})),a.d(t,"l",(function(){return d})),a.d(t,"n",(function(){return f})),a.d(t,"f",(function(){return m})),a.d(t,"q",(function(){return v})),a.d(t,"b",(function(){return b})),a.d(t,"a",(function(){return h})),a.d(t,"o",(function(){return g})),a.d(t,"p",(function(){return x})),a.d(t,"m",(function(){return y}));a("96cf"),a("3b8d");var r=a("b775");function n(e,t,a){return Object(r["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function o(e,t,a){return Object(r["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}function l(e,t){return Object(r["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:e,namespace:t}})}function c(e,t,a){return Object(r["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:e,namespace:t,pod:a}})}function i(e,t,a,n,o,l){return Object(r["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:e,namespace:t,pod:a,container:n,tailLines:o,previous:l}})}function u(e,t,a,r,n){var o="/api/v1/k8s/pod/stdout/download?cluster=".concat(e,"&namespace=").concat(t,"&pod=").concat(a,"&container=").concat(r,"&tailLines=").concat(n,'&_time="')+(new Date).getTime();window.open(o)}function s(e,t,a){return Object(r["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:e,namespace:t,pod:a}})}function p(e,t,a){return Object(r["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:e,namespace:t,pod:a}})}function d(e){return Object(r["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:e}})}function f(e,t,a){return Object(r["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:e,namespace:t,pod:a}})}function m(e){return Object(r["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:e})}function v(e){return Object(r["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:e})}function b(e,t){window.open("/api/v1/k8s/pod/file/download?fileId="+e+"&fileName="+t+"&_time="+(new Date).getTime())}function h(e){return Object(r["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:e})}function g(e){window.open("/api/v1/k8s/pod/file/preview?fileId="+e)}function x(e,t,a){return Object(r["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:e,namespace:t,pod:a}})}function y(e,t,a){return Object(r["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:e,namespace:t,pod:a}})}},a68b:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"自动扩缩容-批量创建",name:"auto-scale-batch-create"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},n=[],o=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(e,t){var a=e.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"auto-scale-batch-create"===a?this.$router.push({name:"tool-auto-scale-batch-create"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),l=o,c=(a("d54f"),a("2877")),i=Object(c["a"])(l,r,n,!1,null,null,null);t["a"]=i.exports},b0c5:function(e,t,a){"use strict";var r=a("520a");a("5ca1")({target:"RegExp",proto:!0,forced:r!==/./.exec},{exec:r})},d54f:function(e,t,a){"use strict";a("eba1")},eba1:function(e,t,a){}}]);