package models

import (
	"fmt"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"time"
)

/**
自动扩缩容 (任意时间段内扩容，固定时间段内缩容）
*/
type AutoScale struct {
	gorm.Model
	App                string `gorm:"size:64;<-:create;index;not null"`
	Cluster            string `gorm:"size:32;<-:create;index;not null"`
	Namespace          string `gorm:"size:32;<-:create;index;not null"`
	CpuTargetPercent   int    `gorm:"size:3"`
	Replicas           uint   `gorm:"size:3"`
	ScaleUpThreshold   int    `gorm:"size:5;default:2"`
	ScaleDownThreshold int    `gorm:"size:5;default:10"`
	ScaleDownStartTime string `gorm:"size:12;default:'23:00'"`
	ScaleDownEndTime   string `gorm:"size:12;default:'06:00'"`
	Author             string `gorm:"size:32"`
}

func (a AutoScale) InScaleDownTimeRange() bool {
	time1, err := time.Parse("15:04", a.ScaleDownStartTime)
	if err != nil {
		log.Info("ScaleDownStartTime parse fail ", err.Error())
		return false
	}
	time2, err := time.Parse("15:04", a.ScaleDownEndTime)
	if err != nil {
		log.Info("ScaleDownEndTime parse fail ", err.Error())
		return false
	}
	n := time.Now()
	startTime := time.Date(n.Year(), n.Month(), n.Day(), time1.Hour(), time1.Minute(), 0, 0, n.Location())
	endTime := time.Date(n.Year(), n.Month(), n.Day(), time2.Hour(), time2.Minute(), 0, 0, n.Location())

	if endTime.Before(startTime) {
		//如果结束时间小于开始时间， 则说明时间区间跨天了， 则缩容范围为 0-startTime 或者 endTime-24
		//比如： 【00:00 - 06:00】 或者 【21:00 - 24:00】
		return n.Before(endTime) || n.After(startTime)
	} else {
		//比如： 【12:00 - 14:00】
		return n.After(startTime) && n.Before(endTime)
	}
}
func (a AutoScale) InScaleUpTimeRange() bool {
	return !a.InScaleDownTimeRange()
}

func (a AutoScale) AppFullName() string {
	return fmt.Sprintf("%s/%s/%s", a.Cluster, a.Namespace, a.App)
}
