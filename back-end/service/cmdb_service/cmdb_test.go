package cmdb_service

import (
	"fs-k8s-app-manager/models"
	"testing"
	"time"
)

func TestServiceOwners_Structure(t *testing.T) {
	// Test ServiceOwners struct creation and field access
	so := ServiceOwners{
		Service:   "test-service",
		Owners:    []string{"owner1", "owner2"},
		MainOwner: []string{"main-owner"},
		Desc:      "Test service description",
	}

	if so.Service != "test-service" {
		t.<PERSON><PERSON>("Expected Service to be 'test-service', got %q", so.Service)
	}

	if len(so.Owners) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 owners, got %d", len(so.Owners))
	}

	if so.Owners[0] != "owner1" {
		t.<PERSON><PERSON><PERSON>("Expected first owner to be 'owner1', got %q", so.Owners[0])
	}

	if len(so.MainOwner) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 main owner, got %d", len(so.MainOwner))
	}

	if so.MainOwner[0] != "main-owner" {
		t.<PERSON><PERSON><PERSON>("Expected main owner to be 'main-owner', got %q", so.MainOwner[0])
	}

	if so.Desc != "Test service description" {
		t.<PERSON><PERSON><PERSON>("Expected description to be 'Test service description', got %q", so.Desc)
	}
}

func TestCacheData_Structure(t *testing.T) {
	// Test cacheData map structure
	cache := make(cacheData)
	
	so1 := ServiceOwners{
		Service:   "service1",
		Owners:    []string{"owner1"},
		MainOwner: []string{"main1"},
		Desc:      "Service 1",
	}
	
	so2 := ServiceOwners{
		Service:   "service2",
		Owners:    []string{"owner2", "owner3"},
		MainOwner: []string{"main2"},
		Desc:      "Service 2",
	}
	
	cache["service1"] = so1
	cache["service2"] = so2
	
	if len(cache) != 2 {
		t.Errorf("Expected cache to have 2 entries, got %d", len(cache))
	}
	
	if cache["service1"].Service != "service1" {
		t.Errorf("Expected service1 entry to have Service 'service1', got %q", cache["service1"].Service)
	}
	
	if len(cache["service2"].Owners) != 2 {
		t.Errorf("Expected service2 to have 2 owners, got %d", len(cache["service2"].Owners))
	}
}

func TestClearServiceOwnersCache(t *testing.T) {
	// Test that ClearServiceOwnersCache doesn't panic
	// In test environment, this may not actually clear anything due to cache dependencies
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("ClearServiceOwnersCache panicked: %v", r)
		}
	}()
	
	ClearServiceOwnersCache()
}

func TestGetServiceOwners(t *testing.T) {
	tests := []struct {
		name        string
		serviceName string
	}{
		{
			name:        "Valid service name",
			serviceName: "test-service",
		},
		{
			name:        "Empty service name",
			serviceName: "",
		},
		{
			name:        "Non-existent service",
			serviceName: "non-existent-service",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return empty results due to missing cache/CMS data
			// But we test that the function doesn't panic
			owners := GetServiceOwners(tt.serviceName)
			
			// owners should be a slice (even if empty)
			if owners == nil {
				t.Errorf("GetServiceOwners should return a non-nil slice")
			}
			
			// Log the result for debugging
			t.Logf("GetServiceOwners(%q) returned %d owners", tt.serviceName, len(owners))
		})
	}
}

func TestGetServiceMainOwners(t *testing.T) {
	tests := []struct {
		name        string
		serviceName string
	}{
		{
			name:        "Valid service name",
			serviceName: "test-service",
		},
		{
			name:        "Empty service name",
			serviceName: "",
		},
		{
			name:        "Non-existent service",
			serviceName: "non-existent-service",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return empty results due to missing cache/CMS data
			// But we test that the function doesn't panic
			mainOwners := GetServiceMainOwners(tt.serviceName)
			
			// mainOwners should be a slice (even if empty)
			if mainOwners == nil {
				t.Errorf("GetServiceMainOwners should return a non-nil slice")
			}
			
			// Log the result for debugging
			t.Logf("GetServiceMainOwners(%q) returned %d main owners", tt.serviceName, len(mainOwners))
		})
	}
}

func TestGetServiceDesc(t *testing.T) {
	tests := []struct {
		name        string
		serviceName string
	}{
		{
			name:        "Valid service name",
			serviceName: "test-service",
		},
		{
			name:        "Empty service name",
			serviceName: "",
		},
		{
			name:        "Non-existent service",
			serviceName: "non-existent-service",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return empty string due to missing cache/CMS data
			// But we test that the function doesn't panic
			desc := GetServiceDesc(tt.serviceName)
			
			// desc should be a string (even if empty)
			if desc == "" {
				t.Logf("GetServiceDesc(%q) returned empty description (expected in test environment)", tt.serviceName)
			} else {
				t.Logf("GetServiceDesc(%q) returned: %q", tt.serviceName, desc)
			}
		})
	}
}

func TestGetUserByRealName(t *testing.T) {
	tests := []struct {
		name     string
		realName string
	}{
		{
			name:     "Valid real name",
			realName: "张三",
		},
		{
			name:     "English name",
			realName: "John Doe",
		},
		{
			name:     "Empty real name",
			realName: "",
		},
		{
			name:     "Non-existent user",
			realName: "non-existent-user",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return empty user due to missing user service data
			// But we test that the function doesn't panic
			user := GetUserByRealName(tt.realName)
			
			// user should be a User struct (even if empty)
			if user.RealName != "" {
				t.Logf("GetUserByRealName(%q) found user: %+v", tt.realName, user)
			} else {
				t.Logf("GetUserByRealName(%q) returned empty user (expected in test environment)", tt.realName)
			}
		})
	}
}

func TestGetUsersByRealNames(t *testing.T) {
	tests := []struct {
		name      string
		realNames []string
	}{
		{
			name:      "Multiple valid names",
			realNames: []string{"张三", "李四", "王五"},
		},
		{
			name:      "Single name",
			realNames: []string{"张三"},
		},
		{
			name:      "Empty slice",
			realNames: []string{},
		},
		{
			name:      "Names with empty strings",
			realNames: []string{"张三", "", "李四"},
		},
		{
			name:      "Non-existent users",
			realNames: []string{"non-existent-1", "non-existent-2"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return empty results due to missing user service data
			// But we test that the function doesn't panic
			users := GetUsersByRealNames(tt.realNames)
			
			// users should be a slice (even if empty)
			if users == nil {
				t.Errorf("GetUsersByRealNames should return a non-nil slice")
			}
			
			// Log the result for debugging
			t.Logf("GetUsersByRealNames(%v) returned %d users", tt.realNames, len(users))
			
			// Check that returned users are valid User structs
			for i, user := range users {
				if user.RealName == "" && user.Username == "" {
					t.Logf("User at index %d is empty (expected in test environment)", i)
				}
			}
		})
	}
}

// Test concurrent access to cache functions
func TestConcurrentCacheAccess(t *testing.T) {
	const numGoroutines = 5
	const numOperations = 3
	
	done := make(chan bool, numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()
			
			for j := 0; j < numOperations; j++ {
				// Test concurrent access to cache functions
				GetServiceOwners("test-service")
				GetServiceMainOwners("test-service")
				GetServiceDesc("test-service")
				GetUserByRealName("test-user")
			}
		}(i)
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		select {
		case <-done:
			// Goroutine completed
		case <-time.After(10 * time.Second):
			t.Fatalf("Timeout waiting for goroutine %d to complete", i)
		}
	}
}

// Test with various edge cases
func TestEdgeCases(t *testing.T) {
	// Test with very long service names
	longServiceName := string(make([]byte, 1000))
	for i := range longServiceName {
		longServiceName = longServiceName[:i] + "a" + longServiceName[i+1:]
	}
	
	owners := GetServiceOwners(longServiceName)
	if owners == nil {
		t.Errorf("GetServiceOwners should handle long service names gracefully")
	}
	
	// Test with special characters in service names
	specialServiceName := "service-with-特殊字符-and-symbols!@#$%"
	owners = GetServiceOwners(specialServiceName)
	if owners == nil {
		t.Errorf("GetServiceOwners should handle special characters gracefully")
	}
	
	// Test with very long real names
	longRealName := string(make([]byte, 500))
	for i := range longRealName {
		longRealName = longRealName[:i] + "张" + longRealName[i+1:]
	}
	
	user := GetUserByRealName(longRealName)
	// Should not panic, even if user is empty
	t.Logf("GetUserByRealName with long name returned user with RealName: %q", user.RealName)
}

// Benchmark tests
func BenchmarkGetServiceOwners(b *testing.B) {
	serviceName := "benchmark-service"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetServiceOwners(serviceName)
	}
}

func BenchmarkGetUserByRealName(b *testing.B) {
	realName := "benchmark-user"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetUserByRealName(realName)
	}
}

func BenchmarkGetUsersByRealNames(b *testing.B) {
	realNames := []string{"user1", "user2", "user3", "user4", "user5"}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetUsersByRealNames(realNames)
	}
}
