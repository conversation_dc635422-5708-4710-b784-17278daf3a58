package strslice

import (
	"strings"
	"testing"
)

func TestFind(t *testing.T) {
	tests := []struct {
		name     string
		slice    []string
		item     string
		expected bool
	}{
		{
			name:     "Item exists in slice",
			slice:    []string{"apple", "banana", "cherry"},
			item:     "banana",
			expected: true,
		},
		{
			name:     "Item does not exist in slice",
			slice:    []string{"apple", "banana", "cherry"},
			item:     "orange",
			expected: false,
		},
		{
			name:     "Empty slice",
			slice:    []string{},
			item:     "apple",
			expected: false,
		},
		{
			name:     "Empty item in non-empty slice",
			slice:    []string{"apple", "banana", "cherry"},
			item:     "",
			expected: false,
		},
		{
			name:     "Empty item in slice with empty string",
			slice:    []string{"apple", "", "cherry"},
			item:     "",
			expected: true,
		},
		{
			name:     "Case sensitive check",
			slice:    []string{"Apple", "Banana", "Cherry"},
			item:     "apple",
			expected: false,
		},
		{
			name:     "Single item slice - match",
			slice:    []string{"only"},
			item:     "only",
			expected: true,
		},
		{
			name:     "Single item slice - no match",
			slice:    []string{"only"},
			item:     "other",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Find(tt.slice, tt.item)
			if result != tt.expected {
				t.Errorf("Find(%v, %q) = %v, expected %v", tt.slice, tt.item, result, tt.expected)
			}
		})
	}
}

func TestFirstNotEmpty(t *testing.T) {
	tests := []struct {
		name     string
		items    []string
		expected string
	}{
		{
			name:     "First item is not empty",
			items:    []string{"first", "second", "third"},
			expected: "first",
		},
		{
			name:     "First item is empty, second is not",
			items:    []string{"", "second", "third"},
			expected: "second",
		},
		{
			name:     "All items are empty",
			items:    []string{"", "", ""},
			expected: "",
		},
		{
			name:     "No items",
			items:    []string{},
			expected: "",
		},
		{
			name:     "Single non-empty item",
			items:    []string{"only"},
			expected: "only",
		},
		{
			name:     "Single empty item",
			items:    []string{""},
			expected: "",
		},
		{
			name:     "Mixed empty and non-empty",
			items:    []string{"", "", "third", "fourth"},
			expected: "third",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FirstNotEmpty(tt.items...)
			if result != tt.expected {
				t.Errorf("FirstNotEmpty(%v) = %q, expected %q", tt.items, result, tt.expected)
			}
		})
	}
}

func TestRemoveDuplicates(t *testing.T) {
	tests := []struct {
		name     string
		slice    []string
		expected []string
	}{
		{
			name:     "No duplicates",
			slice:    []string{"apple", "banana", "cherry"},
			expected: []string{"apple", "banana", "cherry"},
		},
		{
			name:     "With duplicates",
			slice:    []string{"apple", "banana", "apple", "cherry", "banana"},
			expected: []string{"apple", "banana", "cherry"},
		},
		{
			name:     "Empty slice",
			slice:    []string{},
			expected: []string{},
		},
		{
			name:     "Single item",
			slice:    []string{"only"},
			expected: []string{"only"},
		},
		{
			name:     "All same items",
			slice:    []string{"same", "same", "same"},
			expected: []string{"same"},
		},
		{
			name:     "With empty strings",
			slice:    []string{"apple", "", "banana", "", "cherry"},
			expected: []string{"apple", "", "banana", "cherry"},
		},
		{
			name:     "Consecutive duplicates",
			slice:    []string{"apple", "apple", "banana", "banana", "cherry"},
			expected: []string{"apple", "banana", "cherry"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := RemoveDuplicates(tt.slice)

			// Check length
			if len(result) != len(tt.expected) {
				t.Errorf("RemoveDuplicates(%v) returned slice of length %d, expected %d",
					tt.slice, len(result), len(tt.expected))
				return
			}

			// Check that all expected items are present
			for _, expectedItem := range tt.expected {
				if !Find(result, expectedItem) {
					t.Errorf("RemoveDuplicates(%v) = %v, missing expected item %q", tt.slice, result, expectedItem)
					return
				}
			}

			// Check that there are no duplicates in result
			seen := make(map[string]bool)
			for _, item := range result {
				if seen[item] {
					t.Errorf("RemoveDuplicates(%v) = %v, contains duplicate item %q", tt.slice, result, item)
					return
				}
				seen[item] = true
			}
		})
	}
}

func TestFilter(t *testing.T) {
	// Test filtering with a simple predicate
	isLongWord := func(s string) bool {
		return len(s) > 5
	}
	
	tests := []struct {
		name      string
		slice     []string
		predicate func(string) bool
		expected  []string
	}{
		{
			name:      "Filter long words",
			slice:     []string{"cat", "elephant", "dog", "hippopotamus", "ant"},
			predicate: isLongWord,
			expected:  []string{"elephant", "hippopotamus"},
		},
		{
			name:      "No matches",
			slice:     []string{"cat", "dog", "ant"},
			predicate: isLongWord,
			expected:  []string{},
		},
		{
			name:      "All match",
			slice:     []string{"elephant", "hippopotamus", "rhinoceros"},
			predicate: isLongWord,
			expected:  []string{"elephant", "hippopotamus", "rhinoceros"},
		},
		{
			name:      "Empty slice",
			slice:     []string{},
			predicate: isLongWord,
			expected:  []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Filter(tt.slice, tt.predicate)
			
			// Check length
			if len(result) != len(tt.expected) {
				t.Errorf("Filter(%v, predicate) returned slice of length %d, expected %d", 
					tt.slice, len(result), len(tt.expected))
				return
			}
			
			// Check each element
			for i, v := range result {
				if v != tt.expected[i] {
					t.Errorf("Filter(%v, predicate) = %v, expected %v", tt.slice, result, tt.expected)
					break
				}
			}
		})
	}
}

func TestMap(t *testing.T) {
	// Test mapping with a simple function
	toUpper := func(s string) string {
		return strings.ToUpper(s)
	}
	
	tests := []struct {
		name     string
		slice    []string
		mapper   func(string) string
		expected []string
	}{
		{
			name:     "Convert to uppercase",
			slice:    []string{"apple", "banana", "cherry"},
			mapper:   toUpper,
			expected: []string{"APPLE", "BANANA", "CHERRY"},
		},
		{
			name:     "Empty slice",
			slice:    []string{},
			mapper:   toUpper,
			expected: []string{},
		},
		{
			name:     "Single item",
			slice:    []string{"hello"},
			mapper:   toUpper,
			expected: []string{"HELLO"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Map(tt.slice, tt.mapper)
			
			// Check length
			if len(result) != len(tt.expected) {
				t.Errorf("Map(%v, mapper) returned slice of length %d, expected %d", 
					tt.slice, len(result), len(tt.expected))
				return
			}
			
			// Check each element
			for i, v := range result {
				if v != tt.expected[i] {
					t.Errorf("Map(%v, mapper) = %v, expected %v", tt.slice, result, tt.expected)
					break
				}
			}
		})
	}
}

func TestFindStringsNotInArray(t *testing.T) {
	tests := []struct {
		name     string
		arr1     []string
		from     []string
		expected []string
	}{
		{
			name:     "Some items not in array",
			arr1:     []string{"apple", "banana", "cherry", "date"},
			from:     []string{"banana", "date"},
			expected: []string{"apple", "cherry"},
		},
		{
			name:     "All items in array",
			arr1:     []string{"apple", "banana"},
			from:     []string{"apple", "banana", "cherry"},
			expected: []string{},
		},
		{
			name:     "No items in array",
			arr1:     []string{"apple", "banana"},
			from:     []string{"cherry", "date"},
			expected: []string{"apple", "banana"},
		},
		{
			name:     "Empty arr1",
			arr1:     []string{},
			from:     []string{"apple", "banana"},
			expected: []string{},
		},
		{
			name:     "Empty from",
			arr1:     []string{"apple", "banana"},
			from:     []string{},
			expected: []string{"apple", "banana"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FindStringsNotInArray(tt.arr1, tt.from)

			if len(result) != len(tt.expected) {
				t.Errorf("FindStringsNotInArray(%v, %v) returned slice of length %d, expected %d",
					tt.arr1, tt.from, len(result), len(tt.expected))
				return
			}

			for i, v := range result {
				if v != tt.expected[i] {
					t.Errorf("FindStringsNotInArray(%v, %v) = %v, expected %v", tt.arr1, tt.from, result, tt.expected)
					break
				}
			}
		})
	}
}

// Benchmark tests
func BenchmarkFind(b *testing.B) {
	slice := []string{"apple", "banana", "cherry", "date", "elderberry", "fig", "grape"}
	item := "elderberry"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Find(slice, item)
	}
}

func BenchmarkRemoveDuplicates(b *testing.B) {
	slice := []string{"apple", "banana", "apple", "cherry", "banana", "date", "apple", "elderberry"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		RemoveDuplicates(slice)
	}
}

func BenchmarkFilter(b *testing.B) {
	slice := []string{"apple", "banana", "cherry", "date", "elderberry", "fig", "grape"}
	isLongWord := func(s string) bool {
		return len(s) > 5
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Filter(slice, isLongWord)
	}
}
