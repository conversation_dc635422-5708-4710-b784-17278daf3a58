package template

import (
	"bytes"
	"github.com/Masterminds/sprig"
	"path/filepath"
	"text/template"
)

func ParseTemplate(text string, data interface{}) (ret string, err error) {
	t, err := template.New("").Funcs(sprig.FuncMap()).Parse(text)
	if err != nil {
		return "", err
	}
	return renderTemplate(t, data)
}
func ParseTemplateFile(templateFile string, data interface{}) (ret string, err error) {
	t, err := template.New(filepath.Base(templateFile)).Funcs(sprig.FuncMap()).ParseFiles(templateFile)
	if err != nil {
		return "", err
	}
	return renderTemplate(t, data)
}

func renderTemplate(t *template.Template, data interface{}) (ret string, err error) {
	var tpl bytes.Buffer
	err = t.Execute(&tpl, data)
	if err != nil {
		return
	}
	ret = tpl.String()
	return
}
