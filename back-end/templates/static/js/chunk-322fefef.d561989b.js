(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-322fefef"],{"018c":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{"margin-top":"-10px"}},[n("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[n("el-tab-pane",{attrs:{label:"老集群（k8s1)",name:"old-k8s"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"新集群（k8s0)",name:"new-k8s"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"应用迁移处理",name:"migrate-operation"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"发布流程处理",name:"pipeline-batch-operation"}})],1)],1)},r=[],o=(n("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var n=t.name;"old-k8s"===n?this.$router.push({name:"pipeline-migrate-old"}):"new-k8s"===n?this.$router.push({name:"pipeline-migrate-new"}):"migrate-operation"===n?this.$router.push({name:"pipeline-migrate-operation"}):"pipeline-batch-operation"===n?this.$router.push({name:"pipeline-batch-operation"}):this.$message.error("未知操作")}}}),i=o,c=n("2877"),u=Object(c["a"])(i,a,r,!1,null,null,null);e["a"]=u.exports},"02f4":function(t,e,n){var a=n("4588"),r=n("be13");t.exports=function(t){return function(e,n){var o,i,c=String(r(e)),u=a(n),s=c.length;return u<0||u>=s?t?"":void 0:(o=c.charCodeAt(u),o<55296||o>56319||u+1===s||(i=c.charCodeAt(u+1))<56320||i>57343?t?c.charAt(u):o:t?c.slice(u,u+2):i-56320+(o-55296<<10)+65536)}}},"0390":function(t,e,n){"use strict";var a=n("02f4")(!0);t.exports=function(t,e,n){return e+(n?a(t,e).length:1)}},"0bfb":function(t,e,n){"use strict";var a=n("cb7c");t.exports=function(){var t=a(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"214f":function(t,e,n){"use strict";n("b0c5");var a=n("2aba"),r=n("32e9"),o=n("79e5"),i=n("be13"),c=n("2b4c"),u=n("520a"),s=c("species"),l=!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),d=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();t.exports=function(t,e,n){var p=c(t),f=!o((function(){var e={};return e[p]=function(){return 7},7!=""[t](e)})),v=f?!o((function(){var e=!1,n=/a/;return n.exec=function(){return e=!0,null},"split"===t&&(n.constructor={},n.constructor[s]=function(){return n}),n[p](""),!e})):void 0;if(!f||!v||"replace"===t&&!l||"split"===t&&!d){var h=/./[p],m=n(i,p,""[t],(function(t,e,n,a,r){return e.exec===u?f&&!r?{done:!0,value:h.call(e,n,a)}:{done:!0,value:t.call(n,e,a)}:{done:!1}})),b=m[0],g=m[1];a(String.prototype,t,b),r(RegExp.prototype,p,2==e?function(t,e){return g.call(t,this,e)}:function(t){return g.call(t,this)})}}},"28a5":function(t,e,n){"use strict";var a=n("aae3"),r=n("cb7c"),o=n("ebd6"),i=n("0390"),c=n("9def"),u=n("5f1b"),s=n("520a"),l=n("79e5"),d=Math.min,p=[].push,f="split",v="length",h="lastIndex",m=4294967295,b=!l((function(){RegExp(m,"y")}));n("214f")("split",2,(function(t,e,n,l){var g;return g="c"=="abbc"[f](/(b)*/)[1]||4!="test"[f](/(?:)/,-1)[v]||2!="ab"[f](/(?:ab)*/)[v]||4!="."[f](/(.?)(.?)/)[v]||"."[f](/()()/)[v]>1||""[f](/.?/)[v]?function(t,e){var r=String(this);if(void 0===t&&0===e)return[];if(!a(t))return n.call(r,t,e);var o,i,c,u=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,f=void 0===e?m:e>>>0,b=new RegExp(t.source,l+"g");while(o=s.call(b,r)){if(i=b[h],i>d&&(u.push(r.slice(d,o.index)),o[v]>1&&o.index<r[v]&&p.apply(u,o.slice(1)),c=o[0][v],d=i,u[v]>=f))break;b[h]===o.index&&b[h]++}return d===r[v]?!c&&b.test("")||u.push(""):u.push(r.slice(d)),u[v]>f?u.slice(0,f):u}:"0"[f](void 0,0)[v]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,a){var r=t(this),o=void 0==n?void 0:n[e];return void 0!==o?o.call(n,r,a):g.call(String(r),n,a)},function(t,e){var a=l(g,t,this,e,g!==n);if(a.done)return a.value;var s=r(t),p=String(this),f=o(s,RegExp),v=s.unicode,h=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(b?"y":"g"),y=new f(b?s:"^(?:"+s.source+")",h),x=void 0===e?m:e>>>0;if(0===x)return[];if(0===p.length)return null===u(y,p)?[p]:[];var j=0,_=0,O=[];while(_<p.length){y.lastIndex=b?_:0;var w,k=u(y,b?p:p.slice(_));if(null===k||(w=d(c(y.lastIndex+(b?0:_)),p.length))===j)_=i(p,_,v);else{if(O.push(p.slice(j,_)),O.length===x)return O;for(var L=1;L<=k.length-1;L++)if(O.push(k[L]),O.length===x)return O;_=j=w}}return O.push(p.slice(j)),O}]}))},"49b1":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("pipeline-migrate-tab",{attrs:{"active-name":"batch-clone"}}),t._v(" "),n("div",[n("div",{staticStyle:{float:"left",width:"600px"}},[n("el-form",[n("el-form-item",{attrs:{label:"请输入发布流程列表"}},[n("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:8,maxRows:12}},model:{value:t.searchForm,callback:function(e){t.searchForm=e},expression:"searchForm"}})],1),t._v(" "),n("el-form-item",[n("el-popconfirm",{attrs:{title:"确定要克隆发布流程吗？"},on:{confirm:t.query}},[n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{slot:"reference",type:"primary",size:"mini"},slot:"reference"},[t._v("打开克隆页面")])],1),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.changeStatus("enabled")}}},[t._v("批量改成【可用】")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.changeStatus("disabled")}}},[t._v("批量改成【禁用】")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.changeStatus("migrated")}}},[t._v("批量改成【已迁移】")]),t._v(" "),n("br"),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.queryReplicas()}}},[t._v("查询副本数")]),t._v(" "),n("el-popconfirm",{attrs:{title:"确定要修改副本数吗？"},on:{confirm:t.updateReplicas}},[n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{slot:"reference",type:"default",size:"mini"},slot:"reference"},[t._v("修改副本数")])],1),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.queryAddr()}}},[t._v("查询地址")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.queryMigrateAddr()}}},[t._v("获取迁移地址")]),t._v(" "),n("br"),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.createClickHouseSql()}}},[t._v("Tomcat流量查询SQL生成")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.createClickHouseSql2()}}},[t._v("FCP流量查询SQL生成")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.trafficAnalysis()}}},[t._v("Tomcat流量查询")])],1)],1)],1),t._v(" "),n("div",{staticStyle:{float:"left",width:"500px","padding-left":"5px"}},[n("el-form",[n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"内容格式示例:"}}),t._v(" "),n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:""}},[n("div",{staticStyle:{"line-height":"1.2em","background-color":"#eee",height:"180px"}},[t._v("\n            # 克隆页面、修改状态、查询副本数："),n("br"),t._v("\n            k8s0/fstest/fs-devops-console"),n("br"),t._v("\n            k8s0/fstest/fs-service-console"),n("br"),t._v("\n            k8s0/fstest/fs-stone-admin"),n("br"),n("br"),t._v("\n            # 修改副本数："),n("br"),t._v("\n            k8s0/fstest/fs-devops-console/1"),n("br"),t._v("\n            k8s0/fstest/fs-service-console/1"),n("br"),t._v("\n            k8s0/fstest/fs-stone-admin/1\n          ")])])],1)],1)]),t._v(" "),t.searchResult?n("div",{staticStyle:{"margin-top":"20px","max-width":"1080px",clear:"both"}},[n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[t._v("操作结果")])]),t._v(" "),n("div",[n("pre",{staticStyle:{"white-space":"pre-wrap","word-wrap":"break-word","overflow-wrap":"break-word","font-size":"12px"}},[t._v(t._s(this.searchResult))])])])],1):t._e()],1)},r=[],o=n("768b"),i=(n("28a5"),n("2d63")),c=n("018c"),u=n("c1ab"),s={components:{PipelineMigrateTab:c["a"]},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{searchForm:"",searchResult:"",btnLoading:!1}},methods:{clonePipeline:function(t){var e=this.$router.resolve({name:"app-pipeline-edit",query:{operate:"clone",pipelineId:t.id,targetCluster:"k8s0",targetNamespace:t.namespace}});window.open(e.href,"_blank")},query:function(){var t=this;this.btnLoading=!0,Object(u["s"])(this.searchForm).then((function(e){t.searchResult=e.data.message,e.data.data.length>0&&(t.$message.info("即将在新窗口打开发布流程编辑页面"),setTimeout((function(){var n,a=Object(i["a"])(e.data.data);try{for(a.s();!(n=a.n()).done;){var r=n.value;t.clonePipeline(r)}}catch(o){a.e(o)}finally{a.f()}}),3e3))})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},changeStatus:function(t){var e=this;Object(u["u"])(t,this.searchForm).then((function(t){e.searchResult=t.data.message})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.btnLoading=!1}))},queryReplicas:function(){var t=this;this.btnLoading=!0,Object(u["v"])(this.searchForm).then((function(e){t.searchResult=e.data.data.join("\n")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},updateReplicas:function(){var t=this;this.btnLoading=!0,Object(u["w"])(this.searchForm).then((function(e){t.searchResult=e.data.data.join("\n")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},queryAddr:function(){var t=this;this.btnLoading=!0,Object(u["o"])(this.searchForm).then((function(e){t.searchResult=e.data.join("\n")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},queryMigrateAddr:function(){var t=this;this.btnLoading=!0,Object(u["p"])(this.searchForm).then((function(e){t.searchResult=e.data.join("\n")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},createClickHouseSql:function(){this.btnLoading=!0;var t,e={},n=Object(i["a"])(this.searchForm.split("\n"));try{for(n.s();!(t=n.n()).done;){var a=t.value;if(""!==a.trim()){var r=a.split("/"),c=Object(o["a"])(r,3),u=c[0],s=c[1],l=c[2];e[u]||(e[u]={}),e[u][s]||(e[u][s]=[]),e[u][s].push(l)}}}catch(m){n.e(m)}finally{n.f()}var d=[];for(var p in e)for(var f in e[p]){var v=e[p][f].join("','"),h="SELECT app, profile as namespace,COUNT(*) as count FROM logger.tomcat_access WHERE toDate(toDateTime(_time_second_)) > toDate(now() - 5*24*3600) AND cluster = 'foneshare-".concat(p,"' AND profile = '").concat(f,"' AND app IN ('").concat(v,"') GROUP BY (app,profile);\n");d.push(h)}this.searchResult=d.join("\n"),this.btnLoading=!1},createClickHouseSql2:function(){this.btnLoading=!0;var t,e=[],n=Object(i["a"])(this.searchForm.split("\n"));try{for(n.s();!(t=n.n()).done;){var a=t.value;a.trim()&&e.push(a.trim())}}catch(c){n.e(c)}finally{n.f()}var r="'"+e.join("','")+"'",o="SELECT server_ip, COUNT(*) as count FROM biz_log.log_cep_dist WHERE toDate(toDateTime(stamp)) > toDate(now() - 5*24*3600) AND  server_ip IN (".concat(r,") GROUP BY server_ip;\n");this.searchResult=o,this.btnLoading=!1},trafficAnalysis:function(){var t=this;this.btnLoading=!0,Object(u["r"])(this.searchForm).then((function(e){t.searchResult=JSON.stringify(e.data,null,2)})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))}}},l=s,d=n("2877"),p=Object(d["a"])(l,a,r,!1,null,null,null);e["default"]=p.exports},"520a":function(t,e,n){"use strict";var a=n("0bfb"),r=RegExp.prototype.exec,o=String.prototype.replace,i=r,c="lastIndex",u=function(){var t=/a/,e=/b*/g;return r.call(t,"a"),r.call(e,"a"),0!==t[c]||0!==e[c]}(),s=void 0!==/()??/.exec("")[1],l=u||s;l&&(i=function(t){var e,n,i,l,d=this;return s&&(n=new RegExp("^"+d.source+"$(?!\\s)",a.call(d))),u&&(e=d[c]),i=r.call(d,t),u&&i&&(d[c]=d.global?i.index+i[0].length:e),s&&i&&i.length>1&&o.call(i[0],n,(function(){for(l=1;l<arguments.length-2;l++)void 0===arguments[l]&&(i[l]=void 0)})),i}),t.exports=i},"5f1b":function(t,e,n){"use strict";var a=n("23c6"),r=RegExp.prototype.exec;t.exports=function(t,e){var n=t.exec;if("function"===typeof n){var o=n.call(t,e);if("object"!==typeof o)throw new TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==a(t))throw new TypeError("RegExp#exec called on incompatible receiver");return r.call(t,e)}},"768b":function(t,e,n){"use strict";var a=n("a745"),r=n.n(a);function o(t){if(r()(t))return t}var i=n("67bb"),c=n.n(i),u=n("5d58"),s=n.n(u);function l(t,e){var n=null==t?null:"undefined"!==typeof c.a&&t[s.a]||t["@@iterator"];if(null!=n){var a,r,o=[],i=!0,u=!1;try{for(n=n.call(t);!(i=(a=n.next()).done);i=!0)if(o.push(a.value),e&&o.length===e)break}catch(l){u=!0,r=l}finally{try{i||null==n["return"]||n["return"]()}finally{if(u)throw r}}return o}}var d=n("e630");function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){return o(t)||l(t,e)||Object(d["a"])(t,e)||p()}n.d(e,"a",(function(){return f}))},aae3:function(t,e,n){var a=n("d3f4"),r=n("2d95"),o=n("2b4c")("match");t.exports=function(t){var e;return a(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==r(t))}},b0c5:function(t,e,n){"use strict";var a=n("520a");n("5ca1")({target:"RegExp",proto:!0,forced:a!==/./.exec},{exec:a})},c1ab:function(t,e,n){"use strict";n.d(e,"j",(function(){return r})),n.d(e,"l",(function(){return o})),n.d(e,"z",(function(){return i})),n.d(e,"A",(function(){return c})),n.d(e,"d",(function(){return u})),n.d(e,"h",(function(){return s})),n.d(e,"D",(function(){return l})),n.d(e,"F",(function(){return d})),n.d(e,"y",(function(){return p})),n.d(e,"b",(function(){return f})),n.d(e,"g",(function(){return v})),n.d(e,"C",(function(){return h})),n.d(e,"E",(function(){return m})),n.d(e,"x",(function(){return b})),n.d(e,"G",(function(){return g})),n.d(e,"m",(function(){return y})),n.d(e,"e",(function(){return x})),n.d(e,"a",(function(){return j})),n.d(e,"B",(function(){return _})),n.d(e,"k",(function(){return O})),n.d(e,"i",(function(){return w})),n.d(e,"s",(function(){return k})),n.d(e,"v",(function(){return L})),n.d(e,"w",(function(){return R})),n.d(e,"o",(function(){return S})),n.d(e,"p",(function(){return E})),n.d(e,"t",(function(){return C})),n.d(e,"f",(function(){return N})),n.d(e,"u",(function(){return $})),n.d(e,"c",(function(){return q})),n.d(e,"q",(function(){return F})),n.d(e,"r",(function(){return T})),n.d(e,"n",(function(){return z}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function o(t){return Object(a["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function i(t){return Object(a["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function u(){return Object(a["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function s(t){return Object(a["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function l(t){return Object(a["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function f(){return Object(a["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function v(t,e,n,r,o,i,c,u){return Object(a["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(e,"&suffixVersion=").concat(n,"&message=").concat(r,"&dependencyCheck=").concat(i,"&parentPom=").concat(c),method:"post",data:u})}function h(t){return Object(a["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function m(t){return Object(a["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,n,r){return Object(a["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(n,"&overrideNamespace=").concat(r),method:"post"})}function x(t,e,n,r,o){return Object(a["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(n,"&remark=").concat(r,"&dryRun=").concat(o),method:"post"})}function j(){return Object(a["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function _(){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function O(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function w(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function k(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function L(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function R(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function S(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function E(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function C(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function N(t){return Object(a["a"])({url:"/v1/tool/autoscale-batch-create",method:"post",data:t})}function $(t,e){return Object(a["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function q(t){return Object(a["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function F(t){return Object(a["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function T(t){return Object(a["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function z(t,e,n){return Object(a["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(n),method:"get"})}}}]);