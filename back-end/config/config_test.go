package config

import (
	"os"
	"testing"
)

func TestConfig_Structure(t *testing.T) {
	// Test that Config struct can be created
	config := Config{
		App: App{
			RunMode:       "debug",
			HttpPort:      8080,
			CacheCategory: "goCache",
			RuntimeDir:    "./runtime",
			UploadDir:     "./uploads",
			DownloadDir:   "./downloads",
			KubeConfDir:   "./kubeconf",
		},
		Postgres: Postgres{
			Host:     "localhost",
			Port:     5432,
			User:     "test_user",
			Password: "test_pass",
			Database: "test_db",
		},
		Redis: Redis{
			Addr:     "localhost:6379",
			Password: "",
			DB:       0,
		},
		Hostname: "test-hostname",
	}

	if config.App.RunMode != "debug" {
		t.Errorf("Expected App.RunMode to be 'debug', got %q", config.App.RunMode)
	}

	if config.App.HttpPort != 8080 {
		t.<PERSON><PERSON><PERSON>("Expected App.HttpPort to be 8080, got %d", config.App.HttpPort)
	}

	if config.Postgres.Host != "localhost" {
		t.<PERSON>("Expected Postgres.Host to be 'localhost', got %q", config.Postgres.Host)
	}

	if config.Redis.Addr != "localhost:6379" {
		t.Errorf("Expected Redis.Addr to be 'localhost:6379', got %q", config.Redis.Addr)
	}
}

func TestApp_Structure(t *testing.T) {
	app := App{
		RunMode:       "release",
		HttpPort:      9000,
		CacheCategory: "redis",
		RuntimeDir:    "./runtime",
		UploadDir:     "./uploads",
		DownloadDir:   "./downloads",
		KubeConfDir:   "./kubeconf",
	}

	if app.RunMode != "release" {
		t.Errorf("Expected RunMode to be 'release', got %q", app.RunMode)
	}

	if app.HttpPort != 9000 {
		t.Errorf("Expected HttpPort to be 9000, got %d", app.HttpPort)
	}

	if app.CacheCategory != "redis" {
		t.Errorf("Expected CacheCategory to be 'redis', got %q", app.CacheCategory)
	}

	if app.RuntimeDir != "./runtime" {
		t.Errorf("Expected RuntimeDir to be './runtime', got %q", app.RuntimeDir)
	}
}

func TestPostgres_Structure(t *testing.T) {
	db := Postgres{
		Host:     "db.example.com",
		Port:     5432,
		User:     "prod_user",
		Password: "secure_password",
		Database: "production_db",
	}

	if db.Host != "db.example.com" {
		t.Errorf("Expected Host to be 'db.example.com', got %q", db.Host)
	}

	if db.Port != 5432 {
		t.Errorf("Expected Port to be 5432, got %d", db.Port)
	}

	if db.User != "prod_user" {
		t.Errorf("Expected User to be 'prod_user', got %q", db.User)
	}

	if db.Database != "production_db" {
		t.Errorf("Expected Database to be 'production_db', got %q", db.Database)
	}
}

func TestRedis_Structure(t *testing.T) {
	redis := Redis{
		Addr:     "redis.example.com:6380",
		Password: "redis_password",
		DB:       5,
	}

	if redis.Addr != "redis.example.com:6380" {
		t.Errorf("Expected Addr to be 'redis.example.com:6380', got %q", redis.Addr)
	}

	if redis.Password != "redis_password" {
		t.Errorf("Expected Password to be 'redis_password', got %q", redis.Password)
	}

	if redis.DB != 5 {
		t.Errorf("Expected DB to be 5, got %d", redis.DB)
	}
}

func TestHostname_Structure(t *testing.T) {
	config := Config{
		Hostname: "test-hostname.example.com",
	}

	if config.Hostname != "test-hostname.example.com" {
		t.Errorf("Expected Hostname to be 'test-hostname.example.com', got %q", config.Hostname)
	}
}

func TestConfigDefaults(t *testing.T) {
	// Test default values
	var config Config

	if config.App.RunMode != "" {
		t.Errorf("Expected default App.RunMode to be empty, got %q", config.App.RunMode)
	}

	if config.App.HttpPort != 0 {
		t.Errorf("Expected default App.HttpPort to be 0, got %d", config.App.HttpPort)
	}

	if config.Postgres.Port != 0 {
		t.Errorf("Expected default Postgres.Port to be 0, got %d", config.Postgres.Port)
	}

	if config.Redis.DB != 0 {
		t.Errorf("Expected default Redis.DB to be 0, got %d", config.Redis.DB)
	}
}

func TestConfigValidation(t *testing.T) {
	// Test configuration validation scenarios
	tests := []struct {
		name   string
		config Config
		valid  bool
	}{
		{
			name: "Valid configuration",
			config: Config{
				App: App{
					RunMode:  "release",
					HttpPort: 8080,
				},
				Postgres: Postgres{
					Host:     "localhost",
					Port:     5432,
					Database: "test_db",
				},
			},
			valid: true,
		},
		{
			name: "Invalid port (negative)",
			config: Config{
				App: App{
					RunMode:  "debug",
					HttpPort: -1,
				},
			},
			valid: false,
		},
		{
			name: "Invalid port (too high)",
			config: Config{
				App: App{
					RunMode:  "release",
					HttpPort: 70000,
				},
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation checks
			if tt.config.App.HttpPort < 0 || tt.config.App.HttpPort > 65535 {
				if tt.valid {
					t.Errorf("Configuration should be valid but port is out of range: %d", tt.config.App.HttpPort)
				}
			} else {
				if !tt.valid {
					t.Errorf("Configuration should be invalid but validation passed")
				}
			}
		})
	}
}

func TestConfigWithEnvironmentVariables(t *testing.T) {
	// Test configuration with environment variables
	originalEnv := os.Getenv("APP_PORT")
	defer func() {
		if originalEnv != "" {
			os.Setenv("APP_PORT", originalEnv)
		} else {
			os.Unsetenv("APP_PORT")
		}
	}()

	// Set environment variable
	os.Setenv("APP_PORT", "9090")

	// Test that environment variables can be used
	envPort := os.Getenv("APP_PORT")
	if envPort != "9090" {
		t.Errorf("Expected environment variable APP_PORT to be '9090', got %q", envPort)
	}

	// Test unsetting environment variable
	os.Unsetenv("APP_PORT")
	envPort = os.Getenv("APP_PORT")
	if envPort != "" {
		t.Errorf("Expected environment variable APP_PORT to be empty after unset, got %q", envPort)
	}
}

func TestConfigEdgeCases(t *testing.T) {
	// Test edge cases and boundary values
	
	// Test with maximum values
	config := Config{
		App: App{
			HttpPort: 65535, // Maximum valid port
		},
		Postgres: Postgres{
			Port: 65535,
		},
		Redis: Redis{
			DB: 15, // Redis typically supports 0-15 databases
		},
	}

	if config.App.HttpPort != 65535 {
		t.Errorf("Expected maximum port 65535, got %d", config.App.HttpPort)
	}

	// Test with minimum values
	config2 := Config{
		App: App{
			HttpPort: 1, // Minimum valid port
		},
		Postgres: Postgres{
			Port: 1,
		},
		Redis: Redis{
			DB: 0, // Minimum Redis DB
		},
	}

	if config2.App.HttpPort != 1 {
		t.Errorf("Expected minimum port 1, got %d", config2.App.HttpPort)
	}
}

// Benchmark tests
func BenchmarkConfigCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		config := Config{
			App: App{
				RunMode:  "release",
				HttpPort: 8080,
			},
			Postgres: Postgres{
				Host: "localhost",
				Port: 5432,
			},
			Redis: Redis{
				Addr: "localhost:6379",
			},
		}
		_ = config
	}
}

func BenchmarkConfigAccess(b *testing.B) {
	config := Config{
		App: App{
			RunMode:  "benchmark-app",
			HttpPort: 8080,
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = config.App.RunMode
		_ = config.App.HttpPort
		_ = config.Hostname
	}
}
