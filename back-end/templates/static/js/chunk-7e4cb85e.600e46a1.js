(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7e4cb85e"],{"157a":function(e,t,a){"use strict";a("3858")},"1e42":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)},r=[],o=(a("a481"),a("25ca")),l=a("21a6"),s=a.n(l),i={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var e=this.tableRef.$el,t=o["a"].table_to_book(e,{raw:!0}),a=o["b"](t,{bookType:"xlsx",bookSST:!0,type:"array"});try{var n="export-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";s.a.saveAs(new Blob([a],{type:"application/octet-stream"}),n)}catch(r){this.$message.error("导出失败, err: "+r.message),console.error(r)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=i,u=a("2877"),p=Object(u["a"])(c,n,r,!1,null,null,null);t["a"]=p.exports},"28a5":function(e,t,a){"use strict";var n=a("aae3"),r=a("cb7c"),o=a("ebd6"),l=a("0390"),s=a("9def"),i=a("5f1b"),c=a("520a"),u=a("79e5"),p=Math.min,d=[].push,m="split",f="length",h="lastIndex",b=4294967295,v=!u((function(){RegExp(b,"y")}));a("214f")("split",2,(function(e,t,a,u){var g;return g="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[f]||2!="ab"[m](/(?:ab)*/)[f]||4!="."[m](/(.?)(.?)/)[f]||"."[m](/()()/)[f]>1||""[m](/.?/)[f]?function(e,t){var r=String(this);if(void 0===e&&0===t)return[];if(!n(e))return a.call(r,e,t);var o,l,s,i=[],u=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),p=0,m=void 0===t?b:t>>>0,v=new RegExp(e.source,u+"g");while(o=c.call(v,r)){if(l=v[h],l>p&&(i.push(r.slice(p,o.index)),o[f]>1&&o.index<r[f]&&d.apply(i,o.slice(1)),s=o[0][f],p=l,i[f]>=m))break;v[h]===o.index&&v[h]++}return p===r[f]?!s&&v.test("")||i.push(""):i.push(r.slice(p)),i[f]>m?i.slice(0,m):i}:"0"[m](void 0,0)[f]?function(e,t){return void 0===e&&0===t?[]:a.call(this,e,t)}:a,[function(a,n){var r=e(this),o=void 0==a?void 0:a[t];return void 0!==o?o.call(a,r,n):g.call(String(r),a,n)},function(e,t){var n=u(g,e,this,t,g!==a);if(n.done)return n.value;var c=r(e),d=String(this),m=o(c,RegExp),f=c.unicode,h=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(v?"y":"g"),y=new m(v?c:"^(?:"+c.source+")",h),_=void 0===t?b:t>>>0;if(0===_)return[];if(0===d.length)return null===i(y,d)?[d]:[];var w=0,S=0,x=[];while(S<d.length){y.lastIndex=v?S:0;var k,O=i(y,v?d:d.slice(S));if(null===O||(k=p(s(y.lastIndex+(v?0:S)),d.length))===w)S=l(d,S,f);else{if(x.push(d.slice(w,S)),x.length===_)return x;for(var P=1;P<=O.length-1;P++)if(x.push(O[P]),x.length===_)return x;S=w=k}}return x.push(d.slice(w)),x}]}))},"2fdb":function(e,t,a){"use strict";var n=a("5ca1"),r=a("d2c8"),o="includes";n(n.P+n.F*a("5147")(o),"String",{includes:function(e){return!!~r(this,e,o).indexOf(e,arguments.length>1?arguments[1]:void 0)}})},3520:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-tabs",{on:{"tab-click":e.tabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"集群列表",name:"k8s-cluster",lazy:!0}},[a("k8s-cluster")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"宿主机资源使用",name:"cluster-resource",lazy:!0}},[a("cluster-resource")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"服务资源分配",name:"app-resource",lazy:!0}},[a("app-resource")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Pod状态",name:"pod-status",lazy:!0}},[a("pod-status")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"事件列表",name:"cluster-events",lazy:!0}},[a("k8s-event")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"镜像预热任务",name:"image-preheat-list",lazy:!0}},[a("image-preheat-list")],1)],1)],1)},r=[],o=(a("7f7f"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"K8S集群"}},[a("el-select",{attrs:{placeholder:"请选择一个集群",filterable:""},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"节点类型"}},[a("el-select",{attrs:{placeholder:"请选择节点类型"},model:{value:e.searchForm.nodeType,callback:function(t){e.$set(e.searchForm,"nodeType",t)},expression:"searchForm.nodeType"}},e._l(this.nodeOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),e._v(" "),a("el-table",{ref:"table001",attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","row-class-name":e.tableRowClassName,"default-sort":{prop:"cpu.requests",order:"descending"}}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"宿主机",sortable:"",prop:"name"}}),e._v(" "),a("el-table-column",{attrs:{label:"专属类型",align:"center",prop:"dedicatedName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:"info"}},[e._v(e._s(t.row.dedicatedName||"--"))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"CPU",align:"center"}},[a("el-table-column",{attrs:{label:"请求",align:"center",prop:"cpu.requests",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.requests,a.cpu.requests)}}}),e._v(" "),a("el-table-column",{attrs:{label:"请求百分比",align:"center",prop:"cpu.requestsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.requestsPercent,a.cpu.requestsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大",align:"center",prop:"cpu.limits",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.limits,a.cpu.limits)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大百分比",align:"center",prop:"cpu.limitsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.limitsPercent,a.cpu.limitsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"当前使用率",align:"center",prop:"cpu.utilizationPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.utilizationPercent,a.cpu.utilizationPercent)}}})],1),e._v(" "),a("el-table-column",{attrs:{label:"内存",align:"center"}},[a("el-table-column",{attrs:{label:"请求",align:"center",prop:"memory.requests",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.requests,a.memory.requests)}}}),e._v(" "),a("el-table-column",{attrs:{label:"请求百分比",align:"center",prop:"memory.requestsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.requestsPercent,a.memory.requestsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大",align:"center",prop:"memory.limits",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.limits,a.memory.limits)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大百分比",align:"center",prop:"memory.limitsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.limitsPercent,a.memory.limitsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"当前使用率",align:"center",prop:"memory.utilizationPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.utilizationPercent,a.memory.utilizationPercent)}}})],1),e._v(" "),a("el-table-column",{attrs:{label:"",fixed:"right",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return["所有"!==t.row.name?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.showPodCapacity(t.row.name)}}},[e._v("查看Pod\n        ")]):e._e()]}}])})],1),e._v(" "),a("el-drawer",{attrs:{withHeader:!1,visible:e.podCapacity.show,direction:"btt",size:"500"},on:{"update:visible":function(t){return e.$set(e.podCapacity,"show",t)}}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("宿主机："+e._s(e.podCapacity.node))])]),e._v(" "),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.podCapacity.loading,expression:"podCapacity.loading"}],attrs:{data:e.podCapacity.data,"element-loading-text":"Loading",border:"",height:"500",fit:"","highlight-current-row":"","default-sort":{prop:"cpu.requestsPercent",order:"descending"}}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"Pod",sortable:"",prop:"name"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境",sortable:"",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"CPU",align:"center"}},[a("el-table-column",{attrs:{label:"请求",align:"center",prop:"cpu.requests",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.requests,a.cpu.requests)}}}),e._v(" "),a("el-table-column",{attrs:{label:"请求百分比",align:"center",prop:"cpu.requestsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.requestsPercent,a.cpu.requestsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大",align:"center",prop:"cpu.limits",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.limits,a.cpu.limits)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大百分比",align:"center",prop:"cpu.limitsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.limitsPercent,a.cpu.limitsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"当前使用率",align:"center",prop:"cpu.utilizationPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.utilizationPercent,a.cpu.utilizationPercent)}}})],1),e._v(" "),a("el-table-column",{attrs:{label:"内存",align:"center"}},[a("el-table-column",{attrs:{label:"请求",align:"center",prop:"memory.requests",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.requests,a.memory.requests)}}}),e._v(" "),a("el-table-column",{attrs:{label:"请求百分比",align:"center",prop:"memory.requestsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.requestsPercent,a.memory.requestsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大",align:"center",prop:"memory.limits",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.limits,a.memory.limits)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大百分比",align:"center",prop:"memory.limitsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.limitsPercent,a.memory.limitsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"当前使用率",align:"center",prop:"cpu.utilizationPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.utilizationPercent,a.memory.utilizationPercent)}}})],1)],1)],1)])],1)],1)}),l=[],s=(a("aef6"),a("2d63")),i=a("b775");function c(e){return Object(i["a"])({url:"/v1/k8s/cluster/capacity",method:"get",params:e})}function u(e){return Object(i["a"])({url:"/v1/k8s/cluster/pod/capacity",method:"get",params:e})}function p(e){return Object(i["a"])({url:"/v1/k8s/image/preheat",method:"post",data:e})}function d(e,t){return Object(i["a"])({url:"/v1/k8s/image/preheat/jobs?cluster=".concat(e,"&namespace=").concat(t),method:"get"})}function m(e,t,a){return Object(i["a"])({url:"/v1/k8s/image/preheat/jobs?cluster=".concat(e,"&namespace=").concat(t,"&job=").concat(a),method:"delete"})}var f=a("1e42"),h={name:"clusterResource",data:function(){return{loading:!1,tableData:[],searchForm:{cluster:"",nodeType:""},podCapacity:{show:!1,loading:!1,node:"",data:[]}}},components:{ExportButton:f["a"]},computed:{clusterOptions:function(){return this.$settings.clusters},nodeOptions:function(){if(this.searchForm.cluster)for(var e in this.$settings.clusters){var t=this.$settings.clusters[e];if(this.searchForm.cluster===t.name){var a={name:"全部",value:""},n=JSON.parse(JSON.stringify(t.nodes));return n.splice(0,0,a),console.log(n),n}}return[]}},mounted:function(){},methods:{loadTableData:function(){var e=this;this.searchForm.cluster&&(this.loading=!0,c(this.searchForm).then((function(t){e.tableData=t.data.nodes||[];var a=t.data.clusterTotals;a&&(a["name"]="所有",e.tableData.unshift(a));var n,r=Object(s["a"])(e.tableData);try{for(r.s();!(n=r.n()).done;){var o=n.value;e.rowValueHandler(o)}}catch(l){r.e(l)}finally{r.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1})))},tableRowClassName:function(e){var t=e.row;e.rowIndex;return"所有"===t.name?"bold-row":""},pipelinePage:function(e){this.$router.push({name:"cicd-app-deploy",query:{app:e.app}})},rowValueHandler:function(e){function t(e){return/^\d+$/.test(e)?1e3*parseInt(e)+"m":e}function a(e){return/^\d+$/.test(e)?Math.floor(parseInt(e)/1024/1024)+"Mi":e.endsWith("Ki")||e.endsWith("ki")?Math.floor(parseInt(e)/1024)+"Mi":e}e["cpu"]["requests"]=t(e["cpu"]["requests"]),e["cpu"]["limits"]=t(e["cpu"]["limits"]),e["memory"]["requests"]=a(e["memory"]["requests"]),e["memory"]["limits"]=a(e["memory"]["limits"])},showPodCapacity:function(e){var t=this;if(e){this.podCapacity.node=e,this.podCapacity.loading=!0,this.podCapacity.show=!0;var a={cluster:this.searchForm.cluster,node:e};u(a).then((function(e){t.podCapacity.data=e.data.nodes[0].pods})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.podCapacity.loading=!1}))}else this.$message.error("请选择一个宿主机")},strValueSort:function(e,t){return parseInt(e)-parseInt(t)},podPage:function(e){var t={cluster:e.cluster,namespace:e.namespace,app:e.app};this.$router.push({name:"pod-index",query:t})}}},b=h,v=(a("157a"),a("2877")),g=Object(v["a"])(b,o,l,!1,null,null,null),y=g.exports,_=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"集群"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:e.clusterChange},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",filterable:""},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},[e._l(e.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),e._v(" "),a("el-option",{key:"*",attrs:{label:"所有",value:"*"}})],2)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}}),e._v(" "),a("div",{staticStyle:{display:"inline-block","margin-left":"60px","font-size":"12px"}},[e._v("\n        显示：\n        "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.appRemark,callback:function(t){e.$set(e.showFields,"appRemark",t)},expression:"showFields.appRemark"}},[e._v("服务描述")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.appOwner,callback:function(t){e.$set(e.showFields,"appOwner",t)},expression:"showFields.appOwner"}},[e._v("服务负责人")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.appLevel,callback:function(t){e.$set(e.showFields,"appLevel",t)},expression:"showFields.appLevel"}},[e._v("服务等级")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},on:{change:e.onShowDiffReplicasChange},model:{value:e.showFields.onlyShowDiffReplicas,callback:function(t){e.$set(e.showFields,"onlyShowDiffReplicas",t)},expression:"showFields.onlyShowDiffReplicas"}},[e._v("只显示副本差异数据")])],1)],1)],1),e._v(" "),a("el-table",{ref:"table001",attrs:{data:e.tableDataFilters,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","show-summary":"","default-sort":{prop:"requestMemoryTotal",order:"descending"}}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用",sortable:"",prop:"name"}}),e._v(" "),e.showFields.appLevel?a("el-table-column",{attrs:{label:"等级",prop:"appLevel",width:"80px"}}):e._e(),e._v(" "),e.showFields.appRemark?a("el-table-column",{attrs:{label:"应用描述",prop:"appRemark"}}):e._e(),e._v(" "),e.showFields.appOwner?a("el-table-column",{attrs:{label:"应用负责人",prop:"appOwner"}}):e._e(),e._v(" "),a("el-table-column",{attrs:{label:"环境",prop:"namespace",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"变动时间",sortable:"",prop:"updateTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("span",[e._v("C: "+e._s(t.row.createTime))]),a("br"),e._v(" "),a("span",[e._v("U: "+e._s(t.row.updateTime))])])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行副本",sortable:"",align:"center",width:"110px",prop:"replicas"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("b",[e._v(e._s(t.row.replicas))]),a("br"),e._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{slot:"reference",type:"text",size:"mini"},on:{click:function(a){return e.replicasEditDialog(t.row.cluster,t.row.namespace,t.row.name,t.row.replicas)}},slot:"reference"},[e._v("修改\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"发布流程副本",align:"center",width:"110px",prop:"pipelineReplicas"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.pipelineReplicas>=0?a("b",[e._v(e._s(t.row.pipelineReplicas))]):a("span",[e._v("--")]),e._v(" "),a("br"),e._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{slot:"reference",size:"mini",type:"text"},on:{click:function(a){return e.pipelinePage(t.row.name)}},slot:"reference"},[e._v("流程页\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"CPU （单位：core）",align:"center"}},[a("el-table-column",{attrs:{label:"分配总计",width:"160",align:"center",prop:"requestCpuTotal",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("b",{staticStyle:{color:"#01AAED"}},[e._v(e._s(t.row.requestCpuTotal))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"分配",width:"100",align:"center",prop:"requestCpu",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"最高",width:"100",align:"center",prop:"limitCpu",sortable:""}})],1),e._v(" "),a("el-table-column",{attrs:{label:"内存（单位：MiB）",align:"center"}},[a("el-table-column",{attrs:{label:"分配总计",width:"160",align:"center",prop:"requestMemoryTotal",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("b",{staticStyle:{color:"#01AAED"}},[e._v(e._s(t.row.requestMemoryTotal))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"分配",width:"100",align:"center",prop:"requestMemory",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"最高",width:"100",align:"center",prop:"limitMemory",sortable:""}})],1),e._v(" "),a("el-table-column",{attrs:{width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("router-link",{attrs:{to:{name:"pod-index",query:{cluster:t.row.cluster,namespace:t.row.namespace,app:t.row.name}},target:"_blank"}},[a("i",{staticClass:"el-icon-menu",staticStyle:{color:"#409EFF","font-weight":"500"}},[e._v("实例管理")])])]}}])})],1),e._v(" "),a("div",[a("el-backtop")],1),e._v(" "),a("el-dialog",{attrs:{title:"修改实例数",visible:e.replicasEditVisible,width:"500px"},on:{"update:visible":function(t){e.replicasEditVisible=t}}},[a("el-form",{attrs:{"label-position":"right","label-width":"80px"}},[a("el-form-item",{attrs:{label:"集群"}},[a("el-input",{attrs:{disabled:""},model:{value:e.replicasEdit.cluster,callback:function(t){e.$set(e.replicasEdit,"cluster",t)},expression:"replicasEdit.cluster"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-input",{attrs:{disabled:""},model:{value:e.replicasEdit.namespace,callback:function(t){e.$set(e.replicasEdit,"namespace",t)},expression:"replicasEdit.namespace"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"应用"}},[a("el-input",{attrs:{disabled:""},model:{value:e.replicasEdit.app,callback:function(t){e.$set(e.replicasEdit,"app",t)},expression:"replicasEdit.app"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"实例数"}},[a("el-input-number",{attrs:{min:0,max:50},model:{value:e.replicasEdit.replicas,callback:function(t){e.$set(e.replicasEdit,"replicas",t)},expression:"replicasEdit.replicas"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.replicasEditVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",loading:e.replicasEditLoading},on:{click:e.replicasEditSubmit}},[e._v("确 定")])],1)],1)],1)},w=[],S=a("8504"),x=a("b562"),k={name:"appResource",mounted:function(){this.loadTableData()},components:{ExportButton:f["a"]},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(s["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(n){t.e(n)}finally{t.f()}}return[]}},data:function(){return{loading:!1,searchEnv:"",showFields:{appRemark:!1,appOwner:!1,appLevel:!1,onlyShowDiffReplicas:!1},searchForm:{cluster:"",namespace:"",keyword:""},tableData:[],tableDataFilters:[],replicasEditLoading:!1,replicasEditVisible:!1,replicasEdit:{cluster:"",namespace:"",app:"",replicas:1}}},methods:{loadTableData:function(){var e=this;this.searchForm.cluster&&this.searchForm.namespace&&(this.loading=!0,Object(S["g"])(this.searchForm.cluster,this.searchForm.namespace).then((function(t){var a,n=t.data,r=Object(s["a"])(n);try{for(r.s();!(a=r.n()).done;){var o=a.value;o.cluster=e.searchForm.cluster,o.limitCpu=(o.limitCpu/1e3).toFixed(1),o.requestCpu=(o.requestCpu/1e3).toFixed(1),o.limitMemory=Math.floor(o.limitMemory/1024/1024),o.requestMemory=Math.floor(o.requestMemory/1024/1024),o.requestCpuTotal=(o.replicas*o.requestCpu).toFixed(1),o.requestMemoryTotal=Math.floor(o.replicas*o.requestMemory),o.appOwner="-",o.appRemark="-",o.appLevel="-"}}catch(l){r.e(l)}finally{r.f()}e.tableData=n,e.tableDataFilters=e.tableData,e.appendAppInfo()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1})))},clusterChange:function(){this.searchForm.namespace=""},onShowDiffReplicasChange:function(e){this.tableDataFilters=!0===e?this.tableData.filter((function(e){return e.replicas!==e.pipelineReplicas})):this.tableData.filter((function(e){return e.replicas===e.pipelineReplicas}))},replicasEditDialog:function(e,t,a,n){this.replicasEdit={cluster:e,namespace:t,app:a,replicas:n},this.replicasEditVisible=!0},appendAppInfo:function(){var e=this;Object(x["a"])().then((function(t){var a,n={},r=Object(s["a"])(t.data);try{for(r.s();!(a=r.n()).done;){var o=a.value;n[o["name"]]=o}}catch(p){r.e(p)}finally{r.f()}var l,i=Object(s["a"])(e.tableData);try{for(i.s();!(l=i.n()).done;){var c=l.value,u=n[c.name];u&&(c.appRemark=u.remark,c.appOwner=u.owner,c.appLevel=u.level,c.createTime=u.createTime)}}catch(p){i.e(p)}finally{i.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){}))},replicasEditSubmit:function(){var e=this;this.replicasEditLoading=!0,Object(S["f"])(this.replicasEdit).then((function(t){e.$message.success("操作成功");var a,n=Object(s["a"])(e.tableData);try{for(n.s();!(a=n.n()).done;){var r=a.value;r.cluster===e.replicasEdit.cluster&&r.namespace===e.replicasEdit.namespace&&r.name===e.replicasEdit.app&&(r.replicas=e.replicasEdit.replicas)}}catch(o){n.e(o)}finally{n.f()}e.replicasEditVisible=!1})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.replicasEditLoading=!1}))},grafanaPage:function(e){var t="/api/page/redirect?type=grafana&app=".concat(e.name,"&namespace=").concat(e.namespace,"&pod=");window.open(t)},pipelinePage:function(e){var t=this.$router.resolve({name:"cicd-app-deploy",query:{app:e}});window.open(t.href,"_blank")}}},O=k,P=Object(v["a"])(O,_,w,!1,null,null,null),j=P.exports,T=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",[a("env-selector-form",{attrs:{"show-all-namespaces":!1,display:"inline"},on:{submitHandler:e.loadTableData}}),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{attrs:{data:this.tableData,"row-key":"name","default-sort":{prop:"statusDesc",order:"ascending"}}},[a("el-table-column",{attrs:{label:"名称",prop:"name",width:"360",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"状态",sortable:"","sort-by":"statusDesc",prop:"statusDesc",filters:e.statusFilterData,"filter-method":e.filterStatus},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e.podStatusClass(t.row.statusDesc)}),e._v("\n          "+e._s(t.row.statusDesc)+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行版本",prop:"deployTag","show-overflow-tooltip":"","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{label:"重启数/最近重启",prop:"restartCount",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(e._s(t.row.restartCount))]),e._v(" "),t.row.restartCount>0?a("div",{staticStyle:{"font-size":"12px",color:"#888"}},[e._v(e._s(t.row.restartTime))]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",sortable:"",prop:"createTime","min-width":"110"}}),e._v(" "),a("el-table-column",{attrs:{width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"el-icon-menu",attrs:{type:"text"},on:{click:function(a){return e.podPage(t.row)}}},[e._v("实例管理\n          ")])]}}])})],1)],1)],1)},C=[],D=(a("6762"),a("2fdb"),a("a527")),F=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"env-selector-form",style:{display:e.display}},[a("el-form",{staticClass:"demo-form-inline",style:{display:e.display},attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"k8s集群",prop:"cluster"}},[a("el-select",{attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:e.clusterChange},model:{value:e.cluster,callback:function(t){e.cluster=t},expression:"cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[a("el-select",{attrs:{placeholder:"选择namespace",filterable:""},model:{value:e.namespace,callback:function(t){e.namespace=t},expression:"namespace"}},[e.showAllNamespaces?a("el-option",{key:"*",attrs:{label:"所有",value:""}}):e._e(),e._v(" "),e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确认")])],1)],1)},$=[],E={name:"EnvSelectorForm",props:{showAllNamespaces:{type:Boolean,default:!1},display:{type:String,default:"block"}},data:function(){return{cluster:"",namespace:""}},mounted:function(){!this.cluster&&this.clusterOptions&&this.clusterOptions.length&&(this.cluster=this.clusterOptions[0].name),this.cluster&&!this.namespace&&this.namespaceOptions&&this.namespaceOptions.length&&(this.namespace=this.namespaceOptions[0])},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.cluster){var e,t=Object(s["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.cluster===a.name)return a.namespaces}}catch(n){t.e(n)}finally{t.f()}}return[]}},methods:{clusterChange:function(){this.namespace=""},submit:function(){this.$emit("submitHandler",this.cluster,this.namespace)}}},R=E,V=Object(v["a"])(R,F,$,!1,null,null,null),q=V.exports,L={name:"podStatus",data:function(){return{loading:!1,statusFilterData:[{text:"调度中",value:"调度中"},{text:"准备中",value:"准备中"},{text:"启动中",value:"启动中"},{text:"运行中",value:"运行中"},{text:"关闭中",value:"关闭中"},{text:"容器关闭中",value:"容器关闭中"},{text:"不健康",value:"不健康"},{text:"已关闭",value:"已关闭"},{text:"未知状态",value:"未知状态"}],tableData:[]}},components:{ExportButton:f["a"],EnvSelectorForm:q},mounted:function(){},computed:{},methods:{loadTableData:function(e,t){var a=this;this.loading=!0,Object(D["i"])(e,t).then((function(e){var t,n=[],r=Object(s["a"])(e.data);try{for(r.s();!(t=r.n()).done;){var o=t.value;"已关闭"!==o.statusDesc&&n.push(o)}}catch(l){r.e(l)}finally{r.f()}a.tableData=n})).catch((function(e){a.$message.error(e.message)})).finally((function(){a.loading=!1}))},podPage:function(e){var t=this.$router.resolve({name:"pod-index",query:{cluster:e.cluster,namespace:e.namespace,app:e.labelApp}});window.open(t.href,"_blank")},filterStatus:function(e,t){return t.statusDesc===e},podStatusClass:function(e){if(e){if("运行中"===e)return"pod-status-green";if(["调度中","准备中","启动中"].includes(e))return"pod-status-orange"}return"pod-status-red"}}},I=L,z=(a("8d4f"),Object(v["a"])(I,T,C,!1,null,null,null)),N=z.exports,M=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{"font-size":"12px"}},[a("b",[e._v("名称列表：")]),e._v(" "),e._l(this.clusters,(function(t,n){return a("span",[e._v(e._s(t.name)+"、")])}))],2),e._v(" "),a("div",{staticStyle:{"font-size":"12px","margin-bottom":"10px"}},[a("b",[e._v("描述列表：")]),e._v(" "),e._l(this.clusters,(function(t,n){return a("span",[e._v(e._s(t.description)+"、")])}))],2),e._v(" "),a("div",{staticStyle:{position:"absolute",right:"0",top:"0"}},[a("div",{staticStyle:{display:"inline-block","margin-right":"10px","font-size":"12px"}},[e._v("\n      显示：\n      "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.autoScale,callback:function(t){e.$set(e.showFields,"autoScale",t)},expression:"showFields.autoScale"}},[e._v("自动扩缩容")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.autoScaleV2,callback:function(t){e.$set(e.showFields,"autoScaleV2",t)},expression:"showFields.autoScaleV2"}},[e._v("自动扩缩容V2")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.cronScale,callback:function(t){e.$set(e.showFields,"cronScale",t)},expression:"showFields.cronScale"}},[e._v("定时扩缩容")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.imageRegistryProxy,callback:function(t){e.$set(e.showFields,"imageRegistryProxy",t)},expression:"showFields.imageRegistryProxy"}},[e._v("镜像代理库")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.cloudCategory,callback:function(t){e.$set(e.showFields,"cloudCategory",t)},expression:"showFields.cloudCategory"}},[e._v("云类型")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.namespaces,callback:function(t){e.$set(e.showFields,"namespaces",t)},expression:"showFields.namespaces"}},[e._v("命名空间")])],1),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{ref:"table001",attrs:{data:this.clusters,"row-key":"name"}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.image_preheat_dialog(t.row.name)}}},[e._v("镜像预热")]),e._v(" "),a("div",{staticStyle:{color:"#555","font-size":"12px"}},[e._v("job数："+e._s(t.row.imagePreheatJobs))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"集群名",prop:"name"}}),e._v(" "),a("el-table-column",{attrs:{label:"集群描述",prop:"description"}}),e._v(" "),a("el-table-column",{attrs:{label:"版本号",prop:"version"}}),e._v(" "),a("el-table-column",{attrs:{label:"Node VIP",prop:"nodeVIP"}}),e._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"disable",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.enable?a("div",{staticStyle:{color:"#14b614","font-weight":"bold"}},[e._v("开启")]):a("div",{staticStyle:{color:"red"}},[e._v("关闭")])]}}])}),e._v(" "),e.showFields.autoScale?a("el-table-column",{attrs:{label:"自动扩缩",prop:"autoScale",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.autoScale?a("div",{staticStyle:{color:"#14b614","font-weight":"bold"}},[e._v("开启")]):a("div",{staticStyle:{color:"red"}},[e._v("关闭")])]}}],null,!1,2322677043)}):e._e(),e._v(" "),e.showFields.autoScaleV2?a("el-table-column",{attrs:{label:"自动扩缩V2",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["true"===t.row.autoScaleV2?a("div",{staticStyle:{color:"#14b614","font-weight":"bold"}},[e._v("开启")]):"false"===t.row.autoScaleV2?a("div",{staticStyle:{color:"red"}},[e._v("关闭")]):a("div",[e._v(e._s(t.row.autoScaleV2))])]}}],null,!1,1078303857)}):e._e(),e._v(" "),e.showFields.cronScale?a("el-table-column",{attrs:{label:"定时扩缩",prop:"cronScale",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.cronScale?a("div",{staticStyle:{color:"#14b614","font-weight":"bold"}},[e._v("开启")]):a("div",{staticStyle:{color:"red"}},[e._v("关闭")])]}}],null,!1,1534277068)}):e._e(),e._v(" "),e.showFields.imageRegistryProxy?a("el-table-column",{attrs:{label:"镜像库代理",prop:"imageRegistryProxy"}}):e._e(),e._v(" "),e.showFields.cloudCategory?a("el-table-column",{attrs:{label:"云类型",prop:"cloudCategory"}}):e._e(),e._v(" "),e.showFields.namespaces?a("el-table-column",{attrs:{label:"命名空间",prop:"namespaces"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.namespaces)+"\n        ")]}}],null,!1,263394605)}):e._e()],1)],1),e._v(" "),a("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.imagePreheatLoading,expression:"imagePreheatLoading"}],attrs:{title:"镜像预热",visible:e.imagePreheat.dialogVisible},on:{"update:visible":function(t){return e.$set(e.imagePreheat,"dialogVisible",t)}}},[a("el-form",{attrs:{"label-width":"100px"}},[a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"集群名"}},[e._v("\n        "+e._s(e.imagePreheat.cluster)+"\n      ")]),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"预热并发数"}},[e._v("\n            使用\n        "),a("el-input-number",{staticStyle:{width:"120px"},attrs:{"controls-position":"right",min:1,max:5,step:1},model:{value:e.imagePreheat.imagePullParallel,callback:function(t){e.$set(e.imagePreheat,"imagePullParallel",t)},expression:"imagePreheat.imagePullParallel"}}),e._v("\n        个并发数来同时拉取镜像\n      ")],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"应用镜像"}},[e._v("\n        对最近\n        "),a("el-input-number",{staticStyle:{width:"120px"},attrs:{"controls-position":"right",min:0,max:480,step:24},model:{value:e.imagePreheat.appImageRecentHours,callback:function(t){e.$set(e.imagePreheat,"appImageRecentHours",t)},expression:"imagePreheat.appImageRecentHours"}}),e._v("\n        小时内构建的应用镜像进行预热\n      ")],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"命名空间"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择命名空间"},model:{value:e.imagePreheat.namespaces,callback:function(t){e.$set(e.imagePreheat,"namespaces",t)},expression:"imagePreheat.namespaces"}},e._l(["foneshare-gray","foneshare-stage","foneshare-urgent","foneshare"],(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"基础镜像"}},[a("el-input",{attrs:{type:"textarea",rows:8,placeholder:"镜像全称、按行分割"},model:{value:e.imagePreheat.baseImages,callback:function(t){e.$set(e.imagePreheat,"baseImages",t)},expression:"imagePreheat.baseImages"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.imagePreheat.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.image_preheat()}}},[e._v("确 定")])],1)],1)],1)},A=[],H=(a("28a5"),a("b144")),J=a("bb0b"),B={name:"k8sCluster",data:function(){return{imagePreheatLoading:!1,clusters:[],showFields:{autoScale:!0,autoScaleV2:!0,cronScale:!0,cloudCategory:!1,namespaces:!1,imageRegistryProxy:!0},imagePreheat:{dialogVisible:!1,cluster:"",baseImages:"",namespaces:["foneshare-gray","foneshare-stage"],imagePullParallel:1,appImageRecentHours:240}}},components:{ExportButton:f["a"]},mounted:function(){var e,t=this,a=Object(s["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n=e.value,r=Object(H["a"])(n);r["imagePreheatJobs"]="--",r["autoScaleV2"]="--",this.clusters.push(r),this.image_preheat_jobs(r.name)}}catch(o){a.e(o)}finally{a.f()}this.init_base_images(),Object(J["h"])().then((function(e){var a,n=Object(s["a"])(t.clusters);try{for(n.s();!(a=n.n()).done;){var r=a.value;e.data&&(e.data[r.name]?r["autoScaleV2"]="true":r["autoScaleV2"]="false")}}catch(o){n.e(o)}finally{n.f()}})).catch((function(e){console.error(e.message)})).finally((function(){}))},computed:{},methods:{image_preheat_dialog:function(e){this.imagePreheat.cluster=e,this.imagePreheat.dialogVisible=!0},init_base_images:function(){var e=this.clusters.filter((function(e){return"k8s0"===e.name}));e.length>0&&(this.imagePreheat.baseImages=e[0].baseImages.filter((function(e){return e.includes("tomcat8:")||e.includes("tomcat9:")})).join("\n"))},image_preheat_jobs:function(e){var t=this;d(e,"kube-public").then((function(a){var n,r=Object(s["a"])(t.clusters);try{for(r.s();!(n=r.n()).done;){var o=n.value;o.name===e&&(o.imagePreheatJobs="".concat(a.data.length))}}catch(l){r.e(l)}finally{r.f()}})).catch((function(e){console.error(e.message)})).finally((function(){}))},image_preheat:function(){var e=this;this.$confirm("镜像预热时可能会占用较多的网络带宽，确认继续吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.imagePreheatLoading=!0;var t={cluster:e.imagePreheat.cluster,baseImages:e.imagePreheat.baseImages.split("\n"),namespaces:e.imagePreheat.namespaces,imagePullParallel:e.imagePreheat.imagePullParallel,appImageRecentHours:e.imagePreheat.appImageRecentHours};p(t).then((function(t){e.$message.success("".concat(e.imagePreheat.cluster,": 已成功触发镜像预热任务，详情进度请查看集预热服务日志")),e.imagePreheat.dialogVisible=!1})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.imagePreheatLoading=!1}))})).catch((function(){}))}}},K=B,W=Object(v["a"])(K,M,A,!1,null,null,null),U=W.exports,G=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",[a("env-selector-form",{attrs:{"show-all-namespaces":!0},on:{submitHandler:e.loadEvents}})],1),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-timeline",e._l(e.events,(function(t,n){return a("el-timeline-item",{key:n,attrs:{placement:"top","hide-timestamp":!0,timestamp:t.lastTime?t.lastTime:t.createTime}},[a("div",[a("span",[e._v(e._s(t.lastTime?t.lastTime:t.createTime))]),e._v(" "),a("el-tag",{staticStyle:{margin:"0 10px"},attrs:{size:"small",type:"Warning"===t.type?"warning":"info"}},[e._v(e._s(t.type))]),e._v(" "),e.showNamespace?a("span",{staticStyle:{padding:"0 10px"}},[e._v(e._s(t.namespace))]):e._e(),e._v(" "),a("span",{staticStyle:{padding:"0 10px"}},[e._v(" ( x"+e._s(t.count)+" )")]),e._v(" "),a("span",{staticStyle:{"padding-right":"10px"}},[e._v(e._s(t.reason)+":")]),e._v(" "+e._s(t.message)+"\n        ")],1)])})),1)],1)],1)},Y=[],Q=a("655a"),X={name:"k8s-event",data:function(){return{loading:!1,showNamespace:!1,events:[]}},components:{EnvSelectorForm:q},mounted:function(){},computed:{},methods:{loadEvents:function(e,t){var a=this;this.loading=!0,this.showNamespace=null===t||""===t,Object(Q["a"])(e,t).then((function(e){a.events=e.data})).catch((function(e){a.$message.error(e.message)})).finally((function(){a.loading=!1}))}}},Z=X,ee=Object(v["a"])(Z,G,Y,!1,null,null,null),te=ee.exports,ae=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",e._l(e.clusterOptions,(function(t){return a("el-col",{key:t.name,attrs:{span:e.colSpan}},[a("image-preheat",{attrs:{cluster:t.name}})],1)})),1)],1)},ne=[],re=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"image-preheat"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(this.cluster))])]),e._v(" "),a("div",[a("el-table",{attrs:{data:this.tableData,"row-key":"name",size:"small"}},[a("el-table-column",{attrs:{label:"名称",prop:"metadata.name"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{attrs:{title:"点击查看yaml配置"}},[a("el-link",{staticStyle:{"font-size":"12px",margin:"0",padding:"0",color:"#3a8ee6"},attrs:{type:"text"},on:{click:function(a){return e.podDetailDialog(t.row.metadata.name)}}},[e._v("\n                "+e._s(t.row.metadata.name)+"\n              ")])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status.phase",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["Succeeded"===t.row.status.phase?a("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"success",size:"mini"}},[e._v("\n              "+e._s(t.row.status.phase)+"\n            ")]):a("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",size:"small"}},[e._v("\n              "+e._s(t.row.status.phase)+"\n            ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"metadata.creationTimestamp",width:"90"}}),e._v(" "),a("el-table-column",{attrs:{label:"",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.podStdoutLog(t.row.metadata.name)}}},[e._v("日志")]),e._v(" "),a("el-button",{staticStyle:{color:"#E6A23C"},attrs:{type:"text"},on:{click:function(a){return e.jobRemove(t.row.metadata.labels["job-name"])}}},[e._v("删除")])]}}])})],1)],1)]),e._v(" "),a("el-dialog",{attrs:{title:"容器启动日志（标准输出)",visible:e.podStdoutVisible,top:"5vh","close-on-click-modal":!1,width:"70%",center:""},on:{"update:visible":function(t){e.podStdoutVisible=t},close:function(t){e.podStdout.pod=null}}},[a("div",{staticStyle:{"margin-top":"-30px"}},[a("pod-stdout",{attrs:{cluster:this.podStdout.cluster,namespace:this.podStdout.namespace,pod:this.podStdout.pod,containers:this.podStdout.containers}})],1)]),e._v(" "),a("el-dialog",{attrs:{title:e.podDetail.name,visible:e.podDetailVisible,top:"5vh"},on:{"update:visible":function(t){e.podDetailVisible=t}}},[a("div",{staticStyle:{margin:"5px auto","text-align":"center"}},[a("clipboard-icon",{attrs:{text:e.podDetail.json}},[e._v("一键复制")])],1),e._v(" "),a("vue-json-pretty",{directives:[{name:"loading",rawName:"v-loading",value:e.podDetailLoading,expression:"podDetailLoading"}],staticStyle:{"max-height":"600px","overflow-y":"auto"},attrs:{data:e.podDetail.data}})],1)],1)},oe=[],le=a("cf89"),se=a("d538"),ie=a.n(se),ce=a("da37"),ue={name:"imagePreheat",props:{cluster:{type:String,required:!0},namespace:{type:String,default:"kube-public"}},data:function(){return{loading:!1,tableData:[],podStdoutVisible:!1,podStdout:{cluster:"",namespace:"",pod:"",containers:[]},podDetailVisible:!1,podDetailLoading:!1,podDetail:{name:"",data:{},json:""}}},components:{ClipboardIcon:ce["a"],PodStdout:le["a"],VueJsonPretty:ie.a},mounted:function(){this.loadTable()},methods:{loadTable:function(){var e=this;this.loading=!0,d(this.cluster,this.namespace).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))},podDetailDialog:function(e){var t=this;this.podDetailVisible=!0,this.podDetail.name===e&&this.podDetail.data||(this.podDetailLoading=!0,Object(D["e"])(this.cluster,this.namespace,e).then((function(a){t.podDetailLoading=!1,t.podDetail.name=e,t.podDetail.data=a.data,t.podDetail.json=JSON.stringify(t.podDetail.data,null,2)})).catch((function(e){t.$message.error(e.message),t.podDetailLoading=!1})))},podStdoutLog:function(e){this.podStdoutVisible=!0,this.podStdout.cluster=this.cluster,this.podStdout.namespace=this.namespace,this.podStdout.pod=e,this.podStdout.containers=[]},jobRemove:function(e){var t=this;this.$confirm("是否确认删除job：".concat(e," ?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,m(t.cluster,t.namespace,e).then((function(e){t.$message.success("操作成功,2秒后将刷新数据");var a=t;setTimeout((function(){a.loadTable()}),3e3)})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))})).catch((function(e){}))}}},pe=ue,de=(a("88f6"),Object(v["a"])(pe,re,oe,!1,null,null,null)),me=de.exports,fe={name:"imagePreheatList",data:function(){return{span:8}},components:{ImagePreheat:me},mounted:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},colSpan:function(){return this.clusterOptions.length>6?8:12}},methods:{}},he=fe,be=Object(v["a"])(he,ae,ne,!1,null,null,null),ve=be.exports,ge={components:{ImagePreheatList:ve,K8sEvent:te,K8sCluster:U,PodStatus:N,ClusterResource:y,AppResource:j},mounted:function(){var e=this.$route.query.activeTab;e&&(this.activeTab=e)},computed:{},data:function(){return{activeTab:"k8s-cluster"}},methods:{tabClick:function(e){var t=Object(H["a"])(this.$route.query);t["activeTab"]=e.name,this.$router.push({query:t})}}},ye=ge,_e=Object(v["a"])(ye,n,r,!1,null,null,null);t["default"]=_e.exports},3858:function(e,t,a){},"3c75":function(e,t,a){},5147:function(e,t,a){var n=a("2b4c")("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[n]=!1,!"/./"[e](t)}catch(r){}}return!0}},"655a":function(e,t,a){"use strict";a.d(t,"a",(function(){return r}));var n=a("b775");function r(e,t,a,r){return Object(n["a"])({url:"/v1/k8s/event/search",method:"get",params:{cluster:e,namespace:t,type:a||"",reason:r||""}})}},6762:function(e,t,a){"use strict";var n=a("5ca1"),r=a("c366")(!0);n(n.P,"Array",{includes:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},8504:function(e,t,a){"use strict";a.d(t,"g",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"h",(function(){return l})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"i",(function(){return c})),a.d(t,"d",(function(){return u})),a.d(t,"f",(function(){return p})),a.d(t,"e",(function(){return d}));var n=a("b775");function r(e,t){return Object(n["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:e,namespace:t}})}function o(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:e,namespace:t,app:a}})}function l(e){return Object(n["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:e})}function s(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:e,namespace:t,app:a}})}function i(e){return Object(n["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:e})}function c(e){return Object(n["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:e})}function u(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function p(e){return Object(n["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:e})}function d(e,t,a,r,o){return Object(n["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:e,namespace:t,app:a,revision:r,deployTag:o||""}})}},"88f6":function(e,t,a){"use strict";a("3c75")},"8d4f":function(e,t,a){"use strict";a("a4d0")},a4d0:function(e,t,a){},a527:function(e,t,a){"use strict";a.d(t,"h",(function(){return r})),a.d(t,"d",(function(){return o})),a.d(t,"i",(function(){return l})),a.d(t,"e",(function(){return s})),a.d(t,"g",(function(){return i})),a.d(t,"c",(function(){return c})),a.d(t,"j",(function(){return u})),a.d(t,"k",(function(){return p})),a.d(t,"l",(function(){return d})),a.d(t,"n",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"q",(function(){return h})),a.d(t,"b",(function(){return b})),a.d(t,"a",(function(){return v})),a.d(t,"o",(function(){return g})),a.d(t,"p",(function(){return y})),a.d(t,"m",(function(){return _}));a("96cf"),a("3b8d");var n=a("b775");function r(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function o(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}function l(e,t){return Object(n["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:e,namespace:t}})}function s(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:e,namespace:t,pod:a}})}function i(e,t,a,r,o,l){return Object(n["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:e,namespace:t,pod:a,container:r,tailLines:o,previous:l}})}function c(e,t,a,n,r){var o="/api/v1/k8s/pod/stdout/download?cluster=".concat(e,"&namespace=").concat(t,"&pod=").concat(a,"&container=").concat(n,"&tailLines=").concat(r,'&_time="')+(new Date).getTime();window.open(o)}function u(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:e,namespace:t,pod:a}})}function p(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:e,namespace:t,pod:a}})}function d(e){return Object(n["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:e}})}function m(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:e,namespace:t,pod:a}})}function f(e){return Object(n["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:e})}function h(e){return Object(n["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:e})}function b(e,t){window.open("/api/v1/k8s/pod/file/download?fileId="+e+"&fileName="+t+"&_time="+(new Date).getTime())}function v(e){return Object(n["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:e})}function g(e){window.open("/api/v1/k8s/pod/file/preview?fileId="+e)}function y(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:e,namespace:t,pod:a}})}function _(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:e,namespace:t,pod:a}})}},aae3:function(e,t,a){var n=a("d3f4"),r=a("2d95"),o=a("2b4c")("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[o])?!!t:"RegExp"==r(e))}},aef6:function(e,t,a){"use strict";var n=a("5ca1"),r=a("9def"),o=a("d2c8"),l="endsWith",s=""[l];n(n.P+n.F*a("5147")(l),"String",{endsWith:function(e){var t=o(this,e,l),a=arguments.length>1?arguments[1]:void 0,n=r(t.length),i=void 0===a?n:Math.min(r(a),n),c=String(e);return s?s.call(t,c,i):t.slice(i-c.length,i)===c}})},b144:function(e,t,a){"use strict";function n(e){return JSON.parse(JSON.stringify(e))}function r(e){if(!e||!(e instanceof Date))return"";var t=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds();return t}function o(e){return"isCoreApp"===e?"核心服务":"onlyDeployTag"===e?"只允许部署Tag":"addSysctlKeepalive"===e?"调整内核参数":"skyWalkingAgent"===e?"性能跟踪":"appLogToKafka"===e?"接入ClickHouse日志":"buildUseRuntimeJDK"===e?"镜像JDK版本编译代码":"jvmGcLog"===e?"GC日志":e}a.d(t,"a",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"b",(function(){return o}))},b562:function(e,t,a){"use strict";a.d(t,"p",(function(){return r})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"l",(function(){return s})),a.d(t,"j",(function(){return i})),a.d(t,"d",(function(){return c})),a.d(t,"i",(function(){return u})),a.d(t,"h",(function(){return p})),a.d(t,"m",(function(){return d})),a.d(t,"o",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"e",(function(){return h})),a.d(t,"c",(function(){return b})),a.d(t,"k",(function(){return v})),a.d(t,"q",(function(){return g})),a.d(t,"n",(function(){return y})),a.d(t,"g",(function(){return _}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/app/search",method:"get",params:e})}function o(){return Object(n["a"])({url:"/v1/app/apps-with-env",method:"get"})}function l(){return Object(n["a"])({url:"/v1/app/all",method:"get"})}function s(){return Object(n["a"])({url:"/v1/app/names",method:"get"})}function i(e){return Object(n["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function c(e){return Object(n["a"])({url:"/v1/app",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/v1/app",method:"put",data:e})}function p(e){return Object(n["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function d(e,t,a){return Object(n["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:a}})}function m(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function f(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function h(e){return Object(n["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function v(e,t){return Object(n["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function g(e,t){return Object(n["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function y(e,t){return Object(n["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function _(e){return Object(n["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:e}})}},bb0b:function(e,t,a){"use strict";a.d(t,"k",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return l})),a.d(t,"l",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"f",(function(){return c})),a.d(t,"n",(function(){return u})),a.d(t,"o",(function(){return p})),a.d(t,"m",(function(){return d})),a.d(t,"c",(function(){return m})),a.d(t,"d",(function(){return f})),a.d(t,"g",(function(){return h})),a.d(t,"i",(function(){return b})),a.d(t,"h",(function(){return v})),a.d(t,"j",(function(){return g}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/k8s/scale/auto",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/v1/k8s/scale/auto",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/v1/k8s/scale/auto",method:"delete",params:{id:e}})}function s(e){return Object(n["a"])({url:"/v1/k8s/scale/cron",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/v1/k8s/scale/cron",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/v1/k8s/scale/cron",method:"delete",params:{id:e}})}function u(e){return Object(n["a"])({url:"/v1/k8s/scale/log",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/v1/k8s/scale/monitor/log",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler/create-for-core-app",method:"post",params:e})}function h(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler",method:"delete",data:e})}function b(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler/migrate?cluster="+e,method:"post"})}function v(){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler/all-cluster-autoscaler-v2",method:"get"})}function g(e,t,a){return Object(n["a"])({url:"/v1/k8s/scale/all-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}},cf89:function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.stdout.loading,expression:"stdout.loading"}],staticClass:"pod-stdout"},[a("div",{staticStyle:{position:"relative"}},[a("div",{staticStyle:{"font-weight":"bold",float:"left","line-height":"40px"}},[e._v(e._s(this.pod)+" /\n      "),a("el-select",{staticStyle:{width:"240px"},attrs:{size:"mini",placeholder:"请选择容器"},on:{change:e.loadStdoutLog},model:{value:e.container,callback:function(t){e.container=e._n(t)},expression:"container"}},e._l(e.containers,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("div",{staticStyle:{float:"right","margin-right":"20px"}},[a("span",[a("el-checkbox",{on:{change:e.loadStdoutLog},model:{value:e.stdout.previous,callback:function(t){e.$set(e.stdout,"previous",t)},expression:"stdout.previous"}},[e._v("重启前日志")])],1),e._v(" "),a("span",{staticStyle:{"margin-left":"20px"}},[e._v("\n            行数:\n            "),a("el-select",{staticStyle:{width:"120px"},on:{change:e.loadStdoutLog},model:{value:e.stdout.tailLines,callback:function(t){e.$set(e.stdout,"tailLines",e._n(t))},expression:"stdout.tailLines"}},[a("el-option",{attrs:{label:"2000",value:"2000"}}),e._v(" "),a("el-option",{attrs:{label:"5000",value:"5000"}}),e._v(" "),a("el-option",{attrs:{label:"10000",value:"10000"}}),e._v(" "),a("el-option",{attrs:{label:"50000",value:"50000"}})],1)],1),e._v(" "),a("span",{staticStyle:{display:"none"}},[e._v("\n        自动刷新("+e._s(e.stdout.reloadPeriod)+"秒):\n      "),a("el-switch",{on:{change:e.autoReloadSwitch},model:{value:e.stdout.autoReload,callback:function(t){e.$set(e.stdout,"autoReload",t)},expression:"stdout.autoReload"}})],1),e._v(" "),a("el-button",{staticClass:"el-icon-refresh",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(t){return e.loadStdoutLog()}}},[e._v("刷新\n      ")]),e._v(" "),a("el-button",{staticClass:"el-icon-download",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(t){return e.podStdoutLogDownload()}}},[e._v("下载\n      ")])],1),e._v(" "),a("div",{staticStyle:{clear:"both"}})]),e._v(" "),a("div",{staticStyle:{"text-align":"right","margin-right":"5px"}},[e._v("加载时间: "+e._s(e.stdout.lastReloadTime))]),e._v(" "),a("pre",{staticClass:"stdout-log-content",attrs:{id:"stdout-log-content"}},[e._v(e._s(e.stdout.content))])])},r=[],o=a("a527"),l={name:"PodStdout",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!1},containers:{type:Array,required:!1,default:function(){return[]}}},data:function(){return{container:this.containers[0],stdout:{visible:!1,loading:!1,autoReload:!1,previous:!1,tailLines:2e3,reloadPeriod:10,reloadTimer:null,content:"",lastReloadTime:"--"}}},watch:{pod:function(e,t){this.container=this.containers[0],this.loadStdoutLog()}},computed:{},mounted:function(){this.loadStdoutLog()},beforeDestroy:function(){this.stopReloadTimer()},methods:{showStdoutLogDialog:function(){this.stdout.visible=!0,this.loadStdoutLog(),this.stdout.autoReload&&this.startReloadTimer()},loadStdoutLog:function(){var e=this;this.pod&&(console.log("load pod ".concat(this.pod," stdout log")),this.stdout.loading=!0,Object(o["g"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines,this.stdout.previous).then((function(t){e.stdout.content=t.data;var a=e;setTimeout((function(){a.scrollStdoutLogView()}),200),setTimeout((function(){a.scrollStdoutLogView()}),500),setTimeout((function(){a.scrollStdoutLogView()}),700),e.stdout.lastReloadTime=(new Date).toLocaleTimeString()})).catch((function(t){e.$message.error(t.message),e.stopReloadTimer()})).finally((function(){e.stdout.loading=!1})))},podStdoutLogDownload:function(){Object(o["c"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines)},stopReload:function(){this.stdout.autoReload=!1,this.stopReloadTimer()},scrollStdoutLogView:function(){var e=document.getElementById("stdout-log-content");e.scrollTop=e.scrollHeight},startReloadTimer:function(){this.stdout.reloadTimer&&this.stopReloadTimer();var e=this;this.stdout.reloadTimer=setInterval((function(){e.loadStdoutLog()}),1e3*e.stdout.reloadPeriod),console.log("started pod stdout log reload timer :"+this.stdout.reloadTimer)},stopReloadTimer:function(){clearInterval(this.stdout.reloadTimer),console.log("stopped pod stdout log reload timer :"+this.stdout.reloadTimer)},autoReloadSwitch:function(e){this.stdout.autoReload=e,e?this.startReloadTimer():this.stopReloadTimer()}}},s=l,i=(a("fbec"),a("2877")),c=Object(i["a"])(s,n,r,!1,null,"6ff71a9f",null);t["a"]=c.exports},d2c8:function(e,t,a){var n=a("aae3"),r=a("be13");e.exports=function(e,t,a){if(n(t))throw TypeError("String#"+a+" doesn't accept regex!");return String(r(e))}},da37:function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(t){return e.copyToClipboard()}}},[a("i",{staticClass:"el-icon-document-copy"}),e._v(" "),this.buttonText?a("span",[e._v(e._s(this.buttonText))]):e._e()])},r=[],o={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var e=this,t=this.text;t?navigator.clipboard.writeText(t).then((function(){e.$message.success("复制成功")})).catch((function(){var a=document.createElement("input");document.body.appendChild(a),a.setAttribute("value",t),a.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(a),e.$message.success("复制成功")})):this.$message.warning("内容为空")}}},l=o,s=a("2877"),i=Object(s["a"])(l,n,r,!1,null,null,null);t["a"]=i.exports},e18c:function(e,t,a){},fbec:function(e,t,a){"use strict";a("e18c")}}]);