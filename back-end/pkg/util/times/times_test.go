package times

import (
	"testing"
	"time"
)

func TestFuncTimeCost(t *testing.T) {
	// Test function time cost measurement
	timeCostFunc := FuncTimeCost()
	
	// Simulate some work
	time.Sleep(10 * time.Millisecond)
	
	// This should not panic and should log the time cost
	timeCostFunc("TestFunction")
}

func TestConvertUTCToChinaTime(t *testing.T) {
	tests := []struct {
		name        string
		timeStr     string
		expectError bool
		expectHour  int // Expected hour in China timezone
	}{
		{
			name:        "Valid RFC3339 time",
			timeStr:     "2023-12-25T10:30:00Z",
			expectError: false,
			expectHour:  18, // UTC 10:30 -> China 18:30
		},
		{
			name:        "Valid RFC3339 time with timezone",
			timeStr:     "2023-12-25T10:30:00+00:00",
			expectError: false,
			expectHour:  18,
		},
		{
			name:        "Invalid time format",
			timeStr:     "2023-12-25 10:30:00",
			expectError: true,
		},
		{
			name:        "Empty string",
			timeStr:     "",
			expectError: true,
		},
		{
			name:        "Invalid RFC3339 format",
			timeStr:     "invalid-time",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ConvertUTCToChinaTime(tt.timeStr)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for input %q, but got none", tt.timeStr)
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error for input %q: %v", tt.timeStr, err)
				return
			}
			
			// Check if the timezone is correctly set to Asia/Shanghai
			location, _ := time.LoadLocation("Asia/Shanghai")
			if result.Location().String() != location.String() {
				t.Errorf("Expected timezone %s, got %s", location.String(), result.Location().String())
			}
			
			// Check if the hour conversion is correct
			if result.Hour() != tt.expectHour {
				t.Errorf("Expected hour %d, got %d", tt.expectHour, result.Hour())
			}
		})
	}
}

func TestConvertUTCToChinaTimeEdgeCases(t *testing.T) {
	// Test midnight conversion
	midnightUTC := "2023-12-25T00:00:00Z"
	result, err := ConvertUTCToChinaTime(midnightUTC)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	
	// UTC midnight should be 8 AM in China
	if result.Hour() != 8 {
		t.Errorf("Expected hour 8 for UTC midnight, got %d", result.Hour())
	}
	
	// Test noon conversion
	noonUTC := "2023-12-25T12:00:00Z"
	result, err = ConvertUTCToChinaTime(noonUTC)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	
	// UTC noon should be 8 PM in China
	if result.Hour() != 20 {
		t.Errorf("Expected hour 20 for UTC noon, got %d", result.Hour())
	}
}

func TestConvertUTCToChinaTimeWithDifferentFormats(t *testing.T) {
	// Test with milliseconds
	timeWithMs := "2023-12-25T10:30:00.123Z"
	result, err := ConvertUTCToChinaTime(timeWithMs)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	
	if result.Hour() != 18 {
		t.Errorf("Expected hour 18, got %d", result.Hour())
	}
	
	if result.Nanosecond() != 123000000 {
		t.Errorf("Expected nanoseconds 123000000, got %d", result.Nanosecond())
	}
}

// Benchmark test for performance
func BenchmarkConvertUTCToChinaTime(b *testing.B) {
	timeStr := "2023-12-25T10:30:00Z"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := ConvertUTCToChinaTime(timeStr)
		if err != nil {
			b.Fatalf("Unexpected error: %v", err)
		}
	}
}

func BenchmarkFuncTimeCost(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		timeCostFunc := FuncTimeCost()
		timeCostFunc("BenchmarkFunction")
	}
}
