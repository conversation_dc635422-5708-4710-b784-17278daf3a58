package k8s_templates

import (
	"fs-k8s-app-manager/models/data"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/dto"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestBuildDeployment(t *testing.T) {
	// Create a minimal deployment parameter for testing
	param := DeploymentParam{
		Cluster:         "test-cluster",
		Namespace:       "test-namespace",
		App:             "test-app",
		AppImage:        "nginx:latest",
		ImagePullPolicy: "Always",
		Replicas:        3,
		MaxSurge:        "25%",
		DeployId:        "deploy-123",
		Annotations:     map[string]string{"app.kubernetes.io/name": "test-app"},
		Labels:          map[string]string{"version": "v1.0.0"},
		PodAnnotations:  map[string]string{"prometheus.io/scrape": "true"},
		Resources: data.Resources{
			RequestCPU:    0.1,
			RequestMemory: 128,
			LimitCPU:      0.5,
			LimitMemory:   512,
		},
		DeployStrategy: constant.DEPLOY_STRATEGY_ROLL_UPDATE,
		LivenessProbe: data.Probe{
			Enable:              true,
			InitialDelaySeconds: 30,
			PeriodSeconds:       10,
		},
		ReadinessProbe: data.Probe{
			Enable:              true,
			InitialDelaySeconds: 5,
			PeriodSeconds:       5,
		},
		Envs: datatype.Envs{
			{Name: "ENV", Value: "production"},
			{Name: "DEBUG", Value: "false"},
		},
		Ports: datatype.Ports{
			{Name: "http", Value: 8080, Type: constant.PORT_TYPE_USER},
			{Name: "metrics", Value: 9090, Type: constant.PORT_TYPE_USER},
		},
		RevisionHistoryLimit: 10,
	}

	// Test if template file exists (this will fail if template doesn't exist)
	templatePath := getFilePath("deployment.yaml.tmpl")
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		t.Skipf("Template file %s does not exist, skipping test", templatePath)
	}

	result, err := BuildDeployment(param)
	if err != nil {
		t.Errorf("BuildDeployment failed: %v", err)
		return
	}

	// Verify the result contains expected values
	expectedStrings := []string{
		"test-app",
		"test-namespace",
		"nginx:latest",
		"replicas: 3",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't", expected)
		}
	}

	// Verify it's valid YAML structure
	if !strings.Contains(result, "apiVersion:") || !strings.Contains(result, "kind:") {
		t.Errorf("Result doesn't appear to be valid Kubernetes YAML")
	}
}

func TestBuildDeploymentWithName(t *testing.T) {
	param := DeploymentParam{
		App:       "test-app",
		Namespace: "test-namespace",
		AppImage:  "nginx:latest",
		Replicas:  1,
	}

	// Test with non-existent template file
	_, err := BuildDeploymentWithName(param, "non-existent.yaml.tmpl")
	if err == nil {
		t.Errorf("Expected error for non-existent template file")
	}

	// Test with deployment.yaml.tmpl if it exists
	templatePath := getFilePath("deployment.yaml.tmpl")
	if _, err := os.Stat(templatePath); err == nil {
		result, err := BuildDeploymentWithName(param, "deployment.yaml.tmpl")
		if err != nil {
			t.Errorf("BuildDeploymentWithName failed: %v", err)
		} else if result == "" {
			t.Errorf("BuildDeploymentWithName returned empty result")
		}
	}
}

func TestBuildService(t *testing.T) {
	param := ServiceParam{
		App:       "test-app",
		Namespace: "test-namespace",
		Ports: []Port{
			{Name: "http", Port: 80, TargetPortStr: "8080"},
			{Name: "metrics", Port: 9090, TargetPortStr: "9090"},
		},
	}

	// Check if template exists
	templatePath := getFilePath("service.yaml.tmpl")
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		t.Skipf("Template file %s does not exist, skipping test", templatePath)
	}

	result, err := BuildService(param)
	if err != nil {
		t.Errorf("BuildService failed: %v", err)
		return
	}

	// Verify the result contains expected values
	expectedStrings := []string{
		"test-app",
		"test-namespace",
		"port: 80",
		"port: 9090",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't", expected)
		}
	}
}

func TestBuildIngress(t *testing.T) {
	param := IngressParam{
		App:         "test-app",
		Namespace:   "test-namespace",
		IngressHost: "test-app.example.com",
	}

	// Check if template exists
	templatePath := getFilePath("ingress.yaml.tmpl")
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		t.Skipf("Template file %s does not exist, skipping test", templatePath)
	}

	result, err := BuildIngress(param)
	if err != nil {
		t.Errorf("BuildIngress failed: %v", err)
		return
	}

	// Verify the result contains expected values
	expectedStrings := []string{
		"test-app",
		"test-namespace",
		"test-app.example.com",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't", expected)
		}
	}
}

func TestBuildHpa(t *testing.T) {
	param := HpaParam{
		App:                            "test-app",
		Namespace:                      "test-namespace",
		MinReplicas:                    2,
		MaxReplicas:                    10,
		TargetCPUUtilizationPercentage: 70,
		Annotations:                    map[string]string{"hpa.autoscaling.banzaicloud.io/minReplicas": "2"},
	}

	// Check if template exists
	templatePath := getFilePath("scale-hpa.yaml.tmpl")
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		t.Skipf("Template file %s does not exist, skipping test", templatePath)
	}

	result, err := BuildHpa(param)
	if err != nil {
		t.Errorf("BuildHpa failed: %v", err)
		return
	}

	// Verify the result contains expected values
	expectedStrings := []string{
		"test-app",
		"test-namespace",
		"minReplicas: 2",
		"maxReplicas: 10",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't", expected)
		}
	}
}

func TestBuildPodAutoScaler(t *testing.T) {
	param := dto.PodAutoScalerDTO{
		Name:      "test-autoscaler",
		Namespace: "test-namespace",
		App:       "test-app",
	}

	// Check if template exists
	templatePath := getFilePath("fs-pod-autoscaler.yaml.tmpl")
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		t.Skipf("Template file %s does not exist, skipping test", templatePath)
	}

	result, err := BuildPodAutoScaler(param)
	if err != nil {
		t.Errorf("BuildPodAutoScaler failed: %v", err)
		return
	}

	// Verify the result contains expected values
	expectedStrings := []string{
		"test-autoscaler",
		"test-namespace",
		"test-app",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't", expected)
		}
	}
}

func TestBuildPodMonitor(t *testing.T) {
	param := PrometheusMonitorParam{
		App:        "test-app",
		Namespace:  "test-namespace",
		Prometheus: "default",
		Endpoints: []Endpoint{
			{Path: "/metrics", Port: "9090", Interval: "30s", ScrapeTimeout: "10s"},
		},
	}

	// Check if template exists
	templatePath := getFilePath("pod-monitor.yaml.tmpl")
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		t.Skipf("Template file %s does not exist, skipping test", templatePath)
	}

	result, err := BuildPodMonitor(param)
	if err != nil {
		t.Errorf("BuildPodMonitor failed: %v", err)
		return
	}

	// Verify the result contains expected values
	expectedStrings := []string{
		"test-app",
		"test-namespace",
		"/metrics",
		"9090",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't", expected)
		}
	}
}

func TestGetFileContent(t *testing.T) {
	// Test with non-existent file
	_, err := GetFileContent("non-existent-file.txt")
	if err == nil {
		t.Errorf("Expected error for non-existent file")
	}

	// Create a test file in templates directory
	templatesDir := filepath.Join("templates")
	if err := os.MkdirAll(templatesDir, 0755); err != nil {
		t.Skipf("Cannot create templates directory: %v", err)
	}

	testFile := filepath.Join(templatesDir, "test-content.txt")
	testContent := "This is test content"
	if err := os.WriteFile(testFile, []byte(testContent), 0644); err != nil {
		t.Skipf("Cannot create test file: %v", err)
	}
	defer os.Remove(testFile)

	content, err := GetFileContent("test-content.txt")
	if err != nil {
		t.Errorf("GetFileContent failed: %v", err)
		return
	}

	if content != testContent {
		t.Errorf("Expected content %q, got %q", testContent, content)
	}
}

func TestBuildContentWithFilename(t *testing.T) {
	// Create a test template file
	templatesDir := filepath.Join("templates")
	if err := os.MkdirAll(templatesDir, 0755); err != nil {
		t.Skipf("Cannot create templates directory: %v", err)
	}

	testFile := filepath.Join(templatesDir, "test-template.tmpl")
	testTemplate := "Hello {{.Name}}, your app is {{.App}}"
	if err := os.WriteFile(testFile, []byte(testTemplate), 0644); err != nil {
		t.Skipf("Cannot create test template: %v", err)
	}
	defer os.Remove(testFile)

	param := map[string]string{
		"Name": "John",
		"App":  "test-app",
	}

	result, err := BuildContentWithFilename("test-template.tmpl", param)
	if err != nil {
		t.Errorf("BuildContentWithFilename failed: %v", err)
		return
	}

	expected := "Hello John, your app is test-app"
	if result != expected {
		t.Errorf("Expected %q, got %q", expected, result)
	}
}

func TestGetFilePath(t *testing.T) {
	// Test the internal getFilePath function
	filename := "test.yaml.tmpl"
	result := getFilePath(filename)

	// Should contain the templates directory and the filename
	if !strings.Contains(result, "k8s-templates/templates/") {
		t.Errorf("Expected path to contain 'k8s-templates/templates/', got %q", result)
	}

	if !strings.HasSuffix(result, filename) {
		t.Errorf("Expected path to end with %q, got %q", filename, result)
	}

	// Should be an absolute path
	if !filepath.IsAbs(result) {
		t.Errorf("Expected absolute path, got %q", result)
	}
}
