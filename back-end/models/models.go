package models

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"strings"
	"time"
)

var db *gorm.DB

func init() {
	fmt.Println("model db init...")
	var err error
	conf := config.Conf.Postgres
	dsn := fmt.Sprintf("user=%s password=%s dbname=%s host=%s port=%d sslmode=disable TimeZone=Asia/Shanghai",
		conf.User, conf.Password, conf.Database, conf.Host, conf.Port)
	var dbLogger logger.Interface
	if strings.EqualFold(config.Conf.App.RunMode, "debug") {
		dbLogger = logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
			logger.Config{
				SlowThreshold: time.Second,
				LogLevel:      logger.Info,
				Colorful:      true,
			},
		)
	}
	db, err = gorm.Open(postgres.New(postgres.Config{
		DSN:                  dsn,
		PreferSimpleProtocol: true, // disables implicit prepared statement usage
	}), &gorm.Config{
		Logger: dbLogger,
	})

	//defer db.Close()
	if err != nil {
		log.Fatalf("models.Setup err: %v", err)
	}
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("models.Setup err: %v", err)
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	err = db.AutoMigrate(&User{}, &Org{}, &App{},
		&Artifact{}, &Pipeline{}, &Log{}, &TempAuth{},
		&CronScale{}, &AutoScale{}, &ScaleLog{}, ScaleMonitorLog{},
		&Reboot{}, &Event{},
		&Job{}, &Task{}, &CommonData{}, &AppVersionHistory{}, &ArtifactImage{})
	if err != nil {
		log.Fatalf("models.Setup err: %v", err)
	}
	//db.Callback().Create().Replace("gorm:update_time", updateTimeForCreate)
}

func DB() *gorm.DB {
	return db
}
