<template>
  <div class="app-container">
    <div>
      <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
        <el-form-item label="操作类型">
          <el-autocomplete
            clearable
            v-model="searchForm.operate"
            placeholder="使用全量匹配"
            :fetch-suggestions="getOperateOptions"
            style="width: 180px"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="操作人">
          <el-input v-model="searchForm.author" style="width: 200px;" placeholder="使用模糊查询" clearable></el-input>
        </el-form-item>
        <el-form-item label="操作对象" style="margin-bottom: 0;">
          <el-input v-model="searchForm.target" style="width: 320px;" placeholder="使用模糊查询" clearable></el-input>
        </el-form-item>
        <el-form-item style="margin-bottom: 0;">
          <el-button type="primary" @click="loadTableData" @submit.native.prevent>查询</el-button>
          <export-button :table-ref="this.$refs.table001"></export-button>
        </el-form-item>
      </el-form>
    </div>
    <el-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.limit"
      layout="total,sizes,prev,pager,next"
      :page-sizes="[10, 20, 50, 100, 200, 400,1000,2000,3000,5000]"
      :total="tableData.count"
      @size-change="pageSizeChange"
      @current-change="pageChange">
    </el-pagination>
    <el-table
      ref="table001"
      v-loading="tableLoading"
      :data="tableData.data"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="操作" prop="operate">
      </el-table-column>
      <el-table-column label="操作人" prop="author">
      </el-table-column>
      <el-table-column label="操作对象" prop="target">
      </el-table-column>
      <el-table-column label="操作时间" width="200px;" prop="createdTime">
      </el-table-column>
      <el-table-column label="内容">
        <template slot-scope="scope">
          {{ scope.row.content.substring(0, 100) }}
          <span v-if=" scope.row.content.length > 100">
            <span style="padding-left: 5px;color:#777;font-weight: bold;">...</span>
          <el-button type="text" @click="logContent(scope.row)">查看所有
          </el-button>
          </span>
          <div v-if=" scope.row.operate === 'tomcat-traffic-count'">
            <el-button type="text" @click="tomcatTrafficAnalysis(scope.row)">分析Tomcat流量
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="Tomcat流量分析" :visible.sync="tomcatTraffic.visible" :fullscreen="true" width="50%" top="5vh">
      <el-alert
        title="提示"
        type="info"
        description="统计数据里过滤掉了k8s健康检测、logConf日志调整等内部组件的请求流量"
        show-icon>
      </el-alert>
      <el-table :data="tomcatTraffic.data">
        <el-table-column property="app" sortable label="应用"></el-table-column>
        <el-table-column property="cluster" sortable label="集群"></el-table-column>
        <el-table-column property="namespace" sortable label="环境"></el-table-column>
        <el-table-column label="pod数">
          <template v-slot="scope">
            {{ scope.row.pods.length }}
          </template>
        </el-table-column>
        <el-table-column property="time" label="分析时间"></el-table-column>
        <el-table-column property="logFile" label="分析日志"></el-table-column>
        <el-table-column property="tomcatTraffic" label="业务访问量" sortable></el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%" top="5vh" :close-on-click-modal="false">
      <div style="margin-top: -50px;overflow: auto;">
        <div style="text-align: center">
          <clipboard-icon :text="this.dialogContent" button-text="一键复制内容"></clipboard-icon>
        </div>
        <pre style="white-space: pre-wrap;border: solid 1px #eee;padding:5px;margin-top: 0;max-height: 600px;overflow-y: auto;">{{ dialogContent }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {queryLogs} from "@/api/log";
import ExportButton from "@/views/components/export-button.vue";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  components: {ClipboardIcon, ExportButton},
  data() {
    return {
      tableData: {
        data: [],
        count: 0
      },
      operateOptions: [
        {
          "value": "应用-创建"
        },
        {
          "value": "应用-删除"
        },
        {
          "value": "应用-回滚"
        },
        {
          "value": "应用-批量发布"
        },
        {
          "value": "应用-批量重发"
        },
        {
          "value": "应用-权限编辑"
        },
        {
          "value": "应用-编辑"
        },
        {
          "value": "应用-部署"
        },
        {
          "value": "应用-部署取消"
        },
        {
          "value": "应用-重发"
        },
        {
          "value": "应用-资源临时调整"
        },
        {
          "value": "发布流程-下线"
        },
        {
          "value": "发布流程-创建"
        },
        {
          "value": "发布流程-审核"
        },
        {
          "value": "发布流程-批量同步配置"
        },
        {
          "value": "发布流程-编辑"
        },
        {
          "value": "发布流程-资源批量更新"
        },
        {
          "value": "实例-删除"
        },
        {
          "value": "实例-摘除"
        },
        {
          "value": "实例文件-上传"
        },
        {
          "value": "实例文件-下载"
        },
        {
          "value": "实例文件-归档"
        },
        {
          "value": "日志-清理"
        },
        {
          "value": "服务-使用重建升级"
        },
        {
          "value": "服务-手动扩容"
        },
        {
          "value": "服务-手动缩容"
        },
        {
          "value": "服务重启"
        },
        {
          "value": "服务-重启"
        },
        {
          "value": "端口-备份"
        },
        {
          "value": "系统配置-修改"
        },
        {
          "value": "自动扩缩容-扩容"
        },
        {
          "value": "自动扩缩容-新建"
        },
        {
          "value": "语音电话-拨打"
        },
        {
          "value": "部署模块-创建"
        },
        {
          "value": "部署模块-删除"
        },
        {
          "value": "部门-修改"
        },
        {
          "value": "部门-创建"
        },
        {
          "value": "app-version-snapshot"
        },
        {
          "value": "Gitlab部署文件-下载"
        },
        {
          "value": "GitTag-创建"
        },
        {
          "value": "GitTag-批量删除"
        },
        {
          "value": "Git分支-批量删除"
        },
        {
          "value": "Harbor-GC"
        },
        {
          "value": "jacoco-coverage-download"
        },
        {
          "value": "jacoco-coverage-reset"
        },
        {
          "value": "jacoco-dump-download"
        },
        {
          "value": "jacoco-report"
        },
        {
          "value": "jacoco-reset"
        },
        {
          "value": "jar-scan"
        },
        {
          "value": "tomcat-version-scan"
        },
        {
          "value": "K8S-Ingress-Apply"
        },
        {
          "value": "K8S-PodMonitor-Apply"
        },
        {
          "value": "K8S-PrometheusMonitor-Apply"
        },
        {
          "value": "K8S-Service-Apply"
        },
        {
          "value": "K8S-ServiceMonitor-Apply"
        },
        {
          "value": "oncall"
        },
        {
          "value": "Pod-ThreadDump"
        },
        {
          "value": "Pod事件-上报"
        },
        {
          "value": "tomcat-traffic-count"
        },
        {
          "value": "war-gc"
        },
        {
          "value": "临时授权-添加"
        },
        {
          "value": "创建Bugfix分支"
        },
        {
          "value": "app-version-snapshot"
        },
        {
          "value": "OnCall-服务扩容"
        },
        {
          "value": "OnCall-服务缩容"
        },
        {
          "value": "OnCall-线程Dump"
        },
        {
          "value": "OnCall-节点驱逐"
        }
      ],
      tableLoading: false,
      searchForm: {
        author: "",
        operate: "",
        target: "",
        page: 1,
        limit: 20,
      },
      dialogVisible: false,
      dialogTitle: "日志内容",
      dialogContent: "-",
      tomcatTraffic: {
        visible: false,
        data: []
      }
    }
  },
  computed: {
  },
  mounted() {
    this.searchForm.operate = this.$route.query.operate || "";
    this.searchForm.author = this.$route.query.author || "";
    this.searchForm.target = this.$route.query.target || "";
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      queryLogs(this.searchForm).then(response => {
        this.tableData = response.data;
        this.tableLoading = false;
      }).catch((e) => {
        this.$message.error(e.message);
        this.tableLoading = false;
      });
    },
    pageSizeChange(size) {
      this.searchForm.limit = size;
      this.loadTableData();
    },
    pageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    logContent(row) {
      let content = row.content;
      if (content.startsWith("[") || content.startsWith("{")) {
        try {
          content = JSON.stringify(JSON.parse(content), null, 2)
        } catch (e) {
          console.log("json parse fail")
        }
      }
      this.dialogContent = content
      this.dialogVisible = true
    },
    tomcatTrafficAnalysis(row) {
      let content = row.content;
      try {
        content = JSON.parse(content)
      } catch (e) {
        this.$message.error("解析数据格式失败")
      }
      this.tomcatTraffic.data = content
      this.tomcatTraffic.visible = true
    },
    getOperateOptions(queryString, cb) {
      let ret = this.operateOptions;
      if (queryString) {
        ret = ret.filter(item => {
          return (item.value.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        })
      }
      cb(ret);
    }
  }
}
</script>


