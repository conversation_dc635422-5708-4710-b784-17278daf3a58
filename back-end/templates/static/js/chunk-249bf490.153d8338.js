(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-249bf490"],{2861:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"yaml-export"}}),t._v(" "),a("el-form",{ref:"searchForm",staticStyle:{height:"50px"},attrs:{inline:!0,model:t.searchForm}},[a("el-form-item",{attrs:{label:"k8s集群",prop:"cluster"}},[a("el-select",{attrs:{placeholder:"选择k8s集群"},on:{change:t.loadApp},model:{value:t.searchForm.cluster,callback:function(e){t.$set(t.searchForm,"cluster",e)},expression:"searchForm.cluster"}},t._l(this.clusterOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[a("el-select",{attrs:{placeholder:"选择Namespace"},on:{change:t.loadApp},model:{value:t.searchForm.namespace,callback:function(e){t.$set(t.searchForm,"namespace",e)},expression:"searchForm.namespace"}},t._l(this.namespaceOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-select",{staticStyle:{width:"340px"},attrs:{filterable:"",placeholder:"选择应用"},model:{value:t.searchForm.app,callback:function(e){t.$set(t.searchForm,"app",e)},expression:"searchForm.app"}},t._l(this.appOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})})),1)],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.addApp}},[t._v("添加")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:t.addAllApp}},[t._v("添加所有")])],1)],1),t._v(" "),a("el-table",{attrs:{data:this.tableData,"row-key":"name"}},[a("el-table-column",{attrs:{label:"应用名",prop:"app"}}),t._v(" "),a("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),t._v(" "),a("el-table-column",{attrs:{label:"环境",prop:"namespace"}})],1),t._v(" "),a("div",{staticStyle:{"padding-top":"10px","text-align":"center"}},[a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.yamlExportBtnLoading,expression:"yamlExportBtnLoading"}],attrs:{type:"primary",size:"small",icon:"el-icon-view"},on:{click:t.printYaml}},[t._v("查看表格服务Yaml")])],1),t._v(" "),a("el-dialog",{attrs:{title:"yaml内容",visible:t.yamlResultShow,width:"70%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.yamlResultShow=e}}},[a("div",{staticStyle:{"margin-top":"-30px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",size:"small",icon:"el-icon-document-copy"},on:{click:t.copyToClipboard}},[t._v("一键复制")]),t._v(" "),a("pre",{staticStyle:{"white-space":"pre-wrap",border:"1px solid #eee",padding:"5px","margin-top":"5px"}},[t._v(t._s(this.yamlResult))])],1)])],1)},r=[],o=(a("7f7f"),a("2d63")),c=a("a68b"),l=a("8504"),u=a("c1ab"),s=(a("27aa"),{components:{appManageTab:c["a"]},mounted:function(){},beforeDestroy:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var t,e=Object(o["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var a=t.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(n){e.e(n)}finally{e.f()}}return[]}},data:function(){return{searchForm:{cluster:"",namespace:"",app:""},appOptions:[],tableData:[],yamlResultShow:!1,yamlResult:"",yamlExportBtnLoading:!1}},methods:{loadApp:function(){var t=this.searchForm.cluster,e=this.searchForm.namespace;if(t&&e){var a=this;Object(l["g"])(t,e).then((function(t){a.appOptions=t.data})).catch((function(t){console.error(t)}))}else this.appOptions=[]},addApp:function(){var t=this.searchForm.cluster,e=this.searchForm.namespace,a=this.searchForm.app;if(a){var n,r="".concat(t,"@@").concat(e,"@@").concat(a),c=Object(o["a"])(this.tableData);try{for(c.s();!(n=c.n()).done;){var l=n.value;if(l.key===r)return void this.$message.warning("服务已存在")}}catch(s){c.e(s)}finally{c.f()}var u={key:r,cluster:t,namespace:e,app:a};this.tableData.push(u)}else this.$message.warning("请选择服务")},addAllApp:function(){this.$message.warning("todo")},printYaml:function(){var t=this;if(this.tableData.size<1)this.$message.warning("请先添加服务");else{this.yamlExportBtnLoading=!0,this.yamlResult="";var e=JSON.stringify(this.tableData);Object(u["G"])(e).then((function(e){t.yamlResult=e.data,t.yamlResultShow=!0})).catch((function(t){console.error(t)})).finally((function(){t.yamlExportBtnLoading=!1}))}},copyToClipboard:function(){var t=this,e=this.yamlResult;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var a=document.createElement("input");document.body.appendChild(a),a.setAttribute("value",e),a.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(a),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}}),p=s,i=a("2877"),d=Object(i["a"])(p,n,r,!1,null,null,null);e["default"]=d.exports},8504:function(t,e,a){"use strict";a.d(e,"g",(function(){return r})),a.d(e,"a",(function(){return o})),a.d(e,"h",(function(){return c})),a.d(e,"c",(function(){return l})),a.d(e,"b",(function(){return u})),a.d(e,"i",(function(){return s})),a.d(e,"d",(function(){return p})),a.d(e,"f",(function(){return i})),a.d(e,"e",(function(){return d}));var n=a("b775");function r(t,e){return Object(n["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:a}})}function c(t){return Object(n["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function l(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:a}})}function u(t){return Object(n["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function s(t){return Object(n["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function p(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:a}})}function i(t){return Object(n["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,a,r,o){return Object(n["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:a,revision:r,deployTag:o||""}})}},a68b:function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"自动扩缩容-批量创建",name:"auto-scale-batch-create"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},r=[],o=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var a=t.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"auto-scale-batch-create"===a?this.$router.push({name:"tool-auto-scale-batch-create"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),c=o,l=(a("d54f"),a("2877")),u=Object(l["a"])(c,n,r,!1,null,null,null);e["a"]=u.exports},c1ab:function(t,e,a){"use strict";a.d(e,"j",(function(){return r})),a.d(e,"l",(function(){return o})),a.d(e,"z",(function(){return c})),a.d(e,"A",(function(){return l})),a.d(e,"d",(function(){return u})),a.d(e,"h",(function(){return s})),a.d(e,"D",(function(){return p})),a.d(e,"F",(function(){return i})),a.d(e,"y",(function(){return d})),a.d(e,"b",(function(){return m})),a.d(e,"g",(function(){return h})),a.d(e,"C",(function(){return f})),a.d(e,"E",(function(){return b})),a.d(e,"x",(function(){return v})),a.d(e,"G",(function(){return y})),a.d(e,"m",(function(){return g})),a.d(e,"e",(function(){return O})),a.d(e,"a",(function(){return j})),a.d(e,"B",(function(){return k})),a.d(e,"k",(function(){return _})),a.d(e,"i",(function(){return x})),a.d(e,"s",(function(){return $})),a.d(e,"v",(function(){return w})),a.d(e,"w",(function(){return F})),a.d(e,"o",(function(){return C})),a.d(e,"p",(function(){return S})),a.d(e,"t",(function(){return A})),a.d(e,"f",(function(){return R})),a.d(e,"u",(function(){return E})),a.d(e,"c",(function(){return T})),a.d(e,"q",(function(){return D})),a.d(e,"r",(function(){return B})),a.d(e,"n",(function(){return N}));var n=a("b775");function r(t){return Object(n["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function o(t){return Object(n["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(n["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function l(t){return Object(n["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function u(){return Object(n["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function s(t){return Object(n["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function i(t){return Object(n["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(n["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function h(t,e,a,r,o,c,l,u){return Object(n["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(e,"&suffixVersion=").concat(a,"&message=").concat(r,"&dependencyCheck=").concat(c,"&parentPom=").concat(l),method:"post",data:u})}function f(t){return Object(n["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function v(t){return Object(n["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function g(t,e,a,r){return Object(n["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&overrideNamespace=").concat(r),method:"post"})}function O(t,e,a,r,o){return Object(n["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(a,"&remark=").concat(r,"&dryRun=").concat(o),method:"post"})}function j(){return Object(n["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function k(){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function _(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function x(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function $(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function w(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function F(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function C(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function S(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function A(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function R(t){return Object(n["a"])({url:"/v1/tool/autoscale-batch-create",method:"post",data:t})}function E(t,e){return Object(n["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function T(t){return Object(n["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function D(t){return Object(n["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function B(t){return Object(n["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function N(t,e,a){return Object(n["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(a),method:"get"})}},d54f:function(t,e,a){"use strict";a("eba1")},eba1:function(t,e,a){}}]);