package cache

import (
	"fs-k8s-app-manager/config"
	"testing"
	"time"
)

// Mock config for testing
func setupTestConfig() {
	// Set cache category to GoCache for testing
	config.Conf.App.CacheCategory = "goCache"
}

func TestCacheType(t *testing.T) {
	setupTestConfig()
	
	cType := cacheType()
	if cType != GoCache {
		t.<PERSON>rf("Expected cache type %s, got %s", GoCache, cType)
	}
	
	// Test with Redis
	config.Conf.App.CacheCategory = "redis"
	cType = cacheType()
	if cType != Redis {
		t.E<PERSON>rf("Expected cache type %s, got %s", Redis, cType)
	}
	
	// Reset to GoCache for other tests
	config.Conf.App.CacheCategory = "goCache"
}

func TestSetAndGetStruct(t *testing.T) {
	setupTestConfig()
	
	type TestStruct struct {
		Name  string
		Value int
		Items []string
	}
	
	tests := []struct {
		name   string
		key    string
		value  TestStruct
		expire time.Duration
	}{
		{
			name: "Simple struct",
			key:  "test:struct:1",
			value: TestStruct{
				Name:  "test",
				Value: 42,
				Items: []string{"a", "b", "c"},
			},
			expire: 1 * time.Minute,
		},
		{
			name: "Empty struct",
			key:  "test:struct:2",
			value: TestStruct{},
			expire: 30 * time.Second,
		},
		{
			name: "Struct with nil slice",
			key:  "test:struct:3",
			value: TestStruct{
				Name:  "test-nil",
				Value: 0,
				Items: nil,
			},
			expire: 2 * time.Minute,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test SetStruct
			err := SetStruct(tt.key, tt.value, tt.expire)
			if err != nil {
				t.Errorf("SetStruct failed: %v", err)
				return
			}
			
			// Test GetStruct
			var result TestStruct
			retrieved, found := GetStruct(tt.key, result)
			if !found {
				t.Errorf("GetStruct should find the key %s", tt.key)
				return
			}
			
			// Verify the retrieved value
			if retrieved.Name != tt.value.Name {
				t.Errorf("Expected Name %s, got %s", tt.value.Name, retrieved.Name)
			}
			if retrieved.Value != tt.value.Value {
				t.Errorf("Expected Value %d, got %d", tt.value.Value, retrieved.Value)
			}
			if len(retrieved.Items) != len(tt.value.Items) {
				t.Errorf("Expected Items length %d, got %d", len(tt.value.Items), len(retrieved.Items))
			}
			
			// Clean up
			Delete(tt.key)
		})
	}
}

func TestSetAndGetStr(t *testing.T) {
	setupTestConfig()
	
	tests := []struct {
		name   string
		key    string
		value  string
		expire time.Duration
	}{
		{
			name:   "Simple string",
			key:    "test:string:1",
			value:  "hello world",
			expire: 1 * time.Minute,
		},
		{
			name:   "Empty string",
			key:    "test:string:2",
			value:  "",
			expire: 30 * time.Second,
		},
		{
			name:   "JSON string",
			key:    "test:string:3",
			value:  `{"name":"test","value":123}`,
			expire: 2 * time.Minute,
		},
		{
			name:   "Unicode string",
			key:    "test:string:4",
			value:  "测试中文字符串",
			expire: 1 * time.Minute,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test SetStr
			err := SetStr(tt.key, tt.value, tt.expire)
			if err != nil {
				t.Errorf("SetStr failed: %v", err)
				return
			}
			
			// Test GetStr
			retrieved, found := GetStr(tt.key)
			if !found {
				t.Errorf("GetStr should find the key %s", tt.key)
				return
			}
			
			if retrieved != tt.value {
				t.Errorf("Expected value %q, got %q", tt.value, retrieved)
			}
			
			// Clean up
			Delete(tt.key)
		})
	}
}

func TestGetNonExistentKey(t *testing.T) {
	setupTestConfig()
	
	// Test GetStruct with non-existent key
	type TestStruct struct {
		Name string
	}
	var defaultStruct TestStruct
	result, found := GetStruct("non:existent:key", defaultStruct)
	if found {
		t.Errorf("GetStruct should not find non-existent key")
	}
	if result.Name != "" {
		t.Errorf("GetStruct should return default value for non-existent key")
	}
	
	// Test GetStr with non-existent key
	strResult, found := GetStr("non:existent:string:key")
	if found {
		t.Errorf("GetStr should not find non-existent key")
	}
	if strResult != "" {
		t.Errorf("GetStr should return empty string for non-existent key")
	}
}

func TestDelete(t *testing.T) {
	setupTestConfig()
	
	key := "test:delete:key"
	value := "test value"
	
	// Set a value
	err := SetStr(key, value, 1*time.Minute)
	if err != nil {
		t.Fatalf("SetStr failed: %v", err)
	}
	
	// Verify it exists
	_, found := GetStr(key)
	if !found {
		t.Fatalf("Key should exist before deletion")
	}
	
	// Delete the key
	Delete(key)
	
	// Verify it's deleted
	_, found = GetStr(key)
	if found {
		t.Errorf("Key should not exist after deletion")
	}
}

func TestClearAll(t *testing.T) {
	setupTestConfig()
	
	// Set multiple test values
	testKeys := []string{
		"test:clear:1",
		"test:clear:2",
		"test:clear:3",
	}
	
	for _, key := range testKeys {
		err := SetStr(key, "test value", 1*time.Minute)
		if err != nil {
			t.Fatalf("SetStr failed for key %s: %v", key, err)
		}
	}
	
	// Verify they exist
	for _, key := range testKeys {
		_, found := GetStr(key)
		if !found {
			t.Fatalf("Key %s should exist before clear", key)
		}
	}
	
	// Clear all
	ClearAll()
	
	// Verify they're cleared (except session keys)
	for _, key := range testKeys {
		_, found := GetStr(key)
		if found {
			t.Errorf("Key %s should not exist after clear", key)
		}
	}
}

func TestCacheExpiration(t *testing.T) {
	setupTestConfig()
	
	key := "test:expiration:key"
	value := "test value"
	shortExpire := 100 * time.Millisecond
	
	// Set a value with short expiration
	err := SetStr(key, value, shortExpire)
	if err != nil {
		t.Fatalf("SetStr failed: %v", err)
	}
	
	// Verify it exists immediately
	retrieved, found := GetStr(key)
	if !found {
		t.Fatalf("Key should exist immediately after setting")
	}
	if retrieved != value {
		t.Errorf("Expected value %q, got %q", value, retrieved)
	}
	
	// Wait for expiration
	time.Sleep(200 * time.Millisecond)
	
	// Verify it's expired (this test might be flaky depending on cache implementation)
	_, found = GetStr(key)
	// Note: GoCache might still have the item, Redis would expire it
	// This test is more relevant for Redis cache
	t.Logf("Key found after expiration: %v (implementation dependent)", found)
}

func TestUnsupportedCacheType(t *testing.T) {
	// Test with unsupported cache type
	originalCategory := config.Conf.App.CacheCategory
	defer func() {
		config.Conf.App.CacheCategory = originalCategory
	}()
	
	config.Conf.App.CacheCategory = "unsupported"
	
	// These should panic
	defer func() {
		if r := recover(); r == nil {
			t.Errorf("Expected panic for unsupported cache type")
		}
	}()
	
	SetStr("test:key", "test value", 1*time.Minute)
}

func TestCacheWithComplexStruct(t *testing.T) {
	setupTestConfig()
	
	type ComplexStruct struct {
		ID       int
		Name     string
		Tags     []string
		Metadata map[string]interface{}
		Nested   struct {
			Value string
			Count int
		}
	}
	
	complex := ComplexStruct{
		ID:   123,
		Name: "complex test",
		Tags: []string{"tag1", "tag2", "tag3"},
		Metadata: map[string]interface{}{
			"key1": "value1",
			"key2": 42,
			"key3": true,
		},
		Nested: struct {
			Value string
			Count int
		}{
			Value: "nested value",
			Count: 10,
		},
	}
	
	key := "test:complex:struct"
	
	// Set complex struct
	err := SetStruct(key, complex, 1*time.Minute)
	if err != nil {
		t.Fatalf("SetStruct failed: %v", err)
	}
	
	// Get complex struct
	var result ComplexStruct
	retrieved, found := GetStruct(key, result)
	if !found {
		t.Fatalf("GetStruct should find the complex struct")
	}
	
	// Verify complex struct fields
	if retrieved.ID != complex.ID {
		t.Errorf("Expected ID %d, got %d", complex.ID, retrieved.ID)
	}
	if retrieved.Name != complex.Name {
		t.Errorf("Expected Name %s, got %s", complex.Name, retrieved.Name)
	}
	if len(retrieved.Tags) != len(complex.Tags) {
		t.Errorf("Expected Tags length %d, got %d", len(complex.Tags), len(retrieved.Tags))
	}
	if retrieved.Nested.Value != complex.Nested.Value {
		t.Errorf("Expected Nested.Value %s, got %s", complex.Nested.Value, retrieved.Nested.Value)
	}
	
	// Clean up
	Delete(key)
}

// Benchmark tests
func BenchmarkSetStruct(b *testing.B) {
	setupTestConfig()
	
	type BenchStruct struct {
		Name  string
		Value int
		Items []string
	}
	
	testStruct := BenchStruct{
		Name:  "benchmark test",
		Value: 42,
		Items: []string{"a", "b", "c", "d", "e"},
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		key := "bench:struct:" + string(rune(i))
		SetStruct(key, testStruct, 1*time.Minute)
	}
}

func BenchmarkGetStruct(b *testing.B) {
	setupTestConfig()
	
	type BenchStruct struct {
		Name  string
		Value int
	}
	
	// Setup
	testStruct := BenchStruct{Name: "benchmark", Value: 42}
	key := "bench:get:struct"
	SetStruct(key, testStruct, 1*time.Minute)
	
	var result BenchStruct
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetStruct(key, result)
	}
}

func BenchmarkSetStr(b *testing.B) {
	setupTestConfig()
	
	value := "benchmark test string value"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		key := "bench:string:" + string(rune(i))
		SetStr(key, value, 1*time.Minute)
	}
}

func BenchmarkGetStr(b *testing.B) {
	setupTestConfig()
	
	// Setup
	key := "bench:get:string"
	value := "benchmark test string"
	SetStr(key, value, 1*time.Minute)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetStr(key)
	}
}
