(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-66e3dfb0"],{"018c":function(t,e,n){"use strict";var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{"margin-top":"-10px"}},[n("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[n("el-tab-pane",{attrs:{label:"老集群（k8s1)",name:"old-k8s"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"新集群（k8s0)",name:"new-k8s"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"应用迁移处理",name:"migrate-operation"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"发布流程处理",name:"pipeline-batch-operation"}})],1)],1)},a=[],o=(n("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var n=t.name;"old-k8s"===n?this.$router.push({name:"pipeline-migrate-old"}):"new-k8s"===n?this.$router.push({name:"pipeline-migrate-new"}):"migrate-operation"===n?this.$router.push({name:"pipeline-migrate-operation"}):"pipeline-batch-operation"===n?this.$router.push({name:"pipeline-batch-operation"}):this.$message.error("未知操作")}}}),c=o,u=n("2877"),i=Object(u["a"])(c,r,a,!1,null,null,null);e["a"]=i.exports},"02f4":function(t,e,n){var r=n("4588"),a=n("be13");t.exports=function(t){return function(e,n){var o,c,u=String(a(e)),i=r(n),s=u.length;return i<0||i>=s?t?"":void 0:(o=u.charCodeAt(i),o<55296||o>56319||i+1===s||(c=u.charCodeAt(i+1))<56320||c>57343?t?u.charAt(i):o:t?u.slice(i,i+2):c-56320+(o-55296<<10)+65536)}}},"0390":function(t,e,n){"use strict";var r=n("02f4")(!0);t.exports=function(t,e,n){return e+(n?r(t,e).length:1)}},"0bfb":function(t,e,n){"use strict";var r=n("cb7c");t.exports=function(){var t=r(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"11e9":function(t,e,n){var r=n("52a7"),a=n("4630"),o=n("6821"),c=n("6a99"),u=n("69a8"),i=n("c69a"),s=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?s:function(t,e){if(t=o(t),e=c(e,!0),i)try{return s(t,e)}catch(n){}if(u(t,e))return a(!r.f.call(t,e),t[e])}},"214f":function(t,e,n){"use strict";n("b0c5");var r=n("2aba"),a=n("32e9"),o=n("79e5"),c=n("be13"),u=n("2b4c"),i=n("520a"),s=u("species"),l=!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),p=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();t.exports=function(t,e,n){var d=u(t),f=!o((function(){var e={};return e[d]=function(){return 7},7!=""[t](e)})),m=f?!o((function(){var e=!1,n=/a/;return n.exec=function(){return e=!0,null},"split"===t&&(n.constructor={},n.constructor[s]=function(){return n}),n[d](""),!e})):void 0;if(!f||!m||"replace"===t&&!l||"split"===t&&!p){var v=/./[d],h=n(c,d,""[t],(function(t,e,n,r,a){return e.exec===i?f&&!a?{done:!0,value:v.call(e,n,r)}:{done:!0,value:t.call(n,e,r)}:{done:!1}})),b=h[0],g=h[1];r(String.prototype,t,b),a(RegExp.prototype,d,2==e?function(t,e){return g.call(t,this,e)}:function(t){return g.call(t,this)})}}},"4f7f":function(t,e,n){"use strict";var r=n("c26b"),a=n("b39a"),o="Set";t.exports=n("e0b8")(o,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return r.def(a(this,o),t=0===t?0:t,t)}},r)},5147:function(t,e,n){var r=n("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[r]=!1,!"/./"[t](e)}catch(a){}}return!0}},"51a9":function(t,e,n){"use strict";n.d(e,"c",(function(){return a})),n.d(e,"e",(function(){return o})),n.d(e,"d",(function(){return c})),n.d(e,"l",(function(){return u})),n.d(e,"m",(function(){return i})),n.d(e,"a",(function(){return s})),n.d(e,"f",(function(){return l})),n.d(e,"i",(function(){return p})),n.d(e,"j",(function(){return d})),n.d(e,"k",(function(){return f})),n.d(e,"n",(function(){return m})),n.d(e,"g",(function(){return v})),n.d(e,"b",(function(){return h})),n.d(e,"h",(function(){return b})),n.d(e,"o",(function(){return g}));var r=n("b775");function a(t){return Object(r["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function o(t){return Object(r["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function c(t,e,n){return Object(r["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:n}})}function u(t){return Object(r["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function s(){return Object(r["a"])({url:"/v1/pipeline/all",method:"get"})}function l(t){return Object(r["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function p(t){return Object(r["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function d(t){return Object(r["a"])({url:"/v1/pipeline",method:"post",data:t})}function f(t){return Object(r["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function m(t){return Object(r["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function v(t,e,n,a){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:a}})}function h(t){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function b(t,e,n,a){return Object(r["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:a}})}function g(t){return Object(r["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"520a":function(t,e,n){"use strict";var r=n("0bfb"),a=RegExp.prototype.exec,o=String.prototype.replace,c=a,u="lastIndex",i=function(){var t=/a/,e=/b*/g;return a.call(t,"a"),a.call(e,"a"),0!==t[u]||0!==e[u]}(),s=void 0!==/()??/.exec("")[1],l=i||s;l&&(c=function(t){var e,n,c,l,p=this;return s&&(n=new RegExp("^"+p.source+"$(?!\\s)",r.call(p))),i&&(e=p[u]),c=a.call(p,t),i&&c&&(p[u]=p.global?c.index+c[0].length:e),s&&c&&c.length>1&&o.call(c[0],n,(function(){for(l=1;l<arguments.length-2;l++)void 0===arguments[l]&&(c[l]=void 0)})),c}),t.exports=c},"5dbc":function(t,e,n){var r=n("d3f4"),a=n("8b97").set;t.exports=function(t,e,n){var o,c=e.constructor;return c!==n&&"function"==typeof c&&(o=c.prototype)!==n.prototype&&r(o)&&a&&a(t,o),t}},"5df3":function(t,e,n){"use strict";var r=n("02f4")(!0);n("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=r(e,n),this._i+=t.length,{value:t,done:!1})}))},"5f1b":function(t,e,n){"use strict";var r=n("23c6"),a=RegExp.prototype.exec;t.exports=function(t,e){var n=t.exec;if("function"===typeof n){var o=n.call(t,e);if("object"!==typeof o)throw new TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==r(t))throw new TypeError("RegExp#exec called on incompatible receiver");return a.call(t,e)}},"67ab":function(t,e,n){var r=n("ca5a")("meta"),a=n("d3f4"),o=n("69a8"),c=n("86cc").f,u=0,i=Object.isExtensible||function(){return!0},s=!n("79e5")((function(){return i(Object.preventExtensions({}))})),l=function(t){c(t,r,{value:{i:"O"+ ++u,w:{}}})},p=function(t,e){if(!a(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!o(t,r)){if(!i(t))return"F";if(!e)return"E";l(t)}return t[r].i},d=function(t,e){if(!o(t,r)){if(!i(t))return!0;if(!e)return!1;l(t)}return t[r].w},f=function(t){return s&&m.NEED&&i(t)&&!o(t,r)&&l(t),t},m=t.exports={KEY:r,NEED:!1,fastKey:p,getWeak:d,onFreeze:f}},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return a})),n.d(e,"a",(function(){return o})),n.d(e,"h",(function(){return c})),n.d(e,"c",(function(){return u})),n.d(e,"b",(function(){return i})),n.d(e,"i",(function(){return s})),n.d(e,"d",(function(){return l})),n.d(e,"f",(function(){return p})),n.d(e,"e",(function(){return d}));var r=n("b775");function a(t,e){return Object(r["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,n){return Object(r["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(r["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function u(t,e,n){return Object(r["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t){return Object(r["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function s(t){return Object(r["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function l(t,e,n){return Object(r["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function p(t){return Object(r["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,n,a,o){return Object(r["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:a,deployTag:o||""}})}},"8b97":function(t,e,n){var r=n("d3f4"),a=n("cb7c"),o=function(t,e){if(a(t),!r(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,r){try{r=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),r(t,[]),e=!(t instanceof Array)}catch(a){e=!0}return function(t,n){return o(t,n),e?t.__proto__=n:r(t,n),t}}({},!1):void 0),check:o}},a481:function(t,e,n){"use strict";var r=n("cb7c"),a=n("4bf8"),o=n("9def"),c=n("4588"),u=n("0390"),i=n("5f1b"),s=Math.max,l=Math.min,p=Math.floor,d=/\$([$&`']|\d\d?|<[^>]*>)/g,f=/\$([$&`']|\d\d?)/g,m=function(t){return void 0===t?t:String(t)};n("214f")("replace",2,(function(t,e,n,v){return[function(r,a){var o=t(this),c=void 0==r?void 0:r[e];return void 0!==c?c.call(r,o,a):n.call(String(o),r,a)},function(t,e){var a=v(n,t,this,e);if(a.done)return a.value;var p=r(t),d=String(this),f="function"===typeof e;f||(e=String(e));var b=p.global;if(b){var g=p.unicode;p.lastIndex=0}var y=[];while(1){var _=i(p,d);if(null===_)break;if(y.push(_),!b)break;var O=String(_[0]);""===O&&(p.lastIndex=u(d,o(p.lastIndex),g))}for(var x="",j=0,w=0;w<y.length;w++){_=y[w];for(var k=String(_[0]),S=s(l(c(_.index),d.length),0),E=[],$=1;$<_.length;$++)E.push(m(_[$]));var U=_.groups;if(f){var C=[k].concat(E,S,d);void 0!==U&&C.push(U);var N=String(e.apply(void 0,C))}else N=h(k,d,S,E,U,e);S>=j&&(x+=d.slice(j,S)+N,j=S+k.length)}return x+d.slice(j)}];function h(t,e,r,o,c,u){var i=r+t.length,s=o.length,l=f;return void 0!==c&&(c=a(c),l=d),n.call(u,l,(function(n,a){var u;switch(a.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,r);case"'":return e.slice(i);case"<":u=c[a.slice(1,-1)];break;default:var l=+a;if(0===l)return n;if(l>s){var d=p(l/10);return 0===d?n:d<=s?void 0===o[d-1]?a.charAt(1):o[d-1]+a.charAt(1):n}u=o[l-1]}return void 0===u?"":u}))}}))},aae3:function(t,e,n){var r=n("d3f4"),a=n("2d95"),o=n("2b4c")("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==a(t))}},aef6:function(t,e,n){"use strict";var r=n("5ca1"),a=n("9def"),o=n("d2c8"),c="endsWith",u=""[c];r(r.P+r.F*n("5147")(c),"String",{endsWith:function(t){var e=o(this,t,c),n=arguments.length>1?arguments[1]:void 0,r=a(e.length),i=void 0===n?r:Math.min(a(n),r),s=String(t);return u?u.call(e,s,i):e.slice(i-s.length,i)===s}})},b0c5:function(t,e,n){"use strict";var r=n("520a");n("5ca1")({target:"RegExp",proto:!0,forced:r!==/./.exec},{exec:r})},b39a:function(t,e,n){var r=n("d3f4");t.exports=function(t,e){if(!r(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},b562:function(t,e,n){"use strict";n.d(e,"p",(function(){return a})),n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"l",(function(){return u})),n.d(e,"j",(function(){return i})),n.d(e,"d",(function(){return s})),n.d(e,"i",(function(){return l})),n.d(e,"h",(function(){return p})),n.d(e,"m",(function(){return d})),n.d(e,"o",(function(){return f})),n.d(e,"f",(function(){return m})),n.d(e,"e",(function(){return v})),n.d(e,"c",(function(){return h})),n.d(e,"k",(function(){return b})),n.d(e,"q",(function(){return g})),n.d(e,"n",(function(){return y})),n.d(e,"g",(function(){return _}));var r=n("b775");function a(t){return Object(r["a"])({url:"/v1/app/search",method:"get",params:t})}function o(){return Object(r["a"])({url:"/v1/app/apps-with-env",method:"get"})}function c(){return Object(r["a"])({url:"/v1/app/all",method:"get"})}function u(){return Object(r["a"])({url:"/v1/app/names",method:"get"})}function i(t){return Object(r["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function s(t){return Object(r["a"])({url:"/v1/app",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/v1/app",method:"put",data:t})}function p(t){return Object(r["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function d(t,e,n){return Object(r["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:n}})}function f(t){return Object(r["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function m(t){return Object(r["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function v(t){return Object(r["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function h(t){return Object(r["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function b(t,e){return Object(r["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function g(t,e){return Object(r["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function y(t,e){return Object(r["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function _(t){return Object(r["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:t}})}},c1ab:function(t,e,n){"use strict";n.d(e,"j",(function(){return a})),n.d(e,"l",(function(){return o})),n.d(e,"z",(function(){return c})),n.d(e,"A",(function(){return u})),n.d(e,"d",(function(){return i})),n.d(e,"h",(function(){return s})),n.d(e,"D",(function(){return l})),n.d(e,"F",(function(){return p})),n.d(e,"y",(function(){return d})),n.d(e,"b",(function(){return f})),n.d(e,"g",(function(){return m})),n.d(e,"C",(function(){return v})),n.d(e,"E",(function(){return h})),n.d(e,"x",(function(){return b})),n.d(e,"G",(function(){return g})),n.d(e,"m",(function(){return y})),n.d(e,"e",(function(){return _})),n.d(e,"a",(function(){return O})),n.d(e,"B",(function(){return x})),n.d(e,"k",(function(){return j})),n.d(e,"i",(function(){return w})),n.d(e,"s",(function(){return k})),n.d(e,"v",(function(){return S})),n.d(e,"w",(function(){return E})),n.d(e,"o",(function(){return $})),n.d(e,"p",(function(){return U})),n.d(e,"t",(function(){return C})),n.d(e,"f",(function(){return N})),n.d(e,"u",(function(){return F})),n.d(e,"c",(function(){return T})),n.d(e,"q",(function(){return A})),n.d(e,"r",(function(){return D})),n.d(e,"n",(function(){return R}));var r=n("b775");function a(t){return Object(r["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function o(t){return Object(r["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(r["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function u(t){return Object(r["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function i(){return Object(r["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function s(t){return Object(r["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(r["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function f(){return Object(r["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function m(t,e,n,a,o,c,u,i){return Object(r["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(e,"&suffixVersion=").concat(n,"&message=").concat(a,"&dependencyCheck=").concat(c,"&parentPom=").concat(u),method:"post",data:i})}function v(t){return Object(r["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function h(t){return Object(r["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function b(t){return Object(r["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(r["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,n,a){return Object(r["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(n,"&overrideNamespace=").concat(a),method:"post"})}function _(t,e,n,a,o){return Object(r["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(n,"&remark=").concat(a,"&dryRun=").concat(o),method:"post"})}function O(){return Object(r["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function x(){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function j(t){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function w(t){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function k(t){return Object(r["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function S(t){return Object(r["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function E(t){return Object(r["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function $(t){return Object(r["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function U(t){return Object(r["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function C(t){return Object(r["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function N(t){return Object(r["a"])({url:"/v1/tool/autoscale-batch-create",method:"post",data:t})}function F(t,e){return Object(r["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function T(t){return Object(r["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function A(t){return Object(r["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function D(t){return Object(r["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function R(t,e,n){return Object(r["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(n),method:"get"})}},c26b:function(t,e,n){"use strict";var r=n("86cc").f,a=n("2aeb"),o=n("dcbc"),c=n("9b43"),u=n("f605"),i=n("4a59"),s=n("01f9"),l=n("d53b"),p=n("7a56"),d=n("9e1e"),f=n("67ab").fastKey,m=n("b39a"),v=d?"_s":"size",h=function(t,e){var n,r=f(e);if("F"!==r)return t._i[r];for(n=t._f;n;n=n.n)if(n.k==e)return n};t.exports={getConstructor:function(t,e,n,s){var l=t((function(t,r){u(t,l,e,"_i"),t._t=e,t._i=a(null),t._f=void 0,t._l=void 0,t[v]=0,void 0!=r&&i(r,n,t[s],t)}));return o(l.prototype,{clear:function(){for(var t=m(this,e),n=t._i,r=t._f;r;r=r.n)r.r=!0,r.p&&(r.p=r.p.n=void 0),delete n[r.i];t._f=t._l=void 0,t[v]=0},delete:function(t){var n=m(this,e),r=h(n,t);if(r){var a=r.n,o=r.p;delete n._i[r.i],r.r=!0,o&&(o.n=a),a&&(a.p=o),n._f==r&&(n._f=a),n._l==r&&(n._l=o),n[v]--}return!!r},forEach:function(t){m(this,e);var n,r=c(t,arguments.length>1?arguments[1]:void 0,3);while(n=n?n.n:this._f){r(n.v,n.k,this);while(n&&n.r)n=n.p}},has:function(t){return!!h(m(this,e),t)}}),d&&r(l.prototype,"size",{get:function(){return m(this,e)[v]}}),l},def:function(t,e,n){var r,a,o=h(t,e);return o?o.v=n:(t._l=o={i:a=f(e,!0),k:e,v:n,p:r=t._l,n:void 0,r:!1},t._f||(t._f=o),r&&(r.n=o),t[v]++,"F"!==a&&(t._i[a]=o)),t},getEntry:h,setStrong:function(t,e,n){s(t,e,(function(t,n){this._t=m(t,e),this._k=n,this._l=void 0}),(function(){var t=this,e=t._k,n=t._l;while(n&&n.r)n=n.p;return t._t&&(t._l=n=n?n.n:t._t._f)?l(0,"keys"==e?n.k:"values"==e?n.v:[n.k,n.v]):(t._t=void 0,l(1))}),n?"entries":"values",!n,!0),p(e)}}},cdbe:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("pipeline-migrate-tab",{attrs:{"active-name":"new-k8s"}}),t._v(" "),n("el-row",{staticStyle:{"max-width":"1080px"},attrs:{gutter:20}},[n("el-col",{attrs:{span:18}},[n("el-form",[n("el-form-item",[n("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:5},placeholder:"输入需要过滤的发布流程列表，内容格式：集群/运行环境/应用名，多个之间用换行分割"},model:{value:t.searchForm,callback:function(e){t.searchForm=e},expression:"searchForm"}})],1)],1)],1),t._v(" "),n("el-col",{attrs:{span:6}},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.tableFilter}},[t._v("过滤")])],1)],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData,"element-loading-text":"数据加载中...","default-sort":{prop:"createdTime",order:"ascending"},border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{type:"selection",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"140px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text"},on:{click:function(n){return t.deployUseK8s1Tag(e.row)}}},[t._v("使用k8s1版本发布\n        ")]),n("br"),t._v(" "),n("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text"}},[t._v("\n            发布流程页\n          ")])],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"地址 (port / name)"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.ports,(function(r){return n("div",{staticStyle:{border:"1px #ccc solid",padding:"2px","margin-top":"3px"}},[n("div",{staticStyle:{display:"inline-block"}},[t._v("\n            "+t._s(r.value)+" / "+t._s(r.name)+"\n          ")]),t._v(" "),n("el-button",{staticStyle:{"padding-top":"0","padding-bottom":"0"},attrs:{type:"text"},on:{click:function(n){return t.updateCMSConfig(e.row,r.value)}}},[t._v("修改配置中心\n          ")])],1)}))}}])}),t._v(" "),n("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),t._v(" "),n("el-table-column",{attrs:{label:"环境",filters:t.namespaceOptions,"filter-method":t.filterNamespace,prop:"namespace"}}),t._v(" "),n("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),t._v(" "),n("el-table-column",{attrs:{label:"状态",width:"100px",align:"center",filters:[{text:"正常",value:"enabled"},{text:"已迁移",value:"migrated"},{text:"待审核",value:"待审核"}],"filter-method":t.filterStatus},scopedSlots:t._u([{key:"default",fn:function(e){return["enabled"===e.row.status?n("el-tag",{attrs:{type:"success"}},[t._v("\n          "+t._s(t.convertStatus(e.row.status))+"\n        ")]):n("el-tag",{attrs:{type:"warning"}},[t._v("\n          "+t._s(t.convertStatus(e.row.status))+"\n        ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"配置实例数",align:"center",prop:"replicas"}}),t._v(" "),n("el-table-column",{attrs:{label:"创建人",property:"author"}}),t._v(" "),n("el-table-column",{attrs:{label:"修改时间",prop:"updatedTime",width:"100px"}})],1),t._v(" "),n("el-dialog",{attrs:{title:"地址修改确认",visible:t.cmsUpdateVisible,width:"860px"},on:{"update:visible":function(e){t.cmsUpdateVisible=e}}},[n("div",[n("el-descriptions",{attrs:{column:1,border:""}},[n("el-descriptions-item",[n("template",{slot:"label"},[n("b",[t._v(t._s(t.cmsUpdateOld.cluster))]),t._v(" "+t._s("/"+t.cmsUpdateOld.namespace+"/"+t.cmsUpdateOld.app)+" 地址：\n          ")]),t._v("\n          "+t._s(t.cmsUpdateOld.text)+"\n        ")],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[n("b",[t._v(t._s(t.cmsUpdateNew.cluster))]),t._v(" "+t._s("/"+t.cmsUpdateNew.namespace+"/"+t.cmsUpdateNew.app)+" 地址：\n          ")]),t._v("\n          "+t._s(t.cmsUpdateNew.text)+"\n        ")],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[n("b",{staticStyle:{"font-size":"1.2em"}},[t._v("配置中心修改：")])]),t._v("\n          "+t._s(t.cmsUpdateOld.text)+" → "+t._s(t.cmsUpdateNew.text)+"\n        ")],2)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.cmsUpdateVisible=!1}}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:t.cmsPage}},[t._v("去配置中心修改")])],1)])],1)},a=[],o=(n("96cf"),n("3b8d")),c=(n("aef6"),n("a481"),n("2d63")),u=(n("ac6a"),n("5df3"),n("4f7f"),n("51a9")),i=n("8504"),s=n("b562"),l=n("018c"),p=n("c1ab"),d={components:{PipelineMigrateTab:l["a"]},data:function(){return{tableDataAll:[],tableData:[],searchForm:"",namespaceOptions:[],tableLoading:!1,cmsUpdateVisible:!1,cmsUpdateOld:{cluster:"",namespace:"",app:"",text:""},cmsUpdateNew:{cluster:"",namespace:"",app:"",text:""}}},computed:{},mounted:function(){this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(u["l"])({cluster:"k8s0",page:1,limit:1e4}).then((function(e){var n,r=new Set,a=Object(c["a"])(e.data.data);try{for(a.s();!(n=a.n()).done;){var o=n.value;r.add(o.namespace)}}catch(l){a.e(l)}finally{a.f()}var u,i=Object(c["a"])(r);try{for(i.s();!(u=i.n()).done;){var s=u.value;t.namespaceOptions.push({text:s,value:s})}}catch(l){i.e(l)}finally{i.f()}t.tableDataAll=e.data.data,t.tableFilter()})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},tableFilter:function(){if(this.searchForm&&this.searchForm.trim()){this.tableData=[],this.searchForm=this.searchForm.replace(/ /g,""),this.searchForm.endsWith("\n")||(this.searchForm+="\n"),console.log(this.searchForm);var t,e=Object(c["a"])(this.tableDataAll);try{for(e.s();!(t=e.n()).done;){var n=t.value;this.searchForm.indexOf("".concat(n.cluster,"/").concat(n.namespace,"/").concat(n.app,"\n"))>-1&&this.tableData.push(n)}}catch(r){e.e(r)}finally{e.f()}}else this.tableData=this.tableDataAll},findDeployment:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(e,n,r){var a,o,u,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=null,o=Object(c["a"])(this.tableData),t.prev=2,o.s();case 4:if((u=o.n()).done){t.next=11;break}if(s=u.value,s.cluster!==e||s.namespace!==n||s.app!==r){t.next=9;break}return a=s,t.abrupt("break",11);case 9:t.next=4;break;case 11:t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](2),o.e(t.t0);case 16:return t.prev=16,o.f(),t.finish(16);case 19:if(null!==a){t.next=22;break}return this.$message.warning("找不到发布流程，应用：".concat(e," / ").concat(n," / ").concat(r)),t.abrupt("return");case 22:Object(i["a"])(e,n,r).then((function(t){a.extraAttr.deployTag=t.data.deployTag,a.extraAttr.runningPodNum=t.data.replicas})).catch((function(t){a.extraAttr.deployTag="--",a.extraAttr.runningPodNum=0,console.log(t.message)}));case 23:case"end":return t.stop()}}),t,this,[[2,13,16,19]])})));function e(e,n,r){return t.apply(this,arguments)}return e}(),filterStatus:function(t,e){return e.status===t},filterNamespace:function(t,e){return e.namespace===t},updateCMSConfig:function(t,e){var n=this;"k8s0"===t.cluster?Object(s["m"])("k8s0",t.namespace,t.app).then((function(r){n.cmsUpdateOld={},n.cmsUpdateNew={};var a,o=null,u=Object(c["a"])(r.data);try{for(u.s();!(a=u.n()).done;){var i=a.value;if(i.port===e){o=i;break}}}catch(l){u.e(l)}finally{u.f()}o?(n.cmsUpdateNew={cluster:"k8s0",namespace:t.namespace,app:t.app,text:o.clusterOuterAddress[0]},Object(s["m"])("k8s1",t.namespace,t.app).then((function(r){var a,o=null,u=Object(c["a"])(r.data);try{for(u.s();!(a=u.n()).done;){var i=a.value;if(i.port===e){o=i;break}}}catch(l){u.e(l)}finally{u.f()}o?(n.cmsUpdateOld={cluster:"k8s1",namespace:t.namespace,app:t.app,text:o.clusterOuterAddress[0]},n.cmsUpdateVisible=!0):n.$message.error("找不到地址")})).catch((function(t){n.$message.error(t.message)}))):n.$message.error("找不到地址")})).catch((function(t){n.$message.error(t.message)})):this.$message.warning("目前只支持k8s0环境")},deployUseK8s1Tag:function(t){var e=this;this.$prompt("确定要使用k8s1集群的版本发布应用 ".concat(t.app," 吗？，如果要跳过镜像构建，输入 true，否则输入 false"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^(true|false)$/,inputErrorMessage:"输入值只能是 true 或者 false",showInput:!0,inputValue:"true",type:"warning"}).then((function(n){var r=n.value,a={cluster:t.cluster,namespace:t.namespace,app:t.app,oldCluster:"k8s1",skipImageBuild:r,forceCodeCompile:!1};Object(p["c"])(a).then((function(t){e.$message.success(t.data)})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){}))},pipelinePage:function(t){this.$router.push({name:"cicd-app-deploy",query:{app:t.app}})},cmsPage:function(){var t="https://oss.foneshare.cn/cms/replace/preview/?profile=";t+="&src="+this.cmsUpdateOld.text+"&dst="+this.cmsUpdateNew.text,window.open(t,"_blank")},convertStatus:function(t){switch(t){case"enabled":return"正常";case"audit":return"待审核";case"migrated":return"已迁移";default:return"未知"}}}},f=d,m=n("2877"),v=Object(m["a"])(f,r,a,!1,null,null,null);e["default"]=v.exports},d2c8:function(t,e,n){var r=n("aae3"),a=n("be13");t.exports=function(t,e,n){if(r(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(a(t))}},e0b8:function(t,e,n){"use strict";var r=n("7726"),a=n("5ca1"),o=n("2aba"),c=n("dcbc"),u=n("67ab"),i=n("4a59"),s=n("f605"),l=n("d3f4"),p=n("79e5"),d=n("5cc5"),f=n("7f20"),m=n("5dbc");t.exports=function(t,e,n,v,h,b){var g=r[t],y=g,_=h?"set":"add",O=y&&y.prototype,x={},j=function(t){var e=O[t];o(O,t,"delete"==t||"has"==t?function(t){return!(b&&!l(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return b&&!l(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,n){return e.call(this,0===t?0:t,n),this})};if("function"==typeof y&&(b||O.forEach&&!p((function(){(new y).entries().next()})))){var w=new y,k=w[_](b?{}:-0,1)!=w,S=p((function(){w.has(1)})),E=d((function(t){new y(t)})),$=!b&&p((function(){var t=new y,e=5;while(e--)t[_](e,e);return!t.has(-0)}));E||(y=e((function(e,n){s(e,y,t);var r=m(new g,e,y);return void 0!=n&&i(n,h,r[_],r),r})),y.prototype=O,O.constructor=y),(S||$)&&(j("delete"),j("has"),h&&j("get")),($||k)&&j(_),b&&O.clear&&delete O.clear}else y=v.getConstructor(e,t,h,_),c(y.prototype,n),u.NEED=!0;return f(y,t),x[t]=y,a(a.G+a.W+a.F*(y!=g),x),b||v.setStrong(y,t,h),y}}}]);