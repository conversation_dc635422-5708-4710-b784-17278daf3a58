package cronjob

import (
	"fmt"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	k8s_cli "fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/service/auto_scale_service"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/service/scale_log_service"
	"fs-k8s-app-manager/service/scale_monitor_log_service"
	log "github.com/sirupsen/logrus"
	v1 "k8s.io/api/apps/v1"
	"time"
)

type thresholdCounter struct {
	counters map[string]int
}

func (t *thresholdCounter) key(sca models.AutoScale) string {
	return fmt.Sprintf("scaleThreshold@@%s@@%s@@%s", sca.Cluster, sca.Namespace, sca.App)
}
func (t *thresholdCounter) get(sca models.AutoScale) int {
	return t.counters[t.key(sca)]
}
func (t *thresholdCounter) inc(sca models.AutoScale) int {
	t.counters[t.key(sca)] += 1
	return t.counters[t.key(sca)]
}
func (t *thresholdCounter) sub(sca models.AutoScale) int {
	t.counters[t.key(sca)] -= 1
	return t.counters[t.key(sca)]
}
func (t *thresholdCounter) clear(sca models.AutoScale) {
	t.counters[t.key(sca)] = 0
}

var scaleUpCounter = thresholdCounter{
	counters: map[string]int{},
}

var scaleDownCounter = thresholdCounter{
	counters: map[string]int{},
}

type locker struct {
	items map[string]time.Time
}

func (l *locker) key(sca models.AutoScale) string {
	return fmt.Sprintf("scaleLocker@@%s@@%s@@%s", sca.Cluster, sca.Namespace, sca.App)
}
func (l *locker) lock(sca models.AutoScale, t time.Time) {
	l.items[l.key(sca)] = t
}
func (l *locker) release(sca models.AutoScale) {
	delete(l.items, l.key(sca))
}
func (l *locker) isLock(sca models.AutoScale) bool {
	return l.items[l.key(sca)].After(time.Now())
}
func (l *locker) get(sca models.AutoScale) time.Time {
	return l.items[l.key(sca)]
}

var scaleUpLocker = locker{items: map[string]time.Time{}}
var scaleDownLocker = locker{items: map[string]time.Time{}}

/*
*
cpu使用率告警抑制
*/
func cpuUsageAlertInhibition(pod string) bool {
	key := key.Pre().JOB.Key("pod_cpu_usage@" + pod)
	//获取锁
	if _, found := cache.GetStr(key); found {
		return true
	}
	//加锁
	cache.SetStr(key, time.Now().Format("200601021504"), 10*time.Minute)
	return false
}

/*
自动扩缩容
*/
func autoScale() {
	log.Info("start job")
	start := time.Now()
	allItems, err := auto_scale_service.FindAll()
	if err != nil {
		log.Error(err)
		return
	}

	items := make([]models.AutoScale, 0, len(allItems))
	for _, it := range allItems {
		if c := settings.GetSetting().GetCluster(it.Cluster); c != nil && c.AutoScale {
			items = append(items, it)
		}
	}

	metricFactory := MetricFactory{
		metrics: make(map[string][]kubectl.PodMetric),
	}
	deployFactory := DeployFactory{
		deploys: make(map[string]*v1.DeploymentList),
	}
	for _, sca := range items {
		appMetric := metricFactory.getAppMetrics(sca.Cluster, sca.Namespace, sca.App)
		dep := deployFactory.getAppDeploy(sca.Cluster, sca.Namespace, sca.App)
		if dep == nil {
			log.Warnf("deployment can't found, %s/%s/%s", sca.Cluster, sca.Namespace, sca.App)
			continue
		}
		autoScaleTask(sca, appMetric, dep)
	}
	log.Infof("job time cost: %s", time.Since(start))
}

func scaleUp(sca models.AutoScale, appMetric kubectl.AppMetric, dep *dto.Deployment) error {
	defer func() {
		if r := recover(); r != nil {
			log.Error(fmt.Sprintf("%s/%s scale-up panic: %v", dep.Namespace, dep.Name, r))
		}
	}()

	cpuPercent := int((appMetric.CPU * 100) / dep.LimitCPU)
	cpuUsage := fmt.Sprintf("%.2f", float64(appMetric.CPU)/1000.0)
	cpuLimit := fmt.Sprintf("%.2f", float64(dep.LimitCPU)/1000.0)

	if scaleUpLocker.isLock(sca) {
		log.Infof("[scale-up] locked, app: %s/%s, release time: %s",
			dep.Namespace, dep.Name, scaleUpLocker.get(sca).Format("01-02 15:04:05"))
		return nil
	}
	log.Infof("[scale-up] usage, app: %s/%s, cpu: %d%% = %s/%s",
		dep.Namespace, dep.Name, cpuPercent, cpuUsage, cpuLimit)
	logRemark := fmt.Sprintf("pod replica: %d, cpu avg usage:  %d%% = %s/%s", dep.Replicas, cpuPercent, cpuUsage, cpuLimit)
	scale_monitor_log_service.Create(sca.Cluster, sca.Namespace, sca.App, logRemark)
	if cpuPercent < sca.CpuTargetPercent {
		scaleUpCounter.clear(sca)
		return nil
	}
	if scaleUpCounter.inc(sca) < sca.ScaleUpThreshold {
		return nil
	}

	if uint(dep.Replicas) >= sca.Replicas {
		return nil
	}
	oldReplicas := dep.Replicas
	newReplicas := oldReplicas + 3
	if newReplicas > int32(sca.Replicas) {
		newReplicas = int32(sca.Replicas)
	}
	if newReplicas <= oldReplicas {
		return nil
	}

	if err := kubectl.Scale(sca.Cluster, sca.Namespace, sca.App, newReplicas); err != nil {
		return err
	}
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      sca.Cluster,
		Profile:      sca.Namespace,
		Creator:      "自动扩缩容",
		ResourceType: "app",
		ResourceId:   sca.App,
		Title:        "服务自动扩容",
		Message:      fmt.Sprintf("CPU平均使用率：%d%% ，副本数：%d → %d", cpuPercent, dep.Replicas, newReplicas),
		Level:        "info",
		Extra:        "",
	})
	//锁定扩容一段时间，避免pod的启动过程导致再次扩容
	scaleUpLocker.lock(sca, time.Now().Add(10*time.Minute))
	//锁定缩容一段，避免pod的很快被缩回去
	scaleDownLocker.lock(sca, time.Now().Add(180*time.Minute))
	event_service.CreateBySys(event_service.BuildAppKey(sca.Cluster, sca.Namespace, sca.App),
		fmt.Sprintf("服务自动扩容，cpu平均使用率：%d%% ，副本数：%d → %d", cpuPercent, dep.Replicas, newReplicas))
	scale_log_service.CreateBySys("自动扩缩容-扩容", sca.Cluster, sca.Namespace, sca.App,
		fmt.Sprintf("cpu使用率：%d%%, 扩容到: %d", cpuPercent, newReplicas),
		dep.Replicas, newReplicas)

	log_service.CreateBySys("自动扩缩容-扩容", sca.App, fmt.Sprintf("%s , cpu使用率：%d%%, 扩容到: %d", sca.AppFullName(), cpuPercent, sca.Replicas))
	//qixin := []string{
	//	fmt.Sprintf("--- 自动扩容 ---"),
	//	fmt.Sprintf("【应用】：%s", sca.App),
	//	fmt.Sprintf("【环境】：%s", sca.Namespace),
	//	fmt.Sprintf("【所在集群】：%s", sca.Cluster),
	//	fmt.Sprintf("【cpu使用率】：%d%% = %s/%s", cpuPercent, cpuUsage, cpuLimit),
	//	fmt.Sprintf("【副本数】：%d -> %d", dep.Replicas, newReplicas),
	//	fmt.Sprintf("【备注】：%s", remark),
	//	fmt.Sprintf("【负责人】：%s", "@"+cmdb_service.GetOwnerNames(sca.App, " @")),
	//}
	//if err := notify_service.SendQiXinToImportantAlertSession(cmdb_service.GetOwnerIds(sca.App), strings.Join(qixin, "\n")); err != nil {
	//	log.Warn("自动扩容企信发送失败，" + err.Error())
	//}
	return nil
}

func scaleDown(sca models.AutoScale, appMetric kubectl.AppMetric, dep *dto.Deployment) error {
	defer func() {
		if r := recover(); r != nil {
			log.Error(fmt.Sprintf("%s/%s scale-down panic: %v", dep.Namespace, dep.Name, r))
		}
	}()
	pipe, err := pipeline_service.FirstInEnv(sca.Cluster, sca.Namespace, sca.App)
	if err != nil {
		return err
	}
	cpuPercent := int((appMetric.CPU * 100) / dep.LimitCPU)
	cpuUsage := fmt.Sprintf("%.2f", float64(appMetric.CPU)/1000.0)
	cpuLimit := fmt.Sprintf("%.2f", float64(dep.LimitCPU)/1000.0)

	log.Infof("[scale-down] app: %s/%s, cpu: %d%% = %s/%s",
		dep.Namespace, dep.Name, cpuPercent, cpuUsage, cpuLimit)

	if scaleDownLocker.isLock(sca) {
		log.Infof("%s scale down locked, release time: %s",
			sca.AppFullName(), scaleDownLocker.get(sca).Format("01-02 15:04:05"))
		return nil
	}
	if cpuPercent >= sca.CpuTargetPercent {
		scaleDownCounter.clear(sca)
		return nil
	}
	if scaleDownCounter.inc(sca) < sca.ScaleDownThreshold {
		return nil
	}
	if pipe.Replicas >= uint(dep.Replicas) || uint(dep.Replicas) <= 1 {
		return nil
	}

	//缩容： [cpu使用率] < [cpu使用率阈值]  && [发布流程副本数] < [运行副本数] && 不能缩容为0 && 慢缩（一次缩一个）
	newReplicas := dep.Replicas - 1
	if err := kubectl.Scale(sca.Cluster, sca.Namespace, sca.App, newReplicas); err != nil {
		return err
	}
	event_service.CreateBySys(event_service.BuildAppKey(sca.Cluster, sca.Namespace, sca.App),
		fmt.Sprintf("服务自动缩容，cpu平均使用率：%d%% ，副本数：%d → %d", cpuPercent, dep.Replicas, newReplicas))
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      sca.Cluster,
		Profile:      sca.Namespace,
		Creator:      "自动扩缩容",
		ResourceType: "app",
		ResourceId:   sca.App,
		Title:        "服务自动缩容",
		Message:      fmt.Sprintf("CPU平均使用率：%d%% ，副本数：%d → %d", cpuPercent, dep.Replicas, newReplicas),
		Level:        "info",
		Extra:        "",
	})
	scale_log_service.CreateBySys("自动扩缩容-缩容", sca.Cluster, sca.Namespace, sca.App,
		fmt.Sprintf("cpu使用率：%d%%, 缩容到: %d", cpuPercent, newReplicas),
		dep.Replicas, newReplicas)

	//qixin := []string{
	//	fmt.Sprintf("--- 自动缩容 ---"),
	//	fmt.Sprintf("【应用】：%s", sca.App),
	//	fmt.Sprintf("【环境】：%s", sca.Namespace),
	//	fmt.Sprintf("【所在集群】：%s", sca.Cluster),
	//	fmt.Sprintf("【cpu使用率】：%d%% = %s/%s", cpuPercent, cpuUsage, cpuLimit),
	//	fmt.Sprintf("【副本数】：%d -> %d", dep.Replicas, newReplicas),
	//	fmt.Sprintf("【负责人】：%s", "@"+cmdb_service.GetOwnerNames(sca.App, " @")),
	//}
	//if err := notify_service.SendQiXinToImportantAlertSession(cmdb_service.
	//	GetOwnerIds(sca.App), strings.Join(qixin, "\n")); err != nil {
	//	log.Warn("自动缩容企信发送失败，" + err.Error())
	//}
	return nil
}
func autoScaleTask(sca models.AutoScale, appMetric kubectl.AppMetric, deployment *v1.Deployment) {
	dep := k8s_service.ParseDeploymentDTO(deployment, "")

	//应用发版时，pod处于启动阶段，cpu高属正常现象， 不做扩缩容操作
	if dep.UpdateTime.Add(time.Minute * 15).After(time.Now()) {
		return
	}

	//CPU负载高时，保留线程状态现场
	//go func() {
	//	for _, podMetric := range appMetric.PodMetrics {
	//		cpuUsage := podMetric.CPU * 100 / dep.LimitCPU
	//		if cpuUsage > 90 {
	//			if cpuUsageAlertInhibition(podMetric.Name) {
	//				log.Infof("告警抑制: " + podMetric.Name)
	//				continue
	//			}
	//			dumpfile, err := k8s_service.ThreadDump(sca.Cluster, sca.Namespace, podMetric.Name)
	//			if err != nil {
	//				log.Errorf("dump thread fail, app: %s, err: %v", sca.AppFullName(), err)
	//				log_service.CreateBySys("Pod-ThreadDump", sca.AppFullName(), fmt.Sprintf("fail：%v", err))
	//				continue
	//			}
	//			log.Infof("dump thread success, app: %s, file: %s", sca.AppFullName(), dumpfile)
	//			log_service.CreateBySys("Pod-ThreadDump", sca.AppFullName(), map[string]interface{}{
	//				"podMetric": podMetric,
	//				"cpuUsage":  cpuUsage,
	//				"dumpFile":  dumpfile,
	//			})
	//			qixin := []string{
	//				fmt.Sprint("--- Pod CPU使用率超90% ---"),
	//				fmt.Sprintf("【应用】：%s", sca.App),
	//				fmt.Sprintf("【环境】：%s", sca.Namespace),
	//				fmt.Sprintf("【所在集群】：%s", sca.Cluster),
	//				fmt.Sprintf("【Pod】：%s", podMetric.Name),
	//				fmt.Sprintf("【cpu使用率】：%d%%", cpuUsage),
	//				fmt.Sprintf("【线程dump】：%s", dumpfile),
	//				fmt.Sprintf("【负责人】：%s", "@"+cmdb_service.GetOwnerNames(sca.App, " @")),
	//				fmt.Sprintf("【下载页面】：%s/#/pod/file?cluster=%s&namespace=%s&pod=%s&path=%%2Fopt%%2Ftomcat%%2Flogs", config.Conf.K8sAppManager.Host, sca.Cluster, sca.Namespace, podMetric.Name),
	//			}
	//			if err := notify_service.SendQiXinToImportantAlertSession(cmdb_service.
	//				GetOwnerIds(sca.App), strings.Join(qixin, "\n")); err != nil {
	//				log.Warn("企信发送失败，" + err.Error())
	//			}
	//		}
	//	}
	//}()

	//扩容时间段, 有些任务型服务（比如函数，要求晚间能扩容，因此暂且设置为全天时间段
	// isScaleUpTimeWindow := sca.InScaleUpTimeRange()
	isScaleUpTimeWindow := true
	if isScaleUpTimeWindow {
		if err := scaleUp(sca, appMetric, dep); err != nil {
			log.Errorf("scale up fail, app: %s, err: %v", sca.AppFullName(), err.Error())
		}
	}

	//缩容时间段
	if sca.InScaleDownTimeRange() {
		if err := scaleDown(sca, appMetric, dep); err != nil {
			log.Errorf("scale down fail, app: %s, err: %v", sca.AppFullName(), err.Error())
		}
	}

}

type MetricFactory struct {
	metrics map[string][]kubectl.PodMetric
}

func (m *MetricFactory) getMetrics(cluster, namespace string) []kubectl.PodMetric {
	key := fmt.Sprintf("%s@@%s", cluster, namespace)
	if _, found := m.metrics[key]; !found {
		if mcs, err := kubectl.GetPodsMetrics(cluster, namespace, ""); err == nil {
			m.metrics[key] = mcs
		} else {
			m.metrics[key] = make([]kubectl.PodMetric, 0, 0)
			log.Warnf("can't get metrics, %s %s %s", cluster, namespace, err.Error())
		}
	}
	return m.metrics[key]
}
func (m *MetricFactory) getAppMetrics(cluster, namespace, app string) kubectl.AppMetric {
	podMetrics := make([]kubectl.PodMetric, 0, 50)
	for _, it := range m.getMetrics(cluster, namespace) {
		if k8s_util.GetAppName(it.Name) == app {
			podMetrics = append(podMetrics, it)
		}
	}

	go func() {

	}()

	log.Infof("%s/%s pod metics: %+v", namespace, app, podMetrics)
	var cpuAvg, memAvg int64
	if len(podMetrics) > 0 {
		var cpuCount, memCount int64
		for _, it := range podMetrics {
			cpuCount += it.CPU
			memCount += it.Memory
		}
		cpuAvg = cpuCount / int64(len(podMetrics))
		memAvg = memCount / int64(len(podMetrics))
	}
	ret := kubectl.AppMetric{
		BaseMetric: kubectl.BaseMetric{
			Cluster:    cluster,
			Namespace:  namespace,
			Name:       app,
			CreateTime: time.Now(),
			CPU:        cpuAvg,
			Memory:     memAvg,
		},
		PodMetrics: podMetrics,
	}
	log.Infof("%s/%s app metrics: %+v", namespace, app, ret)
	return ret
}

type DeployFactory struct {
	deploys map[string]*v1.DeploymentList
}

func (d *DeployFactory) getDeploys(cluster, namespace string) *v1.DeploymentList {
	key := fmt.Sprintf("%s@@%s", cluster, namespace)
	if _, found := d.deploys[key]; !found {
		if deps, err := k8s_cli.GetDeploymentList(cluster, namespace); err == nil {
			d.deploys[key] = deps
		} else {
			d.deploys[key] = &v1.DeploymentList{
				Items: make([]v1.Deployment, 0, 0),
			}
			log.Warnf("can't get deployment, %s %s %s", cluster, namespace, err.Error())
		}
	}
	return d.deploys[key]
}

func (d *DeployFactory) getAppDeploy(cluster, namespace, app string) *v1.Deployment {
	deployList := d.getDeploys(cluster, namespace)
	if deployList == nil {
		return nil
	}
	for _, it := range deployList.Items {
		for it.Name == app {
			return &it
		}
	}
	return nil
}
