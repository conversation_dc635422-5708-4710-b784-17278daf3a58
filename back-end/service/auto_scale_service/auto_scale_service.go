package auto_scale_service

import (
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
	"time"
)

func SaveOrUpdate(entity models.AutoScale) (err error) {
	if entity.ID == 0 {
		return models.DB().Create(&entity).Error
	} else {
		entity.UpdatedAt = time.Now()
		return models.DB().Omit("created_at").Save(&entity).Error
	}
}

func FindById(id uint) (ret models.AutoScale, err error) {
	err = models.DB().Where(models.AutoScale{Model: gorm.Model{ID: id}}).Take(&ret).Error
	return
}

func FirstInEnv(cluster, namespace, app string) (ret models.AutoScale, err error) {
	err = models.DB().Where(models.AutoScale{
		Cluster:   cluster,
		Namespace: namespace,
		App:       app,
	}).First(&ret).Error
	return
}
func DeleteById(id uint) error {
	return models.DB().Where("id = ?", id).Unscoped().Delete(&models.AutoScale{}).Error
}

func FindAll() (ret []models.AutoScale, err error) {
	err = models.DB().Find(&ret).Error
	return
}

func Search(cluster, namespace, keyword string) (ret []models.AutoScale, err error) {
	db := models.DB().Model(&models.AutoScale{})
	if cluster != "" {
		db.Where("cluster = ?", cluster)
	}
	if namespace != "" {
		db.Where("namespace = ?", namespace)
	}
	if keyword != "" {
		db.Where("app LIKE ?", "%"+keyword+"%")
	}
	err = db.Order("id desc").Find(&ret).Error
	return
}
