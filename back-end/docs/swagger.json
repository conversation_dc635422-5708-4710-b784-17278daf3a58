{"swagger": "2.0", "info": {"description": "k8s应用管理系统API接口文档\n◉ 线下地址：k8s-app.firstshare.cn\n◉ 线上地址：k8s-app.foneshare.cn", "title": "k8s应用管理系统API", "contact": {}, "version": "1.0"}, "paths": {"/jacoco/coverage/download": {"post": {"description": "下载的文件名为：{podName}-jacoco.zip， 解压后的文件结构为：\n<div>--------------------</div>\n<pre>\nclasses              (存放class文件里目录，包含了部署模块的class文件）\njacoco.exec         （jacoco覆盖率文件）\ncommitID.txt        （部署模块运行的CommitID）\ngitRef.txt          （部署模块运行的分支或者tag名）\njars                （存放jar包的目录，里面存放请求参数里指定的jar包）\n└─jar001-1.0.0.jar  （示例的jar文件）\n└─jar002-1.1.0.jar  （示例的jar文件）\n</pre>\n<div>--------------------</div>", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["jaco<PERSON>"], "summary": "生成覆盖率文件并下载，此接口耗时较长，建议调用方配置超时时间为2分钟以上", "parameters": [{"description": "json格式的请求体", "name": "RequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/openapi.DownloadCoverageParam"}}], "responses": {"200": {"description": "下载的文件", "schema": {"type": "file"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}, "/jacoco/coverage/reset": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["jaco<PERSON>"], "summary": "重置覆盖率", "parameters": [{"description": "json格式的请求体", "name": "RequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/openapi.ResetCoverageParam"}}], "responses": {"200": {"description": "操作成功", "schema": {"$ref": "#/definitions/web.Response"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}, "/k8s/app/node-drain": {"post": {"description": "对节点下的pod进行驱逐操作，只会对服务等级为L2和L3的pod进行驱逐", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["oncall"], "summary": "节点驱逐", "parameters": [{"description": "json格式的请求体", "name": "RequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/openapi.OncallWebhookParam"}}, {"type": "string", "default": "false", "description": "是否试运行（只记录日志，不实际操作。可选值：true | false ）", "name": "dryrun", "in": "query", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/web.Response"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}, "/k8s/app/scale-up": {"post": {"description": "对应用的副本数进行扩容操作", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["oncall"], "summary": "应用副本数扩容", "parameters": [{"description": "json格式的请求体", "name": "RequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/openapi.OncallWebhookParam"}}, {"type": "string", "default": "false", "description": "是否试运行（只记录日志，不实际操作。可选值：true | false ）", "name": "dryrun", "in": "query"}, {"type": "string", "default": "false", "description": "是否摘除Pod（当告警资源为pod时有效。可选值：true | false ）", "name": "podDeregister", "in": "query"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/web.Response"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}, "/k8s/app/thread-dump": {"post": {"description": "对pod下的Java线程进行dump操作", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["oncall"], "summary": "dump pod下的Java线程", "parameters": [{"description": "json格式的请求体", "name": "RequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/openapi.OncallWebhookParam"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/web.Response"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}, "/openapi/app/git-project/version": {"get": {"produces": ["application/json"], "tags": ["app"], "summary": "查询应用所使用Git项目的版本信息", "parameters": [{"type": "string", "description": "运行环境", "name": "namespace", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/openapi.GitProjectVersionResponse"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}, "/openapi/app/module/version": {"get": {"produces": ["application/json"], "tags": ["app"], "summary": "查询服务部署模的版本信息", "parameters": [{"type": "string", "description": "运行环境", "name": "namespace", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/openapi.AppModuleVersionResponse"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}, "/openapi/app/restart": {"post": {"produces": ["application/json"], "tags": ["app"], "summary": "重启应用", "parameters": [{"description": "json格式的请求体", "name": "RequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/openapi.AppRestartParam"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/web.Response"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}, "/openapi/app/server/detail": {"get": {"produces": ["application/json"], "tags": ["app"], "summary": "获取应用服务信息", "parameters": [{"type": "string", "description": "k8s集群名", "name": "cluster", "in": "query", "required": true}, {"type": "string", "description": "运行环境", "name": "namespace", "in": "query", "required": true}, {"type": "string", "description": "应用名", "name": "app", "in": "query", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/openapi.AppServerDetailResponse"}}, "400": {"description": "请求参数错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}, "500": {"description": "服务器内部错误，错误信息在message字段中。", "schema": {"$ref": "#/definitions/web.Response"}}}}}}, "definitions": {"dto.DeployModule": {"type": "object", "properties": {"commitId": {"type": "string"}, "commitIdShort": {"type": "string"}, "contextPath": {"type": "string"}, "gitUrl": {"type": "string"}, "module": {"type": "string"}, "tag": {"type": "string"}}}, "dto.Deployment": {"type": "object", "properties": {"cluster": {"type": "string"}, "container0Image": {"description": "第一个容器的镜像", "type": "string"}, "container0ImageTag": {"type": "string"}, "container0Name": {"type": "string"}, "createTime": {"type": "string"}, "deployModules": {"type": "array", "items": {"$ref": "#/definitions/dto.DeployModule"}}, "deployRemark": {"type": "string"}, "deployTag": {"type": "string"}, "deployTime": {"type": "string"}, "deployUser": {"type": "string"}, "language": {"type": "string"}, "lastModifyUser": {"type": "string"}, "limitCpu": {"type": "integer"}, "limitMemory": {"type": "integer"}, "name": {"type": "string"}, "namespace": {"type": "string"}, "replicas": {"type": "integer"}, "requestCpu": {"type": "integer"}, "requestMemory": {"type": "integer"}, "revision": {"type": "string"}, "updateTime": {"type": "string"}}}, "openapi.AppModuleVersionResponse": {"description": "响应结构体", "type": "object", "properties": {"code": {"description": "状态码", "type": "integer", "example": 200}, "data": {"description": "数据", "type": "array", "items": {"$ref": "#/definitions/openapi.AppModuleVersionResponseData"}}, "message": {"description": "状态信息描述", "type": "string", "example": "success"}}}, "openapi.AppModuleVersionResponseData": {"description": "服务版本信息查询返回结构体", "type": "object", "properties": {"app": {"description": "发布系统上的应用名", "type": "string", "example": "fs-k8s-tomcat-test"}, "cluster": {"description": "k8s集群名", "type": "string", "example": "k8s1"}, "gitCommitId": {"description": "代码提交ID", "type": "string", "example": "51e1001334abc0257a340e5791fbf578b06bb627"}, "gitRef": {"description": "代码分支或标签", "type": "string", "example": "master"}, "gitSubDir": {"description": "代码仓库子目录", "type": "string", "example": "fs-k8s-tomcat-test-biz"}, "gitUrl": {"description": "代码仓库地址", "type": "string", "example": "https://git.firstshare.cn/devops/fs-k8s-tomcat-test.git"}, "namespace": {"description": "k8s命名空间 （运行环境）", "type": "string", "example": "fstest"}}}, "openapi.AppRestartParam": {"type": "object", "required": ["app", "cluster", "namespace", "token"], "properties": {"app": {"type": "string", "example": "fs-k8s-tomcat-test"}, "cluster": {"type": "string", "example": "k8s1"}, "namespace": {"type": "string", "example": "fstest"}, "token": {"type": "string", "example": "1234567890"}}}, "openapi.AppServerDetailResponse": {"description": "响应结构体", "type": "object", "properties": {"code": {"description": "状态码", "type": "integer", "example": 200}, "data": {"description": "数据", "allOf": [{"$ref": "#/definitions/dto.Deployment"}]}, "message": {"description": "状态信息描述", "type": "string", "example": "success"}}}, "openapi.DownloadCoverageParam": {"description": "覆盖率下载参数结构体", "type": "object", "required": ["app", "appModule", "cluster", "namespace"], "properties": {"app": {"description": "发布系统上的应用名", "type": "string", "example": "fs-crm-sfa"}, "appModule": {"description": "应用的部署模块， 格式为：git地址@模块名", "type": "string", "example": "https://git.firstshare.cn/sfa/fs-crm-sfa.git@fs-crm-web"}, "cluster": {"description": "k8s集群名", "type": "string", "example": "k8s0"}, "jars": {"description": "需要下载的jar包", "type": "array", "items": {"type": "string"}, "example": ["fs-metadata-common.jar", "fs-metadata-core.jar"]}, "namespace": {"description": "k8s命名空间 （运行环境）", "type": "string", "example": "jaco<PERSON>"}}}, "openapi.GitProjectVersionResponse": {"description": "响应结构体", "type": "object", "properties": {"code": {"description": "状态码", "type": "integer", "example": 200}, "data": {"description": "数据", "type": "array", "items": {"$ref": "#/definitions/openapi.GitProjectVersionResponseData"}}, "message": {"description": "状态信息描述", "type": "string", "example": "success"}}}, "openapi.GitProjectVersionResponseData": {"description": "Git项目版本信息查询返回结构体", "type": "object", "properties": {"apps": {"description": "被引用的应用列表", "type": "array", "items": {"type": "string"}, "example": ["fs-k8s-tomcat-test-biz"]}, "appsWithEnv": {"type": "array", "items": {"type": "string"}, "example": ["k8s1/fstest/fs-k8s-tomcat-test-biz"]}, "gitCommitId": {"description": "代码提交ID", "type": "string", "example": "51e1001334abc0257a340e5791fbf578b06bb627"}, "gitRef": {"description": "代码提交标签", "type": "string", "example": "master"}, "gitUrl": {"type": "string", "example": "https://git.firstshare.cn/devops/fs-k8s-tomcat-test.git"}, "modules": {"description": "被应用引用的模块列表。空值表示该项目的根模块", "type": "array", "items": {"type": "string"}, "example": ["fs-k8s-tomcat-test-biz", "fs-k8s-tomcat-test-provider"]}, "owners": {"description": "被引用的应用的所有负责人信息", "type": "array", "items": {"type": "string"}, "example": ["吴志辉", "刘全胜"]}}}, "openapi.OncallWebhookParam": {"description": "Oncall服务Webhook参数", "type": "object", "required": ["alert_env", "alert_status", "alertname", "namespace", "resource_id", "resource_name", "resource_type"], "properties": {"alert_env": {"description": "告警环境", "type": "string", "example": "k8s0"}, "alert_level": {"description": "告警等级", "type": "string", "example": "WARN"}, "alert_source": {"description": "告警来源", "type": "string", "example": "GRAFANA"}, "alert_status": {"description": "告警状态", "type": "string", "example": "FIRING"}, "alertname": {"description": "告警名称", "type": "string", "example": "pod-cpu-throttled"}, "namespace": {"description": "运行环境", "type": "string", "example": "foneshare"}, "resource_id": {"description": "资源ID", "type": "string", "example": "fs-k8s-tomcat-test-5d8874c9dd-6bg6q"}, "resource_name": {"description": "资源名称", "type": "string", "example": "fs-k8s-tomcat-test"}, "resource_type": {"description": "资源类型", "type": "string", "example": "app"}}}, "openapi.ResetCoverageParam": {"description": "重置覆盖率参数结构体", "type": "object", "required": ["app", "cluster", "namespace"], "properties": {"app": {"description": "发布系统上的应用名", "type": "string", "example": "fs-crm-sfa"}, "cluster": {"description": "k8s集群名", "type": "string", "example": "k8s0"}, "namespace": {"description": "k8s命名空间 （运行环境）", "type": "string", "example": "jaco<PERSON>"}}}, "web.Response": {"description": "响应结构体", "type": "object", "properties": {"code": {"description": "状态码", "type": "integer", "example": 200}, "data": {"description": "数据"}, "message": {"description": "状态信息描述", "type": "string", "example": "success"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "BasicAuth": {"type": "basic"}}}