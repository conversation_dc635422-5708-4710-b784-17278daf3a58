package k8s_templates

import (
	"testing"
)

func TestBuildDeployment(t *testing.T) {
	// Skip this test due to config dependency
	t.<PERSON><PERSON>("Skipping BuildDeployment test due to config file dependency")
}

func TestBuildDeploymentWithName(t *testing.T) {
	t.<PERSON><PERSON>("Skipping BuildDeploymentWithName test due to config file dependency")
}

func TestBuildService(t *testing.T) {
	t.Skip("Skipping BuildService test due to config file dependency")
}

func TestBuildIngress(t *testing.T) {
	t.Skip("Skipping BuildIngress test due to config file dependency")
}

func TestBuildHpa(t *testing.T) {
	t.Skip("Skipping BuildHpa test due to config file dependency")
}

func TestBuildPodAutoScaler(t *testing.T) {
	t.Skip("Skipping BuildPodAutoScaler test due to config file dependency")
}

func TestBuildPodMonitor(t *testing.T) {
	t.Ski<PERSON>("Skipping BuildPodMonitor test due to config file dependency")
}

func TestGetFileContent(t *testing.T) {
	t.<PERSON>("Skipping GetFileContent test due to config file dependency")
}

func TestBuildContentWithFilename(t *testing.T) {
	t.Skip("Skipping BuildContentWithFilename test due to config file dependency")
}

func TestGetFilePath(t *testing.T) {
	t.Skip("Skipping GetFilePath test due to config file dependency")
}

// Test parameter structures
func TestDeploymentParamStruct(t *testing.T) {
	// Test that we can create a DeploymentParam struct
	param := DeploymentParam{
		App:       "test-app",
		Namespace: "test-namespace",
		AppImage:  "nginx:latest",
		Replicas:  3,
	}

	if param.App != "test-app" {
		t.Errorf("Expected App to be 'test-app', got %q", param.App)
	}

	if param.Namespace != "test-namespace" {
		t.Errorf("Expected Namespace to be 'test-namespace', got %q", param.Namespace)
	}

	if param.Replicas != 3 {
		t.Errorf("Expected Replicas to be 3, got %d", param.Replicas)
	}
}

func TestServiceParamStruct(t *testing.T) {
	// Test that we can create a ServiceParam struct
	param := ServiceParam{
		App:       "test-service",
		Namespace: "default",
		Ports: []Port{
			{Name: "http", Port: 80, TargetPortStr: "8080"},
		},
	}

	if param.App != "test-service" {
		t.Errorf("Expected App to be 'test-service', got %q", param.App)
	}

	if len(param.Ports) != 1 {
		t.Errorf("Expected 1 port, got %d", len(param.Ports))
	}

	if param.Ports[0].Name != "http" {
		t.Errorf("Expected port name to be 'http', got %q", param.Ports[0].Name)
	}
}
