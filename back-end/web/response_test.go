package web

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestResponse_Structure(t *testing.T) {
	// Test Response struct creation and field access
	response := Response{
		Code:    200,
		Message: "Success",
		Data:    map[string]string{"key": "value"},
	}

	if response.Code != 200 {
		t.<PERSON>rf("Expected Code to be 200, got %d", response.Code)
	}

	if response.Message != "Success" {
		t.<PERSON>rf("Expected Message to be 'Success', got %q", response.Message)
	}

	if response.Data == nil {
		t.<PERSON><PERSON>("Expected Data to be non-nil")
	}
}

func TestSuccessJson(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Test SuccessJson with data
	testData := map[string]interface{}{
		"message": "test successful",
		"count":   42,
	}

	SuccessJson(c, testData)

	// Check status code
	if w.Code != http.StatusOK {
		t.<PERSON>rrorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	// Check response body
	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response.Code != CODE_SUCCESS {
		t.Errorf("Expected response code %d, got %d", CODE_SUCCESS, response.Code)
	}

	if response.Message != "success" {
		t.Errorf("Expected message 'success', got %q", response.Message)
	}

	if response.Data == nil {
		t.Errorf("Expected data to be non-nil")
	}
}

func TestSuccessJsonWithNilData(t *testing.T) {
	gin.SetMode(gin.TestMode)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Test SuccessJson with nil data
	SuccessJson(c, nil)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response.Data != nil {
		t.Errorf("Expected data to be nil, got %v", response.Data)
	}
}

func TestFailJson(t *testing.T) {
	gin.SetMode(gin.TestMode)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Test FailJson with custom message
	errorMessage := "Something went wrong"
	errorCode := CODE_SERVER_ERROR
	FailJson(c, errorCode, errorMessage)

	// Check status code (FailJson returns 200 with error code in body)
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	// Check response body
	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response.Code != errorCode {
		t.Errorf("Expected response code %d, got %d", errorCode, response.Code)
	}

	if response.Message != errorMessage {
		t.Errorf("Expected message %q, got %q", errorMessage, response.Message)
	}
}

func TestFailJsonWithFailStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Test FailJsonWithFailStatus
	errorMessage := "Authentication required"
	errorCode := CODE_CLIENT_ERROR
	FailJsonWithFailStatus(c, errorCode, errorMessage)

	// Check status code (FailJsonWithFailStatus returns the actual error code)
	if w.Code != errorCode {
		t.Errorf("Expected status code %d, got %d", errorCode, w.Code)
	}

	// Check response body
	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response.Code != errorCode {
		t.Errorf("Expected response code %d, got %d", errorCode, response.Code)
	}

	if response.Message != errorMessage {
		t.Errorf("Expected message %q, got %q", errorMessage, response.Message)
	}
}

func TestRedirectJson(t *testing.T) {
	gin.SetMode(gin.TestMode)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Test RedirectJson
	redirectData := map[string]string{"url": "https://example.com"}
	RedirectJson(c, redirectData)

	// Check status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	// Check response body
	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response.Code != CODE_REDIRECT {
		t.Errorf("Expected response code %d, got %d", CODE_REDIRECT, response.Code)
	}

	if response.Message != "redirect" {
		t.Errorf("Expected message 'redirect', got %q", response.Message)
	}

	if response.Data == nil {
		t.Errorf("Expected data to be non-nil")
	}
}

func TestResponseWithComplexData(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	
	// Test with complex data structure
	complexData := map[string]interface{}{
		"users": []map[string]interface{}{
			{"id": 1, "name": "Alice", "active": true},
			{"id": 2, "name": "Bob", "active": false},
		},
		"metadata": map[string]interface{}{
			"total":    2,
			"page":     1,
			"per_page": 10,
		},
		"timestamp": "2023-12-25T10:30:00Z",
	}
	
	SuccessJson(c, complexData)
	
	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}
	
	// Verify complex data is preserved
	if response.Data == nil {
		t.Errorf("Expected data to be non-nil")
	}
	
	// Convert back to map for verification
	dataMap, ok := response.Data.(map[string]interface{})
	if !ok {
		t.Errorf("Expected data to be a map")
	}
	
	if dataMap["timestamp"] != "2023-12-25T10:30:00Z" {
		t.Errorf("Expected timestamp to be preserved")
	}
}

func TestResponseJSONSerialization(t *testing.T) {
	// Test that Response can be properly serialized to JSON
	response := Response{
		Code:    200,
		Message: "Test message",
		Data: map[string]interface{}{
			"string":  "value",
			"number":  42,
			"boolean": true,
			"array":   []int{1, 2, 3},
			"object":  map[string]string{"nested": "value"},
		},
	}
	
	jsonData, err := json.Marshal(response)
	if err != nil {
		t.Fatalf("Failed to marshal response to JSON: %v", err)
	}
	
	// Test that it can be unmarshaled back
	var unmarshaled Response
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal response from JSON: %v", err)
	}
	
	if unmarshaled.Code != response.Code {
		t.Errorf("Expected code %d, got %d", response.Code, unmarshaled.Code)
	}
	
	if unmarshaled.Message != response.Message {
		t.Errorf("Expected message %q, got %q", response.Message, unmarshaled.Message)
	}
}

// Benchmark tests
func BenchmarkSuccessJson(b *testing.B) {
	gin.SetMode(gin.TestMode)

	testData := map[string]interface{}{
		"message": "benchmark test",
		"count":   100,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		SuccessJson(c, testData)
	}
}

func BenchmarkFailJson(b *testing.B) {
	gin.SetMode(gin.TestMode)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		FailJson(c, CODE_SERVER_ERROR, "benchmark error")
	}
}

func BenchmarkResponseSerialization(b *testing.B) {
	response := Response{
		Code:    200,
		Message: "benchmark",
		Data:    map[string]interface{}{"key": "value"},
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		json.Marshal(response)
	}
}
