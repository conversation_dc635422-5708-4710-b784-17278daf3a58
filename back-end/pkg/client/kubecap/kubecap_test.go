package kubecap

import (
	"strings"
	"testing"
	"time"
)

func TestNode(t *testing.T) {
	tests := []struct {
		name     string
		cluster  string
		nodeType string
	}{
		{
			name:     "Node with empty type",
			cluster:  "test-cluster",
			nodeType: "",
		},
		{
			name:     "Node with specific type",
			cluster:  "test-cluster",
			nodeType: "worker",
		},
		{
			name:     "Node with dedicated type",
			cluster:  "test-cluster",
			nodeType: "dedicated",
		},
		{
			name:     "Empty cluster",
			cluster:  "",
			nodeType: "worker",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return error due to missing kubecap binary or cluster
			// But we test that the function doesn't panic
			output, err := Node(tt.cluster, tt.nodeType)
			
			if err != nil {
				t.Logf("Node returned error (expected in test environment): %v", err)
			}
			
			// output should be a string (even if empty)
			if output != "" {
				t.Logf("Node(%q, %q) returned output: %s", tt.cluster, tt.nodeType, output)
			} else {
				t.Logf("Node(%q, %q) returned empty output (expected in test environment)", tt.cluster, tt.nodeType)
			}
		})
	}
}

func TestPod(t *testing.T) {
	tests := []struct {
		name    string
		cluster string
		node    string
	}{
		{
			name:    "Valid cluster and node",
			cluster: "test-cluster",
			node:    "worker-node-1",
		},
		{
			name:    "Empty cluster",
			cluster: "",
			node:    "worker-node-1",
		},
		{
			name:    "Empty node",
			cluster: "test-cluster",
			node:    "",
		},
		{
			name:    "Both empty",
			cluster: "",
			node:    "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return error due to missing kubecap binary or cluster
			// But we test that the function doesn't panic
			output, err := Pod(tt.cluster, tt.node)
			
			if err != nil {
				t.Logf("Pod returned error (expected in test environment): %v", err)
			}
			
			// output should be a string (even if empty)
			if output != "" {
				t.Logf("Pod(%q, %q) returned output: %s", tt.cluster, tt.node, output)
			} else {
				t.Logf("Pod(%q, %q) returned empty output (expected in test environment)", tt.cluster, tt.node)
			}
		})
	}
}

func TestCmd(t *testing.T) {
	tests := []struct {
		name    string
		cluster string
		param   string
	}{
		{
			name:    "Basic util command",
			cluster: "test-cluster",
			param:   "--util",
		},
		{
			name:    "Node labels command",
			cluster: "test-cluster",
			param:   "--util --node-labels fxiaoke.com/dedicated=worker",
		},
		{
			name:    "Pods command",
			cluster: "test-cluster",
			param:   "--node-labels kubernetes.io/hostname=worker-1 --pods --util",
		},
		{
			name:    "Empty param",
			cluster: "test-cluster",
			param:   "",
		},
		{
			name:    "Empty cluster",
			cluster: "",
			param:   "--util",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return error due to missing kubecap binary or cluster
			// But we test that the function doesn't panic
			output, err := cmd(tt.cluster, tt.param)
			
			if err != nil {
				t.Logf("cmd returned error (expected in test environment): %v", err)
			}
			
			// output should be a string (even if empty)
			if output != "" {
				t.Logf("cmd(%q, %q) returned output: %s", tt.cluster, tt.param, output)
			} else {
				t.Logf("cmd(%q, %q) returned empty output (expected in test environment)", tt.cluster, tt.param)
			}
		})
	}
}

func TestCmdWithTimeout(t *testing.T) {
	tests := []struct {
		name    string
		cluster string
		param   string
		timeout time.Duration
	}{
		{
			name:    "Short timeout",
			cluster: "test-cluster",
			param:   "--util",
			timeout: 1 * time.Second,
		},
		{
			name:    "Long timeout",
			cluster: "test-cluster",
			param:   "--util",
			timeout: 60 * time.Second,
		},
		{
			name:    "Zero timeout",
			cluster: "test-cluster",
			param:   "--util",
			timeout: 0,
		},
		{
			name:    "Negative timeout",
			cluster: "test-cluster",
			param:   "--util",
			timeout: -1 * time.Second,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// In test environment, this will likely return error due to missing kubecap binary or cluster
			// But we test that the function doesn't panic
			output, err := cmdWithTimeout(tt.cluster, tt.param, tt.timeout)
			
			if err != nil {
				t.Logf("cmdWithTimeout returned error (expected in test environment): %v", err)
				
				// Check if it's a timeout error for very short timeouts
				if tt.timeout > 0 && tt.timeout < 100*time.Millisecond {
					if strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "killed") {
						t.Logf("Got expected timeout error for short timeout")
					}
				}
			}
			
			// output should be a string (even if empty)
			if output != "" {
				t.Logf("cmdWithTimeout(%q, %q, %v) returned output: %s", tt.cluster, tt.param, tt.timeout, output)
			} else {
				t.Logf("cmdWithTimeout(%q, %q, %v) returned empty output", tt.cluster, tt.param, tt.timeout)
			}
		})
	}
}

// Test parameter validation and edge cases
func TestParameterValidation(t *testing.T) {
	// Test with very long cluster names
	longCluster := strings.Repeat("a", 1000)
	output, err := Node(longCluster, "worker")
	if err != nil {
		t.Logf("Node with long cluster name returned error (expected): %v", err)
	}
	t.Logf("Node with long cluster name returned output length: %d", len(output))
	
	// Test with special characters in cluster name
	specialCluster := "cluster-with-特殊字符-and-symbols!@#$%"
	output, err = Node(specialCluster, "worker")
	if err != nil {
		t.Logf("Node with special cluster name returned error (expected): %v", err)
	}
	t.Logf("Node with special cluster name returned output length: %d", len(output))
	
	// Test with very long parameters
	longParam := strings.Repeat("--util ", 100)
	output, err = cmd("test-cluster", longParam)
	if err != nil {
		t.Logf("cmd with long param returned error (expected): %v", err)
	}
	t.Logf("cmd with long param returned output length: %d", len(output))
}

// Test concurrent access
func TestConcurrentKubecapOperations(t *testing.T) {
	const numGoroutines = 3
	const numOperations = 2
	
	done := make(chan bool, numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()
			
			for j := 0; j < numOperations; j++ {
				// Test concurrent access to kubecap functions
				Node("test-cluster", "worker")
				Pod("test-cluster", "worker-node-1")
				cmd("test-cluster", "--util")
			}
		}(i)
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		select {
		case <-done:
			// Goroutine completed
		case <-time.After(30 * time.Second):
			t.Fatalf("Timeout waiting for goroutine %d to complete", i)
		}
	}
}

// Test command construction
func TestCommandConstruction(t *testing.T) {
	// Test that Node function constructs correct parameters
	tests := []struct {
		name         string
		nodeType     string
		expectedParam string
	}{
		{
			name:         "Empty node type",
			nodeType:     "",
			expectedParam: "--util",
		},
		{
			name:         "Worker node type",
			nodeType:     "worker",
			expectedParam: "--util --node-labels fxiaoke.com/dedicated=worker",
		},
		{
			name:         "Dedicated node type",
			nodeType:     "dedicated",
			expectedParam: "--util --node-labels fxiaoke.com/dedicated=dedicated",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// We can't easily test the internal parameter construction without modifying the code
			// But we can test that different node types produce different behaviors
			output1, err1 := Node("test-cluster", tt.nodeType)
			output2, err2 := Node("test-cluster", "different-type")
			
			// The outputs might be the same due to errors, but the function calls should complete
			t.Logf("Node with type %q: output len=%d, error=%v", tt.nodeType, len(output1), err1)
			t.Logf("Node with different type: output len=%d, error=%v", len(output2), err2)
		})
	}
}

// Test error handling
func TestErrorHandling(t *testing.T) {
	// Test with invalid cluster names that might cause command failures
	invalidClusters := []string{
		"invalid-cluster",
		"cluster with spaces",
		"cluster/with/slashes",
		"cluster|with|pipes",
		"cluster;with;semicolons",
	}

	for _, cluster := range invalidClusters {
		t.Run("ErrorHandling_"+cluster, func(t *testing.T) {
			output, err := Node(cluster, "worker")
			
			// We expect errors for invalid cluster names
			if err != nil {
				t.Logf("Node with invalid cluster %q returned expected error: %v", cluster, err)
			} else {
				t.Logf("Node with cluster %q unexpectedly succeeded with output: %s", cluster, output)
			}
		})
	}
}

// Benchmark tests
func BenchmarkNode(b *testing.B) {
	cluster := "test-cluster"
	nodeType := "worker"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Node(cluster, nodeType)
	}
}

func BenchmarkPod(b *testing.B) {
	cluster := "test-cluster"
	node := "worker-node-1"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Pod(cluster, node)
	}
}

func BenchmarkCmd(b *testing.B) {
	cluster := "test-cluster"
	param := "--util"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		cmd(cluster, param)
	}
}
