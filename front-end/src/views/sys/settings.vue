<template>
  <div class="app-container" v-loading="loading">
    <div v-if="!this.editMode">
      <div style="margin-bottom: 15px;">
        <el-link type="primary" :underline="false" @click="editToggle">编辑</el-link>
        <el-button
          type="text"
          style="padding: 0;float: right;"
          @click="clear_cache">清除系统缓存
        </el-button>
      </div>
      <vue-json-pretty
        :data="this.$settings"
        v-if="!this.editMode"
      >
      </vue-json-pretty>
    </div>
    <div v-else>
      <div style="margin-bottom: 15px;">
        <el-button @click="editToggle" size="small">取消</el-button>
        <el-button type="primary" @click="editSubmit" size="small">提交</el-button>
      </div>
      <el-input type="textarea" v-model="settingsJson" autosize></el-input>
    </div>
  </div>
</template>

<script>
import VueJsonPretty from 'vue-json-pretty'
import {clearCache, updateSettings} from "@/api/sys";

export default {
  data() {
    return {
      loading: false,
      editMode: false,
      settingsJson: "",
    }
  },
  mounted() {
  },
  methods: {
    clear_cache() {
      this.loading = true
      clearCache().then(response => {
        this.$message.success("操作成功");
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    editToggle() {
      this.$message({
        message: '修改方式： 发布系统里修改ConfigMap文件 -> 提交Git -> 通过GitLab CI 部署ConfigMap文件 -> [发布系统会自动发现文件变更并重新加载配置]',
        duration: '8000',
        type: 'warning'
      });

      // this.editMode = !this.editMode;
      // this.settingsJson = JSON.stringify(this.$settings, null, 2);
    },
    editSubmit() {
      this.loading = true
      updateSettings(this.settingsJson).then(response => {
        this.$message.success("修改成功，即将刷新页面内容");
        setTimeout(function () {
          self.location.reload();
        }, 1000);
      }).catch((e) => {
        this.$message.error("操作失败，原因：" + e.message);
      }).finally(() => {
        this.loading = false
      })
    },
  },
  components: {
    VueJsonPretty
  },
}
</script>

