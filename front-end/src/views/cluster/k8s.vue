<template>
  <div>
    <div style="font-size: 12px">
      <b>名称列表：</b>
      <span v-for="(it,idx) in this.clusters">{{ it.name }}、</span>
    </div>
    <div style="font-size: 12px;margin-bottom: 10px;">
      <b>描述列表：</b>
      <span v-for="(it,idx) in this.clusters">{{ it.description }}、</span>
    </div>
    <div style="position: absolute;right: 0;top: 0;">
      <div style="display: inline-block;margin-right: 10px;font-size: 12px;">
        显示：
        <el-checkbox v-model="showFields.autoScale" style="margin-right: 10px;">自动扩缩容</el-checkbox>
        <el-checkbox v-model="showFields.autoScaleV2" style="margin-right: 10px;">自动扩缩容V2</el-checkbox>
        <el-checkbox v-model="showFields.cronScale" style="margin-right: 10px;">定时扩缩容</el-checkbox>
        <el-checkbox v-model="showFields.imageRegistryProxy" style="margin-right: 10px;">镜像代理库</el-checkbox>
        <el-checkbox v-model="showFields.cloudCategory" style="margin-right: 10px;">云类型</el-checkbox>
        <el-checkbox v-model="showFields.namespaces" style="margin-right: 10px;">命名空间</el-checkbox>
      </div>
      <export-button :table-ref="this.$refs.table001"></export-button>
    </div>
    <el-card class="box-card">
      <el-table
        ref="table001"
        :data="this.clusters"
        row-key="name"
      >
        <el-table-column
          type="index"
          width="50">
        </el-table-column>
        <el-table-column label="" width="120">
          <template slot-scope="scope">
            <el-button type="text" @click="image_preheat_dialog(scope.row.name)">镜像预热</el-button>
            <div style="color: #555;font-size: 12px;">job数：{{ scope.row.imagePreheatJobs }}</div>
          </template>
        </el-table-column>
        <el-table-column label="集群名" prop="name">
        </el-table-column>
        <el-table-column label="集群描述" prop="description">
        </el-table-column>
        <el-table-column label="版本号" prop="version">
        </el-table-column>
        <el-table-column label="Node VIP" prop="nodeVIP">
        </el-table-column>
        <el-table-column label="状态" prop="disable" width="80">
          <template slot-scope="scope">
            <div v-if="scope.row.enable" style="color: #14b614;font-weight: bold;">开启</div>
            <div v-else style="color: red;">关闭</div>
          </template>
        </el-table-column>
        <el-table-column label="自动扩缩" prop="autoScale" v-if="showFields.autoScale" width="90">
          <template slot-scope="scope">
            <div v-if="scope.row.autoScale" style="color: #14b614;font-weight: bold;">开启</div>
            <div v-else style="color: red;">关闭</div>
          </template>
        </el-table-column>
        <el-table-column label="自动扩缩V2" v-if="showFields.autoScaleV2" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.autoScaleV2 === 'true'" style="color: #14b614;font-weight: bold;">开启</div>
            <div v-else-if="scope.row.autoScaleV2 === 'false'" style="color: red;">关闭</div>
            <div v-else>{{ scope.row.autoScaleV2 }}</div>
          </template>
        </el-table-column>
        <el-table-column label="定时扩缩" prop="cronScale" v-if="showFields.cronScale" width="90">
          <template slot-scope="scope">
            <div v-if="scope.row.cronScale" style="color: #14b614;font-weight: bold;">开启</div>
            <div v-else style="color: red;">关闭</div>
          </template>
        </el-table-column>
        <el-table-column label="镜像库代理" prop="imageRegistryProxy" v-if="showFields.imageRegistryProxy">
        </el-table-column>
        <el-table-column label="云类型" prop="cloudCategory" v-if="showFields.cloudCategory">
        </el-table-column>
        <el-table-column label="命名空间" prop="namespaces" v-if="showFields.namespaces">
          <template slot-scope="scope">
            {{ scope.row.namespaces }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog title="镜像预热" :visible.sync="imagePreheat.dialogVisible" v-loading="imagePreheatLoading">
      <el-form label-width="100px">
        <el-form-item label="集群名" style="margin-bottom: 5px;">
          {{ imagePreheat.cluster }}
        </el-form-item>
        <el-form-item label="预热并发数" style="margin-bottom: 5px;">
          &nbsp;&nbsp;&nbsp;&nbsp;使用
          <el-input-number v-model="imagePreheat.imagePullParallel" controls-position="right" :min="1" :max="5" :step="1" style="width: 120px; "></el-input-number>
          个并发数来同时拉取镜像
        </el-form-item>
        <el-form-item label="应用镜像" style="margin-bottom: 5px;">
          对最近
          <el-input-number v-model="imagePreheat.appImageRecentHours" controls-position="right" :min="0" :max="480" :step="24" style="width: 120px; "></el-input-number>
          小时内构建的应用镜像进行预热
        </el-form-item>
        <el-form-item label="命名空间" style="margin-bottom: 5px;">
          <el-select v-model="imagePreheat.namespaces" multiple placeholder="请选择命名空间" style="width: 100%;">
            <el-option
              v-for="namespace in ['foneshare-gray','foneshare-stage','foneshare-urgent','foneshare']"
              :key="namespace"
              :label="namespace"
              :value="namespace">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="基础镜像" style="margin-bottom: 5px;">
          <el-input
            type="textarea"
            :rows="8"
            placeholder="镜像全称、按行分割"
            v-model="imagePreheat.baseImages">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="imagePreheat.dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="image_preheat()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import {getImagePreheatJob, imagePreheat} from "@/api/k8s/cluster";
import {cloneObject} from "@/utils/my-util";
import ExportButton from "@/views/components/export-button.vue";
import {getClustersAutoScalerV2} from "@/api/k8s/scale";

export default {
  name: "k8sCluster",
  data() {
    return {
      imagePreheatLoading: false,
      clusters: [],
      showFields: {
        autoScale: true,
        autoScaleV2: true,
        cronScale: true,
        cloudCategory: false,
        namespaces: false,
        imageRegistryProxy: true,
      },
      imagePreheat: {
        dialogVisible: false,
        cluster: "",
        baseImages: "",
        namespaces: ["foneshare-gray", "foneshare-stage"],
        imagePullParallel: 1,
        appImageRecentHours: 240
      }
    }
  },
  components: {ExportButton},
  mounted() {
    for (let clu of this.$settings.clusters) {
      let item = cloneObject(clu);
      item["imagePreheatJobs"] = "--"
      item["autoScaleV2"] = "--"
      this.clusters.push(item)
      this.image_preheat_jobs(item.name)
    }
    this.init_base_images();
    //加载集群的自动扩缩容V2状态
    getClustersAutoScalerV2().then(response => {
      for (let item of this.clusters) {
        if (response.data) {
          if (response.data[item.name]) {
            item["autoScaleV2"] = "true"
          } else {
            item["autoScaleV2"] = "false"
          }
        }
      }
    }).catch((e) => {
      console.error(e.message)
    }).finally(() => {
    });
  },
  computed: {},
  methods: {
    image_preheat_dialog(cluster) {
      this.imagePreheat.cluster = cluster;
      this.imagePreheat.dialogVisible = true;
    },
    init_base_images() {
      let items = this.clusters.filter(cluster => cluster.name === "k8s0")
      if (items.length > 0) {
        this.imagePreheat.baseImages = items[0].baseImages.filter(it => it.includes("tomcat8:") || it.includes("tomcat9:")).join("\n");
      }
    },
    image_preheat_jobs(cluster) {
      getImagePreheatJob(cluster, "kube-public").then(response => {
        for (let it of this.clusters) {
          if (it.name === cluster) {
            it.imagePreheatJobs = `${response.data.length}`;
          }
        }
      }).catch((e) => {
        console.error(e.message)
      }).finally(() => {
      })
    },
    image_preheat() {
      this.$confirm('镜像预热时可能会占用较多的网络带宽，确认继续吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.imagePreheatLoading = true;
        let params = {
          "cluster": this.imagePreheat.cluster,
          "baseImages": this.imagePreheat.baseImages.split("\n"),
          "namespaces": this.imagePreheat.namespaces,
          "imagePullParallel": this.imagePreheat.imagePullParallel,
          "appImageRecentHours": this.imagePreheat.appImageRecentHours
        }
        imagePreheat(params).then(response => {
          this.$message.success(`${this.imagePreheat.cluster}: 已成功触发镜像预热任务，详情进度请查看集预热服务日志`)
          this.imagePreheat.dialogVisible = false;
        }).catch((e) => {
          this.$message.error(e.message)
        }).finally(() => {
          this.imagePreheatLoading = false;
        })
      }).catch(() => {
      });
    }
  }
}
</script>

