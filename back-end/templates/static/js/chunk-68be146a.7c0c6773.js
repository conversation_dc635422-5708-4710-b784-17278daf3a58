(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-68be146a"],{"02f4":function(e,t,a){var i=a("4588"),l=a("be13");e.exports=function(e){return function(t,a){var n,o,r=String(l(t)),s=i(a),c=r.length;return s<0||s>=c?e?"":void 0:(n=r.charCodeAt(s),n<55296||n>56319||s+1===c||(o=r.charCodeAt(s+1))<56320||o>57343?e?r.charAt(s):n:e?r.slice(s,s+2):o-56320+(n-55296<<10)+65536)}}},"0da1":function(e,t,a){"use strict";a("5e12")},"279d":function(e,t,a){"use strict";a("97ab")},"59bf":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container image-build-container",staticStyle:{margin:"0",padding:"0"}},[a("menu-tabs",{attrs:{"tab-name":"image-build"}}),e._v(" "),a("maintain-alert",{attrs:{"maintain-type":"ci"}}),e._v(" "),a("app-selector2",{attrs:{"update-history":!0,showDetail:!0},on:{change:e.appChange}}),e._v(" "),a("job-runner-alert",{ref:"jobRunnerAlert",attrs:{app:this.currApp,"job-type":"CI"}}),e._v(" "),a("div",{staticStyle:{"min-height":"180px",margin:"10px"}},[a("div",{staticStyle:{"padding-top":"10px"}},[a("div",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-position"},on:{click:e.imageBuildDialog}},[e._v("批量构建镜像")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text",icon:"el-icon-price-tag"},on:{click:function(t){return e.gitTagPage(null)}}},[e._v("创建Tag")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-position"},on:{click:e.pipelinePage}},[e._v("去发布")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"30px",color:"#E6A23C"},attrs:{type:"text",icon:"el-icon-position"},on:{click:e.urgentDeploy}},[e._v("紧急构建")])],1),e._v(" "),a("div",{staticStyle:{clear:"both"}})]),e._v(" "),a("el-table",{ref:"deployModuleTable",attrs:{data:e.tableData,"element-loading-text":"数据加载中...","highlight-selection-row":!0,fit:""}},[a("el-table-column",{attrs:{type:"selection",align:"center",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{label:"",width:"300",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{icon:"el-icon-search",type:"text"},on:{click:function(a){return e.imagePage(t.row)}}},[e._v("查看镜像")]),e._v(" "),a("el-button",{staticStyle:{display:"none"},attrs:{type:"text",icon:"el-icon-price-tag"},on:{click:function(a){return e.gitTagPage(t.row.gitUrl)}}},[e._v("创建Tag")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-s-order"},on:{click:function(a){return e.jobHistoryPage(t.row.gitUrl,t.row.module)}}},[e._v("构建记录")]),e._v(" "),a("el-button",{staticStyle:{padding:"5px 7px","margin-left":"10px"},attrs:{type:"primary",size:"mini",icon:"el-icon-position"},on:{click:function(a){return e.showDialog1([t.row])}}},[e._v("构建镜像")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"Git地址",prop:"gitUrl"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:t.row.gitUrl,target:"_blank"}},[e._v(e._s(t.row.gitUrl))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"Maven子模块",prop:"module"}}),e._v(" "),a("el-table-column",{attrs:{label:"默认编译环境"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.mavenImage.split("/").pop()))])]}}])})],1)],1),e._v(" "),a("el-dialog",{staticClass:"deploy-dialog",attrs:{visible:e.dialog1Visible,"close-on-click-modal":!1,top:"5vh",width:"860px"},on:{"update:visible":function(t){e.dialog1Visible=t}}},[a("template",{slot:"title"},[e._v("\n      部署模块镜像构建（"+e._s(this.currApp)+"）\n      "),a("span",{staticStyle:{"padding-left":"10px",color:"#777","font-size":"12px"}})]),e._v(" "),a("el-form",{ref:"buildFormRef",attrs:{model:e.buildForm,"label-width":"100px",rules:e.buildFormRules}},[e._l(e.buildForm.modules,(function(t,i){return a("div",[a("el-form-item",{staticClass:"module-git-url",staticStyle:{padding:"0",margin:"0 0 -5px 0"},attrs:{label:""}},[a("span",{staticStyle:{"font-size":"12px",color:"#b4532a","font-weight":"bold"}},[e._v("\n            部署模块： "+e._s(t.gitUrl)+" --- "+e._s(t.module)+"\n          ")])]),e._v(" "),a("el-form-item",{attrs:{label:"版本号"}},[a("div",{staticStyle:{display:"inline-block",width:"400px"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},model:{value:e.buildForm.modules[i].tag,callback:function(t){e.$set(e.buildForm.modules[i],"tag",t)},expression:"buildForm.modules[index].tag"}},[e.getVersionOptions(t.gitUrl).branchOptions.length>0?a("el-option-group",{attrs:{label:"Git分支"}},e._l(e.getVersionOptions(t.gitUrl).branchOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}},[a("span",[a("b",[e._v(e._s(t.name))])])])})),1):e._e(),e._v(" "),a("el-option-group",{attrs:{label:"GitTag (message)"}},e._l(e.getVersionOptions(t.gitUrl).tagOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}},[a("span",[a("b",[e._v(e._s(t.name))]),a("span",{staticStyle:{"font-size":"12px",color:"#888","padding-left":"5px"}},[e._v(e._s(t.message?" ("+t.message.substring(0,30)+")":""))])])])})),1)],1)],1),e._v(" "),a("div",{staticStyle:{display:"inline-block",width:"300px","text-align":"right"}},[a("b",{staticStyle:{color:"#606266"}},[e._v("编译环境")]),e._v(" "),a("el-select",{staticStyle:{width:"220px"},attrs:{filterable:""},model:{value:e.buildForm.modules[i].mavenImage,callback:function(t){e.$set(e.buildForm.modules[i],"mavenImage",t)},expression:"buildForm.modules[index].mavenImage"}},e._l(e.mavenOptions,(function(e){return a("el-option",{key:e,attrs:{label:e.split("/").pop(),value:e}})})),1)],1)])],1)})),e._v(" "),a("el-form-item",{attrs:{label:"父POM",prop:"parentPom"}},[a("div",{staticStyle:{display:"inline-block",width:"700px"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.buildForm.parentPom,callback:function(t){e.$set(e.buildForm,"parentPom",t)},expression:"buildForm.parentPom"}},[e._l(this.$settings.parentPoms,(function(t){return a("el-option",{attrs:{label:t.name,value:t.value}},[a("span",[e._v(e._s(t.name)+" ")]),e._v(" "),a("span",{staticStyle:{"padding-left":"20px",color:"#8492a6","font-size":"13px"}},[a("label",{staticStyle:{color:"orangered","font-size":"12px"}},[e._v(e._s(t.desc))])])])})),e._v(" "),a("div",{staticStyle:{"margin-top":"10px","font-size":"12px",color:"#888","margin-left":"20px"}},[e._v("\n              如果要查看父POM所管理的Jar包及其版本信息，请点击 "),a("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:"https://git.firstshare.cn/JavaCommon/parent-pom/-/blob/master/version-diff.md",target:"_blank"}},[e._v("链接")])])],2)],1),e._v(" "),a("div",{staticStyle:{display:"inline-block"}},[a("el-tooltip",{staticClass:"item",staticStyle:{float:"right"},attrs:{effect:"light",placement:"left"}},[a("template",{staticStyle:{"line-height":"1.2em"},slot:"content"},[a("p",[e._v("不同父POM中定义的依赖包版本区别如下：")]),e._v(" "),a("p",[a("b",[e._v("测试版（Alpha)：")]),e._v(" 包含未经测试的高版本依赖包，只在线下环境可用")]),e._v(" "),a("p",[a("b",[e._v("候选版（RC): ")]),e._v(" 通过了线下环境测试的依赖包版本，或者一些紧急Bug修复的依赖包版本")]),e._v(" "),a("p",[a("b",[e._v("稳定版 (RELEASE)：")]),e._v(" 在候选版通过充分测试后，需要进入到全网的依赖包版本")]),e._v(" "),a("el-divider",{staticClass:"env-divider"}),e._v(" "),a("p",[e._v("依赖包版本的升级常规流程如下：首先进入【测试版】在线下环境进行测试，通过后进入"),a("br"),e._v("【候选版】在线上环境进行试用,通过后进入【稳定版】推广到全网。")])],1),e._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1)]),e._v(" "),a("el-form-item",{attrs:{label:"备注信息"}},[a("div",{staticStyle:{display:"inline-block",width:"700px"}},[a("el-input",{attrs:{type:"textarea",rows:2,maxlength:32,max:32},model:{value:e.buildForm.remark,callback:function(t){e.$set(e.buildForm,"remark",t)},expression:"buildForm.remark"}})],1)]),e._v(" "),a("el-form-item",[a("el-checkbox",{staticStyle:{margin:"0 0 0 0"},attrs:{label:"如果镜像存在，则覆盖"},model:{value:e.buildForm.forceCodeCompile,callback:function(t){e.$set(e.buildForm,"forceCodeCompile",t)},expression:"buildForm.forceCodeCompile"}}),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[e._v("\n            默认情况下，当同名镜像已经存在的时候，不允许直接构建镜像，需要选择覆盖。勾选此选项后，会强制重新编译代码并覆盖原有镜像。\n          ")]),e._v(" "),a("i",{staticClass:"el-icon-info"})],2),e._v(" "),a("el-checkbox",{staticStyle:{margin:"0 0 0 30px"},attrs:{label:"执行单元测试"},model:{value:e.buildForm.unitTest,callback:function(t){e.$set(e.buildForm,"unitTest",t)},expression:"buildForm.unitTest"}}),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[e._v("\n            代码在进行Maven编译时，是否执行单元测试\n          ")]),e._v(" "),a("i",{staticClass:"el-icon-info"})],2),e._v(" "),a("el-checkbox",{staticStyle:{margin:"0 0 0 30px"},attrs:{label:"依赖包版本校验",disabled:""},model:{value:e.buildForm.dependencyCheck,callback:function(t){e.$set(e.buildForm,"dependencyCheck",t)},expression:"buildForm.dependencyCheck"}}),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[e._v("\n            对工程的依赖Jar包进行两方面检测。1：同一jar包是否存在多个版本；2：某个jar包版本是否低于平台所要求的最低版本\n          ")]),e._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"提示"}},[a("el-alert",{attrs:{closable:!1,type:"info"}},[a("template",{slot:"title"},[a("div",[a("b",[e._v("镜像版本号生成机制:")])]),e._v(" "),a("div",[e._v("\n              1. 如果版本号为tag，则直接使用该名称"),a("br"),e._v("\n              2. 如果版本号为分支，则添加时间后缀。比如 master 会被替换为 master--202406041130"),a("br"),e._v("\n              3. 如果版本号中包含了斜线 /，则将其替换为 ---。比如 version/910 会被替换为 version---910"),a("br")])])],2)],1)],2),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialog1Visible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.buildBtnLoading,expression:"buildBtnLoading"}],attrs:{type:"primary"},on:{click:e.buildSubmit}},[e._v("开始构建")])],1)],2)],1)},l=[],n=(a("1c4c"),a("ac6a"),a("5df3"),a("4f7f"),a("7f7f"),a("2d63")),o=a("db72"),r=a("b562"),s=a("6a4c"),c=a("b144"),p=a("76fe"),u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-selector-wrapper"},[a("el-form",{ref:"searchForm",attrs:{inline:!0,model:e.searchForm,rules:e.rules}},[a("el-form-item",{attrs:{label:"k8s集群",prop:"cluster"}},[a("el-select",{attrs:{placeholder:"选择k8s集群"},on:{change:e.loadApp},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[a("el-select",{attrs:{placeholder:"选择Namespace"},on:{change:e.loadApp},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-select",{staticStyle:{width:"340px"},attrs:{filterable:"",placeholder:"选择应用"},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app",t)},expression:"searchForm.app"}},e._l(this.appOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1)},d=[],m=a("8504"),b={name:"AppSelector",props:{},data:function(){return{searchForm:{cluster:"",namespace:"",app:""},rules:{cluster:[{required:!0,message:"请选择k8s集群"}],namespace:[{required:!0,message:"请选择运行环境"}],app:[{required:!0,message:"请输入应用名"}]},cache:{}}},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(n["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(i){t.e(i)}finally{t.f()}}return[]}},beforeDestroy:function(){},methods:{loadApp:function(){var e=this.searchForm.cluster,t=this.searchForm.namespace;if(e&&t){var a=e+"##"+t+"##app",i=this.cache[a];if(i)this.appOptions=i;else{var l=this;Object(m["g"])(e,t).then((function(e){l.appOptions=e.data,l.cache[a]=e.data})).catch((function(e){console.error(e)}))}}else this.appOptions=[]}}},g=b,f=(a("279d"),a("2877")),v=Object(f["a"])(g,u,d,!1,null,null,null),h=v.exports,_=a("71df"),y=a("84d4"),k=a("bcbd"),x=a("57c3"),w={name:"image-build",watch:{currApp:function(e){this.loadTableData()}},data:function(){return{loading:!1,tableData:[],currApp:"",dialog1Visible:!1,buildOptions:{versionOptions:{}},buildBtnLoading:!1,buildForm:{app:"",modules:[],unitTest:!1,forceCodeCompile:!1,dependencyCheck:!0,parentPom:"",remark:""},buildFormRules:{parentPom:[{required:!0,message:"请选择父POM"}]}}},components:{MaintainAlert:x["a"],JobRunnerAlert:k["a"],MenuTabs:y["a"],AppSelector2:_["a"],AppSelector:h},computed:{mavenOptions:function(){return this.$settings.mavenImages||[]}},mounted:function(){},methods:{loadTableData:function(){var e=this;this.loading=!0,this.currApp?Object(r["n"])(this.currApp).then((function(t){if(e.tableData=t.data,1===e.tableData.length&&"true"===e.$route.query.openDialog){e.$refs.deployModuleTable.toggleAllSelection();var a=e;setTimeout((function(){a.imageBuildDialog();var t=Object(o["a"])({},e.$route.query);delete t["openDialog"],a.$router.push({query:t})}),300)}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1})):this.tableData=[]},appChange:function(e){this.currApp=e,this.updateQueryParam(),this.loadTableData()},updateQueryParam:function(){var e=Object(c["a"])(this.$route.query);e["app"]=this.currApp,this.$router.push({query:e})},getVersionOptions:function(e){return this.buildOptions.versionOptions[e]||{tagOptions:[],branchOptions:[]}},urgentDeploy:function(){this.$alert('如果需要紧急构建，请申请临时操作权限。 点击 <a href="/#/auth/temp-path" target="_blank" style="color: #3a8ee6">链接</a> 打开临时授权申请页面',"紧急构建提示",{dangerouslyUseHTMLString:!0})},versionIsBranch:function(e,t){var a,i=this.getVersionOptions(e),l=Object(n["a"])(i.branchOptions);try{for(l.s();!(a=l.n()).done;){var o=a.value;if(o.name===t)return!0}}catch(r){l.e(r)}finally{l.f()}return!1},isProdEnv:function(){return window.location.host.indexOf("foneshare")>-1},imageBuildDialog:function(){var e=this.$refs.deployModuleTable.store.states.selection;!e||e.length<1?this.$message.warning("请选择部署模块"):this.showDialog1(e)},showDialog1:function(e){var t=this;this.loading=!0;var a,i=new Set,l=[],o=Object(n["a"])(e);try{for(o.s();!(a=o.n()).done;){var r=a.value;i.add(r.gitUrl),l.push({gitUrl:r.gitUrl,module:r.module,mavenImage:r.mavenImage,tag:""})}}catch(c){o.e(c)}finally{o.f()}Object(s["c"])({gitUrls:Array.from(i)}).then((function(e){t.buildOptions.versionOptions=e.data,t.buildForm.modules=l,t.dialog1Visible=!0})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))},buildSubmit:function(){var e=this;this.$refs["buildFormRef"].validate((function(t){if(t){var a,i={items:[]},l=Object(c["a"])(e.buildForm),o=Object(n["a"])(l.modules);try{for(o.s();!(a=o.n()).done;){var r=a.value;i.items.push({app:e.currApp,gitUrl:r.gitUrl,gitModule:r.module,gitTag:r.tag,mavenImage:r.mavenImage,unitTest:l.unitTest,forceCodeCompile:l.forceCodeCompile,dependencyCheck:l.dependencyCheck,parentPom:l.parentPom,remark:l.remark})}}catch(s){o.e(s)}finally{o.f()}e.buildBtnLoading=!0,Object(p["a"])(i).then((function(t){1===t.data.length?e.jobPage(t.data[0].jobId):e.jobHistoryPage("",""),e.dialog1Visible=!1})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.buildBtnLoading=!1}))}}))},gitTagPage:function(e){var t={app:this.currApp};e&&(t.gitUrl=e);var a=this.$router.resolve({name:"git-tag",query:t});window.open(a.href,"_blank")},imagePage:function(e){var t=this.$router.resolve({name:"cicd-image-list",query:{gitUrl:e.gitUrl,gitModule:e.module}});window.open(t.href,"_blank")},jobPage:function(e){var t=this.$router.resolve({name:"cicd-image-build-detail",query:{jobId:e}});window.open(t.href,"_blank")},jobHistoryPage:function(e,t){var a=this.$router.resolve({name:"cicd-image-build-history",query:{gitUrl:e,gitModule:t,author:""}});window.open(a.href,"_blank")},pipelinePage:function(){var e=this.$router.resolve({name:"cicd-app-deploy",query:{app:this.currApp}});window.open(e.href,"_blank")}}},S=w,F=(a("0da1"),Object(f["a"])(S,i,l,!1,null,null,null));t["default"]=F.exports},"5e12":function(e,t,a){},"97ab":function(e,t,a){}}]);