(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-10a52e78"],{"5b06":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"cms-config-migrate"}}),t._v(" "),a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.form}},[a("el-form-item",{attrs:{label:"模式",prop:"sourceCluster"}},[a("el-select",{model:{value:t.form.op,callback:function(e){t.$set(t.form,"op",e)},expression:"form.op"}},[a("el-option",{attrs:{label:"NodePort转Service",value:"nodeport-to-service"}}),t._v(" "),a("el-option",{attrs:{label:"Service短名转长名",value:"serviceShort-to-serviceLong"}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"环境",prop:"sourceCluster"}},[a("el-select",{staticStyle:{width:"360px"},attrs:{"value-key":"id",filterable:""},model:{value:t.form.sourceCluster,callback:function(e){t.$set(t.form,"sourceCluster",e)},expression:"form.sourceCluster"}},t._l(t.clusterOptions,(function(t){return a("el-option",{key:t.id,attrs:{label:t.cluster+"/"+t.namespace,value:t}})})),1),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.loadData}},[t._v("查询")])],1)],1)],1),t._v(" "),a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.data,size:"mini"}},[a("el-table-column",{attrs:{prop:"name",label:"文件"}}),t._v(" "),a("el-table-column",{attrs:{prop:"profile",label:"配置组"}}),t._v(" "),a("el-table-column",{attrs:{prop:"editor",label:"最后修改人"}}),t._v(" "),a("el-table-column",{attrs:{label:"内容",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return t.cmsContentPreview(e.row)}}},[t._v("查看")])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"addrs",label:"地址","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.repairAddrs,(function(o,n){return a("span",{staticStyle:{margin:"2px 5px",padding:"0 2px",border:"1px solid #e2e2e2",display:"inline-block"}},[t._v("\n            "+t._s(n)+"\n            "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text",size:"mini"},on:{click:function(a){return t.editCMS(e.row.id)}}},[t._v("修改")]),t._v(" "),a("el-tooltip",{attrs:{content:n+" -> "+o,placement:"right"}},[a("el-button",{staticStyle:{"margin-left":"5px"},attrs:{type:"text",size:"mini"},on:{click:function(a){return t.batchEditCMS(e.row.profile,n,o)}}},[t._v("批量替换")])],1)],1)}))}}])})],1)],1),t._v(" "),a("div",[a("el-dialog",{attrs:{title:t.cmsPreview.name+" ("+t.cmsPreview.profile+")",visible:t.cmsPreview.visible,width:"50%"},on:{"update:visible":function(e){return t.$set(t.cmsPreview,"visible",e)}}},[a("div",{staticStyle:{"margin-top":"-40px",border:"1px solid #e2e2e2",padding:"5px","max-height":"600px",overflow:"auto"}},[a("pre",[t._v(t._s(t.cmsPreview.content))])])])],1)],1)},n=[],r=(a("7f7f"),a("2d63")),c=a("c1ab"),l=a("a68b"),i={components:{appManageTab:l["a"]},data:function(){return{form:{op:"nodeport-to-service",sourceCluster:{cluster:null,namespace:null}},data:[],loading:!1,cmsPreview:{visible:!1,name:"",profile:"",content:""}}},computed:{clusterOptions:function(){var t,e=[],a=0,o=Object(r["a"])(this.$settings.clusters);try{for(o.s();!(t=o.n()).done;){var n,c=t.value,l=Object(r["a"])(c.namespaces);try{for(l.s();!(n=l.n()).done;){var i=n.value,u={};u.cluster=c.name,u.namespace=i,u.id=a,e.push(u),a++}}catch(s){l.e(s)}finally{l.f()}}}catch(s){o.e(s)}finally{o.f()}return e}},methods:{loadData:function(){var t=this;this.form.sourceCluster.cluster&&this.form.sourceCluster.namespace?(this.loading=!0,Object(c["n"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.op).then((function(e){t.data=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))):this.$message.error("请选择环境")},editCMS:function(t){var e="/api/page/redirect?type=cmsEdit&cmsId=".concat(t,"&_t")+Date.now();window.open(e)},batchEditCMS:function(t,e,a){var o="/api/page/redirect?type=cmsBatchEdit&profile=".concat(t,"&oldContent=").concat(e,"&newContent=").concat(a,"&_t")+Date.now();window.open(o)},cmsContentPreview:function(t){this.cmsPreview.name=t.name,this.cmsPreview.profile=t.profile,this.cmsPreview.content=t.content,this.cmsPreview.visible=!0}}},u=i,s=a("2877"),p=Object(s["a"])(u,o,n,!1,null,"9d6aec22",null);e["default"]=p.exports},a68b:function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"自动扩缩容-批量创建",name:"auto-scale-batch-create"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},n=[],r=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var a=t.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"auto-scale-batch-create"===a?this.$router.push({name:"tool-auto-scale-batch-create"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),c=r,l=(a("d54f"),a("2877")),i=Object(l["a"])(c,o,n,!1,null,null,null);e["a"]=i.exports},c1ab:function(t,e,a){"use strict";a.d(e,"j",(function(){return n})),a.d(e,"l",(function(){return r})),a.d(e,"z",(function(){return c})),a.d(e,"A",(function(){return l})),a.d(e,"d",(function(){return i})),a.d(e,"h",(function(){return u})),a.d(e,"D",(function(){return s})),a.d(e,"F",(function(){return p})),a.d(e,"y",(function(){return d})),a.d(e,"b",(function(){return m})),a.d(e,"g",(function(){return f})),a.d(e,"C",(function(){return b})),a.d(e,"E",(function(){return v})),a.d(e,"x",(function(){return h})),a.d(e,"G",(function(){return g})),a.d(e,"m",(function(){return y})),a.d(e,"e",(function(){return _})),a.d(e,"a",(function(){return j})),a.d(e,"B",(function(){return O})),a.d(e,"k",(function(){return w})),a.d(e,"i",(function(){return x})),a.d(e,"s",(function(){return C})),a.d(e,"v",(function(){return $})),a.d(e,"w",(function(){return k})),a.d(e,"o",(function(){return S})),a.d(e,"p",(function(){return P})),a.d(e,"t",(function(){return E})),a.d(e,"f",(function(){return z})),a.d(e,"u",(function(){return D})),a.d(e,"c",(function(){return M})),a.d(e,"q",(function(){return N})),a.d(e,"r",(function(){return T})),a.d(e,"n",(function(){return q}));var o=a("b775");function n(t){return Object(o["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function r(t){return Object(o["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(o["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function l(t){return Object(o["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function i(){return Object(o["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function u(t){return Object(o["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function s(t){return Object(o["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(o["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(o["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(o["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(t,e,a,n,r,c,l,i){return Object(o["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(r,"&fixVersion=").concat(e,"&suffixVersion=").concat(a,"&message=").concat(n,"&dependencyCheck=").concat(c,"&parentPom=").concat(l),method:"post",data:i})}function b(t){return Object(o["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function v(t){return Object(o["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function h(t){return Object(o["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(o["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,a,n){return Object(o["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&overrideNamespace=").concat(n),method:"post"})}function _(t,e,a,n,r){return Object(o["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(a,"&remark=").concat(n,"&dryRun=").concat(r),method:"post"})}function j(){return Object(o["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function O(){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function w(t){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function x(t){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function C(t){return Object(o["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function $(t){return Object(o["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function k(t){return Object(o["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function S(t){return Object(o["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function P(t){return Object(o["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function E(t){return Object(o["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function z(t){return Object(o["a"])({url:"/v1/tool/autoscale-batch-create",method:"post",data:t})}function D(t,e){return Object(o["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function M(t){return Object(o["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function N(t){return Object(o["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function T(t){return Object(o["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function q(t,e,a){return Object(o["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(a),method:"get"})}},d54f:function(t,e,a){"use strict";a("eba1")},eba1:function(t,e,a){}}]);