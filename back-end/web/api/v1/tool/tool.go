package tool

import (
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/cms"
	"fs-k8s-app-manager/pkg/client/harbor"
	"fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/client/kubectl/pascli"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/auto_scale_service"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/commondata_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/tool/harbor_tool"
	"io"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	mapset "github.com/deckarep/golang-set/v2"
	"github.com/ghodss/yaml"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	appV1 "k8s.io/api/apps/v1"
)

type FindJarWorkerResult struct {
	items    []string
	ownerSet mapset.Set[string]
}

type FindTomcatVersionWorkerResult struct {
	items    []string
	ownerSet mapset.Set[string]
}

type PipelineResourceUpdateParam struct {
	Cluster       string `json:"cluster"`
	Namespace     string `json:"namespace"`
	App           string `json:"app"`
	RequestCPU    int32  `json:"requestCPU"`
	RequestMemory int32  `json:"requestMemory"`
	LimitCPU      int32  `json:"limitCPU"`
	LimitMemory   int32  `json:"limitMemory"`
	Replicas      int32  `json:"replicas"`
}

type AppVersionSnapshotResult struct {
	Cluster               string                `json:"cluster"`
	Namespace             string                `json:"namespace"`
	App                   string                `json:"app"`
	AppOwner              string                `json:"appOwner"`
	Message               string                `json:"message"`
	ArtifactImageSnapshot string                `json:"artifactImageSnapshot"`
	Image                 string                `json:"imageBackup"`
	DeployModule          datatype.DeployModule `json:"deployModule"`
}

//func AppGC(c *gin.Context) {
//	//user, _ := app_auth.GetUser(c)
//	//if !user.IsAdmin() {
//	//	web.FailJson(c, web.CODE_SERVER_ERROR, "此操作比较重，只有系统管理员才能执行当前操作")
//	//	return
//	//}
//	data := make(map[string]map[string]string)
//
//	apps, err := app_service.FindAll()
//	if err != nil {
//		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
//		return
//	}
//
//	pipes, err := pipeline_service.FindAll()
//	if err != nil {
//		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
//		return
//	}
//
//	findPipelineByApp :=func() {}
//
//
//}

func ScanJar(c *gin.Context) {
	//user, _ := app_auth.GetUser(c)
	//if !user.IsAdmin() {
	//	web.FailJson(c, web.CODE_SERVER_ERROR, "此操作比较重，只有系统管理员才能执行当前操作")
	//	return
	//}
	var p ScanJarParam
	var pipes []models.Pipeline
	var err error
	if err = c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if len(p.JarPrefix) < 8 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "为了避免无意义的扫描，Jar包名前缀的长度必须大于7")
		return
	}
	cacheKey := "scanJarJob"

	if v, found := cache.GetStr(key.Pre().TOOL.Key(cacheKey)); found {
		err = fmt.Errorf("已经有扫描任务正在进行中, 扫描Jar: %s", v)
	}
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	if p.Namespace == "" {
		pipes, err = pipeline_service.FindByCluster(p.Cluster)
	} else {
		pipes, err = pipeline_service.FindByClusterAndNamespace(p.Cluster, p.Namespace)
	}
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	go func() {
		cache.SetStr(key.Pre().TOOL.Key(cacheKey), p.JarPrefix, 2*time.Hour)
		apps := make([]string, 0, 200)
		ownerSet := mapset.NewSet[string]()

		taskSize := 50
		taskCount := len(pipes) / taskSize
		if len(pipes)%taskSize != 0 {
			taskCount += 1
		}
		wg := sync.WaitGroup{}
		wg.Add(taskCount)

		for i := 0; i < taskCount; i++ {
			idx := i
			go func() {
				start := idx * taskSize
				end := (idx + 1) * taskSize
				if end > len(pipes) {
					end = len(pipes)
				}
				ret := findJarWorker(pipes[start:end], p.JarPrefix)
				apps = append(apps, ret.items...)
				ownerSet = ownerSet.Union(ret.ownerSet)
				wg.Done()
			}()
		}
		wg.Wait()
		data := make(map[string]interface{})
		data["owners"] = ownerSet
		data["apps"] = apps
		log_service.Create(auth.GetRealName(c), "jar-scan", fmt.Sprintf("%s/%s/%s", p.Cluster, p.Namespace, p.JarPrefix), data)
		cache.Delete(key.Pre().TOOL.Key(cacheKey))
	}()
	web.SuccessJson(c, "操作成功，正在扫描中，结果请稍后在审计日志下查询")
}

func findJarWorker(pipes []models.Pipeline, jarPrefix string) FindJarWorkerResult {
	ret := FindJarWorkerResult{
		items:    make([]string, 0, len(pipes)),
		ownerSet: mapset.NewSet[string](),
	}
	for _, pi := range pipes {
		log.Info("scan-jar-task: scan ", pi.FullName())
		pods, err := k8s_service.ListPod(pi.Cluster, pi.Namespace, pi.App)
		if err != nil {
			log.Error("can't found pod, ", pi.Cluster, pi.Namespace, pi.App, err.Error())
			continue
		}
		if len(pods) == 0 {
			continue
		}

		isSpringBootJar := false
		if strings.EqualFold(pi.EnvValue(constant.ENV_KEY_SPRING_BOOT_JAR_APP), "true") {
			isSpringBootJar = true
		}
		if jarFiles, err := kubectl.ListJarFile(pi.Cluster, pi.Namespace, pods[0].Name, isSpringBootJar); err != nil {
			log.Error("can't found jar files, pod:", pods[0].Name, err.Error())
			continue
		} else {
			jars := make([]string, 0, 2)
			for _, jar := range jarFiles {
				if strings.HasPrefix(jar, jarPrefix) {
					if !strslice.Find(jars, jar) {
						jars = append(jars, jar)
					}
				}
			}
			if len(jars) > 0 {
				owners := cmdb_service.GetMainOwners(pi.App)
				ownerNames := "-"
				for _, owner := range owners {
					ret.ownerSet.Add(owner)
					ownerNames = owner + " "
				}

				for _, jar := range jars {
					ret.items = append(ret.items, fmt.Sprintf("%s,%s,%s,%s,%s", pi.App, pi.Namespace, pi.Cluster,
						jar, ownerNames))
				}
			}
		}
	}
	return ret
}

func ScanTomcatVersion(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有系统管理员才能执行当前操作")
		return
	}

	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	if cluster == "" || namespace == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "cluster和namespace不能为空")
		return
	}

	var pipes []models.Pipeline
	var err error
	cacheKey := "scanTomcatVersionJob"

	if _, found := cache.GetStr(key.Pre().TOOL.Key(cacheKey)); found {
		err = fmt.Errorf("已经有扫描任务正在进行中, 操作人: %s", user.RealName)
	}
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	if namespace == "" {
		pipes, err = pipeline_service.FindByCluster(cluster)
	} else {
		pipes, err = pipeline_service.FindByClusterAndNamespace(cluster, namespace)
	}
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	go func() {
		cache.SetStr(key.Pre().TOOL.Key(cacheKey), user.Username, 2*time.Hour)
		apps := make([]string, 0, 200)
		ownerSet := mapset.NewSet[string]()

		taskSize := 50
		taskCount := len(pipes) / taskSize
		if len(pipes)%taskSize != 0 {
			taskCount += 1
		}
		wg := sync.WaitGroup{}
		wg.Add(taskCount)

		for i := 0; i < taskCount; i++ {
			idx := i
			go func() {
				start := idx * taskSize
				end := (idx + 1) * taskSize
				if end > len(pipes) {
					end = len(pipes)
				}
				ret := findTomcatVersionWorker(pipes[start:end])
				apps = append(apps, ret.items...)
				ownerSet = ownerSet.Union(ret.ownerSet)
				wg.Done()
			}()
		}
		wg.Wait()
		data := make(map[string]interface{})
		data["owners"] = ownerSet
		data["apps"] = apps
		log_service.Create(auth.GetRealName(c), "tomcat-version-scan", fmt.Sprintf("%s/%s", cluster, namespace), data)
		cache.Delete(key.Pre().TOOL.Key(cacheKey))
	}()
	web.SuccessJson(c, "操作成功，正在扫描中，结果请稍后在审计日志下查询")
}

func findTomcatVersionWorker(pipes []models.Pipeline) FindTomcatVersionWorkerResult {
	ret := FindTomcatVersionWorkerResult{
		items:    make([]string, 0, len(pipes)),
		ownerSet: mapset.NewSet[string](),
	}
	for _, pi := range pipes {
		log.Info("scan-tomcat-version-task: scan ", pi.FullName())
		pods, err := k8s_service.ListPod(pi.Cluster, pi.Namespace, pi.App)
		if err != nil {
			log.Error("can't found pod, ", pi.Cluster, pi.Namespace, pi.App, err.Error())
			continue
		}
		if len(pods) == 0 {
			continue
		}

		isSpringBootJar := strings.EqualFold(pi.EnvValue(constant.ENV_KEY_SPRING_BOOT_JAR_APP), "true")
		tomcatVersion := ""
		if strings.Contains(strings.ToLower(pods[0].Container0Image), "tong-web") || strings.Contains(strings.ToLower(pods[0].Container0Image), "tongweb") {
			tomcatVersion = "Error: 服务使用了东方通TongWeb"
		} else {
			tomcatVersion = kubectl.GetTomcatVersion(pi.Cluster, pi.Namespace, pods[0].Name, isSpringBootJar)
		}
		owners := cmdb_service.GetMainOwners(pi.App)
		ownerNames := "-"
		for _, owner := range owners {
			ret.ownerSet.Add(owner)
			ownerNames = owner + " "
		}

		ret.items = append(ret.items, fmt.Sprintf("%s,%s,%s,%s,%s", pi.App, pi.Namespace, pi.Cluster,
			tomcatVersion, ownerNames))
	}
	return ret
}

func FindAppByAddress(c *gin.Context) {
	address := c.Query("address")
	if strings.Contains(address, "//") {
		address = address[strings.LastIndex(address, "//")+1:]
	}
	items := strings.Split(address, ":")
	if len(items) != 2 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "address is invalid, format must be [ip:port], address: "+address)
		return
	}

	cacheKey := key.Pre().TOOL.Key("findAppByAddress#" + address)
	if d, found := cache.GetStruct(cacheKey, map[string]string{}); found {
		web.SuccessJson(c, d)
		return
	}

	vip := items[0]
	pVal, err := strconv.ParseInt(items[1], 10, 32)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "port must be number, address: "+address)
		return
	}
	nodePort := int32(pVal)
	cluser := settings.GetSetting().GetClusterByVip(vip)

	if cluser == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "can't found cluster, vip: "+vip)
		return
	}
	data := make(map[string]string)
NamespaceLoop:
	for _, ns := range cluser.Namespaces {
		services, err := k8s_service.ListService(cluser.Name, ns)
		if err != nil {
			log.Warnf("can't list services, cluster: %s, ns: %s", cluser.Name, ns)
		}
		for _, ser := range services {
			for _, port := range ser.Ports {
				if port.NodePort == nodePort {
					data["cluster"] = cluser.Name
					data["namespace"] = ser.Namespace
					data["name"] = ser.Name
					break NamespaceLoop
				}
			}
		}
	}
	_ = cache.SetStruct(cacheKey, data, 1*time.Hour)
	if len(data) == 0 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("can't found service, vip: %s, port: %d", vip, nodePort))
		return
	}
	web.SuccessJson(c, data)
}

func FindPodByIP(c *gin.Context) {
	address := c.Query("address")
	if ip := net.ParseIP(address); ip == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "is not ip: "+address)
		return
	}
	cluster := settings.GetSetting().GetClusterByPodIP(address)
	if cluster == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "can't found cluster, addr: "+address)
		return
	}
	data := make(map[string]string)
	for _, ns := range cluster.Namespaces {
		pod, err := k8s_service.GetPodByIP(cluster.Name, ns, address)
		if err == nil {
			data["cluster"] = cluster.Name
			data["namespace"] = pod.Namespace
			data["name"] = pod.LabelApp
			data["pod"] = pod.Name
			break
		}
	}
	if len(data) == 0 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("can't found pod, addr: %s", address))
		return
	}
	web.SuccessJson(c, data)
}

func YamlExport(c *gin.Context) {
	var err error
	reqBody, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	var items []YamlExportItem
	err = json.Unmarshal(reqBody, &items)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	results := make([]YamlExportResultItem, 0, len(items))
	for _, it := range items {
		dep, err := k8s_service.DeploymentDetail(it.Cluster, it.Namespace, it.App)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		v, found := dep.Annotations["kubectl.kubernetes.io/last-applied-configuration"]
		if !found {
			errMsg := fmt.Sprintf("%s/%s/%s deployment annotation kubectl.kubernetes.io/last-applied-configuration not exit!",
				it.Cluster, it.Namespace, it.App)
			web.FailJson(c, web.CODE_SERVER_ERROR, errMsg)
			return
		}
		depYaml, err := yaml.JSONToYAML([]byte(v))
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}

		ser, err := k8s_service.ServiceDetail(it.Cluster, it.Namespace, it.App)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		v, found = ser.Annotations["kubectl.kubernetes.io/last-applied-configuration"]
		if !found {
			errMsg := fmt.Sprintf("%s/%s/%s service annotation kubectl.kubernetes.io/last-applied-configuration not exit!",
				it.Cluster, it.Namespace, it.App)
			web.FailJson(c, web.CODE_SERVER_ERROR, errMsg)
			return
		}
		serYaml, err := yaml.JSONToYAML([]byte(v))
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}

		results = append(results, YamlExportResultItem{
			YamlExportItem: it,
			Deployment:     string(depYaml),
			Service:        string(serYaml),
		})
	}

	retItems := make([]string, 0, len(results)*6)

	for _, it := range results {
		retItems = append(retItems, fmt.Sprintf("# deployment from %s/%s/%s", it.Cluster, it.Namespace, it.App))
		retItems = append(retItems, it.Deployment)
		retItems = append(retItems, "---")
		retItems = append(retItems, fmt.Sprintf("# service from %s/%s/%s", it.Cluster, it.Namespace, it.App))
		retItems = append(retItems, it.Service)
		retItems = append(retItems, "---")
		retItems = append(retItems, "\n")
	}

	data := strings.Join(retItems, "\n")

	web.SuccessJson(c, data)
}

func PipelineBatchResourceUpdate(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有系统管理员才能执行当前操作")
		return
	}
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	var params []PipelineResourceUpdateParam
	err = json.Unmarshal(body, &params)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	if len(params) > 1000 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多1000个发布流程")
		return
	}
	data := make([]string, 0, 1000)
	for _, it := range params {
		if it.RequestMemory > it.LimitMemory {
			data = append(data, fmt.Sprintf("%s/%s/%s: RequestMemory > LimitMemory", it.Cluster, it.Namespace, it.App))
			continue
		}
		if it.RequestCPU > it.LimitCPU {
			data = append(data, fmt.Sprintf("%s/%s/%s: RequestCPU > LimitCPU", it.Cluster, it.Namespace, it.App))
			continue
		}

		pipe, err := pipeline_service.FirstInEnv(it.Cluster, it.Namespace, it.App)
		if err != nil {
			data = append(data, fmt.Sprintf("%s/%s/%s: PipelineNotFound", it.Cluster, it.Namespace, it.App))
			continue
		}
		before := fmt.Sprintf("%d|%d|%d|%d|%d", int32(pipe.Resources.RequestCPU*1000), int32(pipe.Resources.LimitCPU*1000), pipe.Resources.RequestMemory, pipe.Resources.LimitMemory, pipe.Replicas)

		if it.RequestCPU > 0 {
			if pipe.Resources.RequestCPU = float32(it.RequestCPU) / 1000; pipe.Resources.RequestCPU < 0.01 {
				pipe.Resources.RequestCPU = 0.01
			}
		}
		if it.LimitCPU > 0 {
			if pipe.Resources.LimitCPU = float32(it.LimitCPU) / 1000; pipe.Resources.LimitCPU < 0.01 {
				pipe.Resources.LimitCPU = 0.01
			}
		}
		if it.RequestMemory > 0 {
			pipe.Resources.RequestMemory = uint(it.RequestMemory)
		}
		if it.LimitMemory > 0 {
			pipe.Resources.LimitMemory = uint(it.LimitMemory)
		}

		if it.Replicas > 0 {
			pipe.Replicas = uint(it.Replicas)
		}
		after := fmt.Sprintf("%d|%d|%d|%d|%d", int32(pipe.Resources.RequestCPU*1000), int32(pipe.Resources.LimitCPU*1000), pipe.Resources.RequestMemory, pipe.Resources.LimitMemory, pipe.Replicas)
		if before == after {
			data = append(data, fmt.Sprintf("%s/%s/%s: ResourceNotChange, rc|lc|rm|lm|replica=(before: %s, after: %s)", it.Cluster, it.Namespace, it.App, before, after))
			continue
		}
		err = pipeline_service.SaveOrUpdate(&pipe)
		if err != nil {
			data = append(data, fmt.Sprintf("%s/%s/%s: Fail, %s", it.Cluster, it.Namespace, it.App, err.Error()))
		} else {
			data = append(data, fmt.Sprintf("%s/%s/%s: Success, rc|lc|rm|lm|replica=(before: %s, after: %s)", it.Cluster, it.Namespace, it.App, before, after))
		}
	}
	log_service.Create(auth.GetRealName(c), "发布流程-资源批量更新", "", data)
	web.SuccessJson(c, strings.Join(data, "\n"))
}

func createAutoscaleV1(cluster, namespace, app string, maxReplicas uint, username string) error {
	if db, _ := auto_scale_service.FirstInEnv(cluster, namespace, app); db.ID != 0 {
		return errors.New("AutoscaleAlreadyExist")
	}
	return auto_scale_service.SaveOrUpdate(models.AutoScale{
		App:                app,
		Cluster:            cluster,
		Namespace:          namespace,
		CpuTargetPercent:   60,
		Replicas:           maxReplicas,
		ScaleUpThreshold:   2,
		ScaleDownThreshold: 3,
		ScaleDownStartTime: "23:00",
		ScaleDownEndTime:   "06:00",
		Author:             username,
	})
}

func createAutoscaleV2(cluster, namespace, app string, minReplicas, maxReplicas int32, username string) error {
	found, err := pascli.ExistPodAutoScaler(cluster, namespace, app)
	if found {
		return errors.New("AutoscaleAlreadyExist")
	}
	if err != nil {
		return err
	}
	_, err = pascli.ApplyPodAutoScaler(dto.PodAutoScalerDTO{
		Cluster:                             cluster,
		Namespace:                           namespace,
		App:                                 app,
		Paused:                              false,
		MinReplicas:                         minReplicas,
		MaxReplicas:                         maxReplicas,
		ScaleUpInitialDelaySeconds:          600,
		ScaleUpStabilizationWindowSeconds:   30,
		ScaleUpReplicaStep:                  3,
		ScaleUpHourWindow:                   []int32{7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22},
		ScaleDownStabilizationWindowSeconds: 600,
		ScaleDownReplicaStep:                1,
		ScaleDownHourWindow:                 []int32{23, 0, 1, 2, 3, 4, 5, 6},
		TriggerCpuUtilization:               fmt.Sprintf("%d", 60),
		Labels: map[string]string{
			"app.kubernetes.io/managed-by": "fs-k8s-app-manager"},
		Annotations: map[string]string{
			"fxiaoke.com/last-modify-user": username,
			"fxiaoke.com/last-modify-time": time.Now().Format("2006-01-02 15:04:05"),
		},
	})
	return err
}

func AutoscaleBatchCreate(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有系统管理员才能执行当前操作")
		return
	}
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	params := strings.Split(string(body), "\n")

	if len(params) > 1000 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多1000个")
		return
	}
	data := make([]string, 0, 1000)
	for _, it := range params {
		parts := strings.Split(it, "/")
		if len(parts) != 3 {
			data = append(data, fmt.Sprintf("%s = LineFormatError", it))
			continue
		}
		cluster := parts[0]
		namespace := parts[1]
		app := parts[2]

		pipe, err := pipeline_service.FirstInEnv(cluster, namespace, app)
		if err != nil {
			data = append(data, fmt.Sprintf("%s/%s/%s: PipelineNotFound", cluster, namespace, app))
			continue
		}
		if pipe.Replicas == 0 {
			data = append(data, fmt.Sprintf("%s/%s/%s: PipelineReplicasIsZero", cluster, namespace, app))
			continue
		}

		if pascli.IsInstalledPodAutoScaler(cluster) {
			err = createAutoscaleV2(cluster, namespace, app, int32(pipe.Replicas), int32(pipe.Replicas)+5, user.RealName)
			if err != nil {
				data = append(data, fmt.Sprintf("%s: Fail, %s", it, err.Error()))
			} else {
				data = append(data, fmt.Sprintf("%s: Success", it))
			}
			continue
		}
		if err := createAutoscaleV1(cluster, namespace, app, pipe.Replicas+5, user.RealName); err != nil {
			data = append(data, fmt.Sprintf("%s: Fail, %s", it, err.Error()))
		} else {
			data = append(data, fmt.Sprintf("%s: Success", it))
		}
	}
	log_service.Create(auth.GetRealName(c), "自动扩缩容-批量创建", "", data)
	web.SuccessJson(c, strings.Join(data, "\n"))
}

func MigrateAddrQuery(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := string(body)
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}
	if len(lineItems) > 100 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多查询100个应用")
		return
	}
	ret := make([]string, 0, 100)
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 3 {
			ret = append(ret, fmt.Sprintf("%s = LineFormatError", line))
			continue
		}
		cluster := parts[0]
		namespace := parts[1]
		app := parts[2]

		clu := settings.GetSetting().GetCluster(cluster)
		if clu == nil || clu.NodeVIP == "" {
			ret = append(ret, fmt.Sprintf("%s = ClusterNodeVIPNotFound", line))
			continue
		}
		service, err := k8s_service.ServiceDTO(cluster, namespace, app)
		if err != nil || service == nil {
			ret = append(ret, fmt.Sprintf("%s = ServiceNotFound", line))
			continue
		}

		for _, it := range service.Ports {
			ret = append(ret, fmt.Sprintf("%s/%s/%s/%d = %s:%d", cluster, namespace, app, it.Port, clu.NodeVIP, it.NodePort))
		}
	}
	web.SuccessJson(c, ret)
}

func MigrateAddrQuery2(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := string(body)
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}
	if len(lineItems) > 100 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多查询100个应用")
		return
	}
	ret := make([]string, 0, 100)
	ret = append(ret, "namespace/app/port/k8s1Addr/k8s0Addr")
	ret = append(ret, "-------------")
	clu1 := settings.GetSetting().GetCluster("k8s1")
	clu0 := settings.GetSetting().GetCluster("k8s0")
	if clu0 == nil || clu0.NodeVIP == "" || clu1 == nil || clu1.NodeVIP == "" {
		web.FailJson(c, web.CODE_SERVER_ERROR, "集群node vip 未配置")
		return
	}
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 2 {
			ret = append(ret, fmt.Sprintf("%s = LineFormatError", line))
			continue
		}
		namespace := parts[0]
		app := parts[1]
		clu1Service, err := k8s_service.ServiceDTO(clu1.Name, namespace, app)
		if err != nil || clu1Service == nil {
			ret = append(ret, fmt.Sprintf("%s = K8S1ServiceNotFound", line))
			continue
		}
		clu0Service, err := k8s_service.ServiceDTO(clu0.Name, namespace, app)
		if err != nil || clu0Service == nil {
			ret = append(ret, fmt.Sprintf("%s = K8S0ServiceNotFound", line))
			continue
		}

		for _, clu1Port := range clu1Service.Ports {
			item := ""
			for _, clu0Port := range clu0Service.Ports {
				if clu1Port.Port == clu0Port.Port {
					item = fmt.Sprintf("%s/%s/%d/%s:%d/%s:%d",
						namespace, app,
						clu1Port.Port, clu1.NodeVIP, clu1Port.NodePort,
						clu0.NodeVIP, clu0Port.NodePort)
					break
				}
			}
			if item == "" {
				item = fmt.Sprintf("%s cluster1:%d not found in cluter0", line, clu1Port.Port)
			}
			ret = append(ret, item)
		}
	}
	web.SuccessJson(c, ret)
}

func PipelineBatchUpdateStatus(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有系统管理员才能执行当前操作")
		return
	}
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := string(body)
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}

	status := c.Query("status")
	if !constant.PipelineStatusIsValid(status) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "status value is invalid")
		return
	}
	if len(lineItems) > 200 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多200个发布流程")
		return
	}
	ret := make(map[string]interface{})
	msgs := make([]string, 0, 200)
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 3 {
			log.Warnf("line part not equals 3, line: %s", line)
			msgs = append(msgs, fmt.Sprintf("%s: PipelineNotFound", line))
			continue
		}
		cluster := parts[0]
		namespace := parts[1]
		app := parts[2]

		p, err := pipeline_service.FirstInEnv(cluster, namespace, app)
		if err != nil {
			msgs = append(msgs, fmt.Sprintf("%s: PipelineNotFound", line))
			continue
		}
		p.Status = status
		err = pipeline_service.SaveOrUpdate(&p)
		if err != nil {
			msgs = append(msgs, fmt.Sprintf("%s: Fail, %s", line, err.Error()))
		} else {
			msgs = append(msgs, fmt.Sprintf("%s: Success", line))
		}
	}
	ret["message"] = strings.Join(msgs, "\n")
	web.SuccessJson(c, ret)
}

func AppVersionSnapshot(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "无操作权限")
		return
	}
	msgReplacer := strings.NewReplacer("\"", "", "\n", " ")
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	version := c.Query("version")
	remark := c.Query("remark")
	dryRun := c.Query("dryRun") != "false"
	if cluster == "" || namespace == "" || version == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数")
		return
	}
	if strings.Contains(version, "/") {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "版本号不能包含 /")
		return
	}
	entities, _ := commondata_service.Search(models.AppVersionSnapshot, 0, 300)
	for _, entity := range entities {
		if entity.Column01 == version {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "版本号已存在")
			return
		}
	}
	pipes, err := pipeline_service.FindByClusterAndNamespace(cluster, namespace)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	depList, err := k8s.GetDeploymentList(cluster, namespace)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	ret := make([]AppVersionSnapshotResult, 0, 2000)
	for _, p := range pipes {
		if p.Status != constant.PIPELINE_STATUS_ENABLED {
			continue
		}
		if p.Replicas < 1 {
			continue
		}
		item := AppVersionSnapshotResult{
			App:       p.App,
			Cluster:   p.Cluster,
			Namespace: p.Namespace,
			AppOwner:  cmdb_service.GetMainOwnerNames(p.App, ","),
		}
		var dep *appV1.Deployment
		for _, it := range depList.Items {
			if it.Name == p.App {
				dep = &it
				break
			}
		}
		if dep == nil {
			item.Message = msgReplacer.Replace("deployment not found")
			ret = append(ret, item)
			continue
		}
		modules, err := k8s_service.GetDeployModules(dep)
		if err != nil {
			item.Message = msgReplacer.Replace(err.Error())
			ret = append(ret, item)
			continue
		}
		for _, mod := range modules {
			artifactImageSnapshot := ""
			if dryRun {
				item.Message = "harborImageCopy skipped, because dryRun is true"
			} else {
				artifactImageSnapshot = strings.Replace(mod.ArtifactImage, config.Conf.Harbor.Host, config.Conf.Harbor.Host+"/app-snapshot", 1)
				_, project, repo, _, err := harbor_tool.ParseMetaFromArtifactFullName(artifactImageSnapshot)
				err = harbor.Create().CopyArtifact(project, repo, strings.Replace(mod.ArtifactImage, config.Conf.Harbor.Host+"/", "", 1))
				if err != nil {
					artifactImageSnapshot = ""
					item.Message = "harborImageCopy failed: " + msgReplacer.Replace(err.Error())
				}
			}

			ret = append(ret, AppVersionSnapshotResult{
				App:                   item.App,
				Cluster:               item.Cluster,
				Namespace:             item.Namespace,
				Message:               item.Message,
				AppOwner:              item.AppOwner,
				ArtifactImageSnapshot: artifactImageSnapshot,
				DeployModule:          mod,
			})
		}
	}
	log_service.Create(auth.GetRealName(c), "app-version-snapshot", "", map[string]interface{}{
		"params": map[string]interface{}{
			"cluster":   cluster,
			"namespace": namespace,
			"version":   version,
			"remark":    remark,
		},
		"result": ret,
	})
	retStr, err := json.MarshalIndent(ret, "", "  ")
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	err = commondata_service.Create(auth.GetRealName(c), string(models.AppVersionSnapshot), string(retStr), version, cluster, namespace, remark, user.RealName)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, ret)
}

func AllAppOwners(c *gin.Context) {
	data, err := cmdb_service.AllAppOwners()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}
func AppVersionSnapshotSearch(c *gin.Context) {
	items, err := commondata_service.Search(models.AppVersionSnapshot, 0, 300)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, items)
}

func GetAppVersionSnapshotById(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "id参数错误")
		return
	}
	ret, err := commondata_service.FindById(uint(id))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, ret)
}

func AppVersionSnapshotDelete(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "无操作权限")
		return
	}
	id := c.Query("id")
	idVal, err := strconv.Atoi(id)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "id参数错误")
		return
	}
	err = commondata_service.DeleteById(uint(idVal))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func LoadCmsProfileConfigs(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	clu := settings.GetSetting().GetCluster(cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "集群不存在: "+cluster)
		return
	}
	cmsConfigs, err := cms.GetProfileConfigs(namespace)
	if !clu.IsFxiaokeCloud() {
		cloudCMS, err := cms.GetProfileConfigs("cloud")
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		cmsConfigs = append(cmsConfigs, cloudCMS...)
	}
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	op := c.Query("op")
	if op == "nodeport-to-service" {
		addrs, err := getAppAddrs(*clu)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		for idx, it := range cmsConfigs {
			if it.Name == "k8s-service-list.csv" {
				continue
			}
			cmsConfigs[idx].RepairAddrs = make(map[string]string)
			for _, addr := range addrs {
				if strings.Contains(it.Content, addr.NodePortAddr) {
					cmsConfigs[idx].RepairAddrs[addr.NodePortAddr] = addr.ServiceAddrLong
				}
			}
		}
		data := make([]cms.CmsConfig, 0, 100)
		for _, it := range cmsConfigs {
			if len(it.RepairAddrs) > 0 {
				data = append(data, it)
			}
		}
		web.SuccessJson(c, data)
		return
	}

	if op == "serviceShort-to-serviceLong" {
		if true {
			web.FailJson(c, web.CODE_SERVER_ERROR, "暂不支持，待todo解决后开放...")
			return
		}
		addrs, err := getAppAddrs(*clu)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		for idx, it := range cmsConfigs {
			if strings.Contains(config.Conf.CMS.ServiceConfigPath, it.Name) {
				continue
			}
			cmsConfigs[idx].RepairAddrs = make(map[string]string)
			for _, addr := range addrs {
				it.Content = strings.ReplaceAll(it.Content, "\r\n", "\n")
				// 最好是按行匹配，避免误替换
				//todo: 建议增加http://来精准匹配
				if strings.Contains(it.Content, addr.ServiceAddrShort) && !strings.Contains(it.Content, addr.ServiceAddrLong) {
					if it.Profile == "cloud" {
						//为了让专属云支持多运行环境，cloud配置不允许配置服务名地址
						cmsConfigs[idx].RepairAddrs[addr.ServiceAddrShort] = "--must-be-remove-to-detail-cloud--"
					} else if it.Profile == addr.Namespace {
						//避免替换其他环境的服务名地址
						cmsConfigs[idx].RepairAddrs[addr.ServiceAddrShort] = addr.ServiceAddrLong
					}
				}
			}
		}
		data := make([]cms.CmsConfig, 0, 100)
		for _, it := range cmsConfigs {
			if len(it.RepairAddrs) > 0 {
				data = append(data, it)
			}
		}
		web.SuccessJson(c, data)
		return
	}
	web.FailJson(c, web.CODE_CLIENT_ERROR, "op参数错误")
}

type appAddr struct {
	ServiceName      string `json:"serviceName"`
	Namespace        string `json:"namespace"`
	ServiceAddrLong  string `json:"serviceAddrLong"`
	ServiceAddrShort string `json:"ServiceAddrShort"`
	NodePortAddr     string `json:"nodePortAddr"`
}

func getAppAddrs(clu settings.Cluster) ([]appAddr, error) {
	ret := make([]appAddr, 0, 1000)
	for _, ns := range clu.Namespaces {
		services, err := k8s_service.ListService(clu.Name, ns)
		if err != nil {
			return nil, err
		}
		for _, ser := range services {
			for _, p := range ser.Ports {
				item := appAddr{
					ServiceName:      ser.Name,
					Namespace:        ser.Namespace,
					ServiceAddrLong:  fmt.Sprintf("%s.%s:%d", ser.Name, ser.Namespace, p.Port),
					ServiceAddrShort: fmt.Sprintf("%s:%d", ser.Name, p.Port),
					NodePortAddr:     fmt.Sprintf("%s:%d", clu.NodeVIP, p.NodePort),
				}
				ret = append(ret, item)
				//if p.Port == 80 {
				//	ret = append(ret, appAddr{
				//		ServiceName:      item.ServiceName,
				//		Namespace:        item.Namespace,z
				//		ServiceAddrLong:  fmt.Sprintf("http://%s.%s\n", ser.Name, ser.Namespace),
				//		ServiceAddrShort: fmt.Sprintf("http://%s\n", ser.Name),
				//		NodePortAddr:     item.NodePortAddr,
				//	})
				//	ret = append(ret, appAddr{
				//		ServiceName:      item.ServiceName,
				//		Namespace:        item.Namespace,
				//		ServiceAddrLong:  fmt.Sprintf("http://%s.%s\"", ser.Name, ser.Namespace),
				//		ServiceAddrShort: fmt.Sprintf("http://%s\"", ser.Name),
				//		NodePortAddr:     item.NodePortAddr,
				//	})
				//}
			}
		}
	}
	return ret, nil
}

//func parseAddrFromPipeline() ([]string, error) {
//	pipes, err := pipeline_service.FindAll()
//	if err != nil {
//		return nil, err
//	}
//	ret := mapset.NewSet[string]()
//	for _, p := range pipes {
//		for _, port := range p.Ports {
//			item := fmt.Sprintf("%s:%d", p.App, port.Value)
//			ret.Add(item)
//			if port.Value == 80 {
//				ret.Add(fmt.Sprintf("http://%s", p.App))
//			}
//		}
//	}
//	return ret.ToSlice(), nil
//}
