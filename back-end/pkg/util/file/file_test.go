package file

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func TestGetExt(t *testing.T) {
	tests := []struct {
		name     string
		fileName string
		expected string
	}{
		{
			name:     "Simple file with extension",
			fileName: "test.txt",
			expected: ".txt",
		},
		{
			name:     "File with multiple dots",
			fileName: "test.backup.sql",
			expected: ".sql",
		},
		{
			name:     "File without extension",
			fileName: "README",
			expected: "",
		},
		{
			name:     "Hidden file with extension",
			fileName: ".gitignore",
			expected: "",
		},
		{
			name:     "Hidden file with extension after dot",
			fileName: ".env.local",
			expected: ".local",
		},
		{
			name:     "Empty string",
			fileName: "",
			expected: "",
		},
		{
			name:     "Path with extension",
			fileName: "/path/to/file.go",
			expected: ".go",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetExt(tt.fileName)
			if result != tt.expected {
				t.Errorf("GetExt(%q) = %q, expected %q", tt.fileName, result, tt.expected)
			}
		})
	}
}

func TestCheckNotExist(t *testing.T) {
	// Test with non-existent file
	nonExistentFile := "/tmp/non_existent_file_" + time.Now().Format("20060102150405")
	if !CheckNotExist(nonExistentFile) {
		t.Errorf("CheckNotExist should return true for non-existent file")
	}

	// Create a temporary file
	tempFile, err := os.CreateTemp("", "test_file_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	tempFile.Close()

	// Test with existing file
	if CheckNotExist(tempFile.Name()) {
		t.Errorf("CheckNotExist should return false for existing file")
	}
}

func TestCheckPermission(t *testing.T) {
	// Create a temporary file
	tempFile, err := os.CreateTemp("", "test_perm_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	tempFile.Close()

	// Test with accessible file (should return false for permission error)
	if CheckPermission(tempFile.Name()) {
		t.Errorf("CheckPermission should return false for accessible file")
	}

	// Test with non-existent file (should return false as it's not a permission error)
	nonExistentFile := "/tmp/non_existent_file_" + time.Now().Format("20060102150405")
	if CheckPermission(nonExistentFile) {
		t.Errorf("CheckPermission should return false for non-existent file")
	}
}

func TestIsNotExistMkDir(t *testing.T) {
	// Create a unique directory name
	testDir := filepath.Join(os.TempDir(), "test_mkdir_"+time.Now().Format("20060102150405"))
	defer os.RemoveAll(testDir)

	// Test creating non-existent directory
	err := IsNotExistMkDir(testDir)
	if err != nil {
		t.Errorf("IsNotExistMkDir failed: %v", err)
	}

	// Check if directory was created
	if _, err := os.Stat(testDir); os.IsNotExist(err) {
		t.Errorf("Directory was not created")
	}

	// Test with existing directory (should not return error)
	err = IsNotExistMkDir(testDir)
	if err != nil {
		t.Errorf("IsNotExistMkDir should not fail for existing directory: %v", err)
	}
}

func TestMustOpen(t *testing.T) {
	// Create a temporary directory
	tempDir, err := os.MkdirTemp("", "test_mustopen_")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Test opening file in existing directory
	testFile := filepath.Join(tempDir, "test.txt")
	file, err := MustOpen(testFile, tempDir)
	if err != nil {
		t.Errorf("MustOpen failed: %v", err)
	}
	if file != nil {
		file.Close()
		defer os.Remove(testFile)
	}

	// Test opening file in non-existent directory
	nonExistentDir := filepath.Join(tempDir, "subdir")
	testFile2 := filepath.Join(nonExistentDir, "test2.txt")
	file2, err := MustOpen(testFile2, nonExistentDir)
	if err != nil {
		t.Errorf("MustOpen should create directory and file: %v", err)
	}
	if file2 != nil {
		file2.Close()
	}
}

func TestWrite(t *testing.T) {
	// Create a temporary file
	tempFile, err := os.CreateTemp("", "test_write_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	tempFile.Close()

	// Test writing content
	testContent := "Hello, World!\nThis is a test."
	err = Write(tempFile.Name(), testContent)
	if err != nil {
		t.Errorf("Write failed: %v", err)
	}

	// Verify content was written
	content, err := os.ReadFile(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to read file: %v", err)
	}

	if string(content) != testContent {
		t.Errorf("Written content doesn't match. Expected: %q, Got: %q", testContent, string(content))
	}
}

func TestRead(t *testing.T) {
	// Create a temporary file with content
	testContent := "Hello, World!\nThis is a test file."
	tempFile, err := os.CreateTemp("", "test_read_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())

	_, err = tempFile.WriteString(testContent)
	if err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	tempFile.Close()

	// Test reading content
	content, err := Read(tempFile.Name())
	if err != nil {
		t.Errorf("Read failed: %v", err)
	}

	if content != testContent {
		t.Errorf("Read content doesn't match. Expected: %q, Got: %q", testContent, content)
	}

	// Test reading non-existent file
	_, err = Read("/tmp/non_existent_file_" + time.Now().Format("20060102150405"))
	if err == nil {
		t.Errorf("Read should fail for non-existent file")
	}
}

func TestAbsPath(t *testing.T) {
	// Test with relative path
	relativePath := "test/path"
	absPath := AbsPath(relativePath)
	
	if !filepath.IsAbs(absPath) {
		t.Errorf("AbsPath should return absolute path, got: %q", absPath)
	}

	// Test with already absolute path
	if filepath.IsAbs(relativePath) {
		t.Skip("Skipping test as relative path is already absolute")
	}

	// Test with empty string
	emptyPath := AbsPath("")
	if emptyPath == "" {
		t.Errorf("AbsPath should not return empty string")
	}
}

func TestCreateTempFile(t *testing.T) {
	// Skip this test as it depends on global config
	t.Skip("CreateTempFile depends on global config, skipping in unit test")

	testContent := "This is test content for temporary file"

	// Create temporary file
	filePath, err := CreateTempFile(testContent)
	if err != nil {
		t.Errorf("CreateTempFile failed: %v", err)
		return
	}
	defer os.Remove(filePath)

	// Verify file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Temporary file was not created")
		return
	}

	// Verify content
	content, err := Read(filePath)
	if err != nil {
		t.Errorf("Failed to read temporary file: %v", err)
		return
	}

	if content != testContent {
		t.Errorf("Temporary file content doesn't match. Expected: %q, Got: %q", testContent, content)
	}

	// Verify file is in temp directory
	if !strings.Contains(filePath, "tmp") {
		t.Errorf("Temporary file should be in tmp directory, got: %q", filePath)
	}
}

func TestGetSize(t *testing.T) {
	// Create a temporary file with known content
	testContent := "Hello, World!"
	tempFile, err := os.CreateTemp("", "test_size_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())

	_, err = tempFile.WriteString(testContent)
	if err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	tempFile.Close()

	// Test getting file size
	size, err := GetSize(tempFile.Name())
	if err != nil {
		t.Errorf("GetSize failed: %v", err)
	}

	expectedSize := int64(len(testContent))
	if size != expectedSize {
		t.Errorf("GetSize returned wrong size. Expected: %d, Got: %d", expectedSize, size)
	}

	// Test with non-existent file
	_, err = GetSize("/tmp/non_existent_file_" + time.Now().Format("20060102150405"))
	if err == nil {
		t.Errorf("GetSize should fail for non-existent file")
	}
}

// Benchmark tests
func BenchmarkWrite(b *testing.B) {
	tempFile, err := os.CreateTemp("", "bench_write_*.txt")
	if err != nil {
		b.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	tempFile.Close()

	content := "This is benchmark test content"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Write(tempFile.Name(), content)
	}
}

func BenchmarkRead(b *testing.B) {
	// Setup
	tempFile, err := os.CreateTemp("", "bench_read_*.txt")
	if err != nil {
		b.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	
	content := "This is benchmark test content"
	tempFile.WriteString(content)
	tempFile.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Read(tempFile.Name())
	}
}
