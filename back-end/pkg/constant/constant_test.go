package constant

import (
	"testing"
)

func TestDeployStrategyConstants(t *testing.T) {
	// Test that deploy strategy constants are defined and have expected values
	tests := []struct {
		name     string
		constant DeployStrategy
		expected string
	}{
		{
			name:     "Rolling update strategy",
			constant: DEPLOY_STRATEGY_ROLL_UPDATE,
			expected: "ROLL_UPDATE",
		},
		{
			name:     "Recreate strategy",
			constant: DEPLOY_STRATEGY_RECREATE,
			expected: "RECREATE",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if string(tt.constant) != tt.expected {
				t.Errorf("Expected %s to be %q, got %q", tt.name, tt.expected, string(tt.constant))
			}
		})
	}
}

func TestPortTypeConstants(t *testing.T) {
	// Test that port type constants are defined and have expected values
	tests := []struct {
		name     string
		constant PortType
		expected string
	}{
		{
			name:     "User port type",
			constant: PORT_TYPE_USER,
			expected: "USER",
		},
		{
			name:     "System port type",
			constant: PORT_TYPE_SYSTEM,
			expected: "SYSTEM",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if string(tt.constant) != tt.expected {
				t.Errorf("Expected %s to be %q, got %q", tt.name, tt.expected, string(tt.constant))
			}
		})
	}
}

func TestScheduleStrategyConstants(t *testing.T) {
	// Test that schedule strategy constants are defined and have expected values
	tests := []struct {
		name     string
		constant ScheduleStrategy
		expected string
	}{
		{
			name:     "Preferred schedule strategy",
			constant: SCHEDULE_STRATEGY_PREFERRED,
			expected: "PREFERRED",
		},
		{
			name:     "Required schedule strategy",
			constant: SCHEDULE_STRATEGY_REQUIRED,
			expected: "REQUIRED",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if string(tt.constant) != tt.expected {
				t.Errorf("Expected %s to be %q, got %q", tt.name, tt.expected, string(tt.constant))
			}
		})
	}
}

func TestEnvTypeConstants(t *testing.T) {
	// Test that env type constants are defined and have expected values
	tests := []struct {
		name     string
		constant EnvType
		expected string
	}{
		{
			name:     "System env type",
			constant: ENV_TYPE_SYSTEM,
			expected: "SYSTEM",
		},
		{
			name:     "User env type",
			constant: ENV_TYPE_USER,
			expected: "USER",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if string(tt.constant) != tt.expected {
				t.Errorf("Expected %s to be %q, got %q", tt.name, tt.expected, string(tt.constant))
			}
		})
	}
}

func TestConstantTypes(t *testing.T) {
	// Test that constants are of correct types

	// Test DeployStrategy type
	var deployStrategy DeployStrategy = DEPLOY_STRATEGY_ROLL_UPDATE
	if string(deployStrategy) == "" {
		t.Errorf("DeployStrategy constant should not be empty")
	}

	// Test PortType type
	var portType PortType = PORT_TYPE_USER
	if string(portType) == "" {
		t.Errorf("PortType constant should not be empty")
	}

	// Test ScheduleStrategy type
	var scheduleStrategy ScheduleStrategy = SCHEDULE_STRATEGY_PREFERRED
	if string(scheduleStrategy) == "" {
		t.Errorf("ScheduleStrategy constant should not be empty")
	}

	// Test EnvType type
	var envType EnvType = ENV_TYPE_SYSTEM
	if string(envType) == "" {
		t.Errorf("EnvType constant should not be empty")
	}
}

func TestConstantUniqueness(t *testing.T) {
	// Test that constants within the same category are unique

	// Test deploy strategy constants
	deployStrategies := []DeployStrategy{
		DEPLOY_STRATEGY_ROLL_UPDATE,
		DEPLOY_STRATEGY_RECREATE,
	}

	seen := make(map[string]bool)
	for _, strategy := range deployStrategies {
		strategyStr := string(strategy)
		if seen[strategyStr] {
			t.Errorf("Duplicate deploy strategy constant: %q", strategyStr)
		}
		seen[strategyStr] = true
	}

	// Test port type constants
	portTypes := []PortType{
		PORT_TYPE_USER,
		PORT_TYPE_SYSTEM,
	}

	seen = make(map[string]bool)
	for _, portType := range portTypes {
		portTypeStr := string(portType)
		if seen[portTypeStr] {
			t.Errorf("Duplicate port type constant: %q", portTypeStr)
		}
		seen[portTypeStr] = true
	}
}

func TestConstantNonEmpty(t *testing.T) {
	// Test that all constants are non-empty

	// Test DeployStrategy constants
	if string(DEPLOY_STRATEGY_ROLL_UPDATE) == "" {
		t.Errorf("DEPLOY_STRATEGY_ROLL_UPDATE should not be empty")
	}
	if string(DEPLOY_STRATEGY_RECREATE) == "" {
		t.Errorf("DEPLOY_STRATEGY_RECREATE should not be empty")
	}

	// Test PortType constants
	if string(PORT_TYPE_USER) == "" {
		t.Errorf("PORT_TYPE_USER should not be empty")
	}
	if string(PORT_TYPE_SYSTEM) == "" {
		t.Errorf("PORT_TYPE_SYSTEM should not be empty")
	}

	// Test ScheduleStrategy constants
	if string(SCHEDULE_STRATEGY_PREFERRED) == "" {
		t.Errorf("SCHEDULE_STRATEGY_PREFERRED should not be empty")
	}
	if string(SCHEDULE_STRATEGY_REQUIRED) == "" {
		t.Errorf("SCHEDULE_STRATEGY_REQUIRED should not be empty")
	}

	// Test EnvType constants
	if string(ENV_TYPE_SYSTEM) == "" {
		t.Errorf("ENV_TYPE_SYSTEM should not be empty")
	}
	if string(ENV_TYPE_USER) == "" {
		t.Errorf("ENV_TYPE_USER should not be empty")
	}
}

// Benchmark tests
func BenchmarkConstantAccess(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = DEPLOY_STRATEGY_ROLL_UPDATE
		_ = PORT_TYPE_USER
		_ = SCHEDULE_STRATEGY_PREFERRED
		_ = ENV_TYPE_SYSTEM
	}
}


