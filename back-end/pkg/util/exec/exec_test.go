package exec

import (
	"fmt"
	"runtime"
	"strings"
	"testing"
	"time"
)

func TestCommandExec(t *testing.T) {
	tests := []struct {
		name        string
		command     string
		expectError bool
		contains    string // Expected substring in output
	}{
		{
			name:        "Simple echo command",
			command:     "echo hello",
			expectError: false,
			contains:    "hello",
		},
		{
			name:        "List current directory",
			command:     getListCommand(),
			expectError: false,
			contains:    "", // Don't check specific content
		},
		{
			name:        "Invalid command",
			command:     "nonexistentcommand12345",
			expectError: true,
			contains:    "",
		},
		{
			name:        "Command with pipe",
			command:     getPipeCommand(),
			expectError: false,
			contains:    "hello",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output, err := CommandExec(tt.command)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for command %q, but got none", tt.command)
				}
				return
			}
			
			if err != nil {
				t.<PERSON><PERSON><PERSON>("Unexpected error for command %q: %v", tt.command, err)
				return
			}
			
			if tt.contains != "" && !strings.Contains(output, tt.contains) {
				t.<PERSON><PERSON><PERSON>("Expected output to contain %q, got: %q", tt.contains, output)
			}
		})
	}
}

func TestCommandExecWithTimeout(t *testing.T) {
	tests := []struct {
		name        string
		command     string
		timeout     time.Duration
		expectError bool
		errorType   string // "timeout" or "command"
	}{
		{
			name:        "Quick command with long timeout",
			command:     "echo quick",
			timeout:     5 * time.Second,
			expectError: false,
		},
		{
			name:        "Sleep command with short timeout",
			command:     getSleepCommand(2),
			timeout:     500 * time.Millisecond,
			expectError: true,
			errorType:   "timeout",
		},
		{
			name:        "Invalid command with timeout",
			command:     "invalidcommand123",
			timeout:     1 * time.Second,
			expectError: true,
			errorType:   "command",
		},
		{
			name:        "Zero timeout",
			command:     "echo test",
			timeout:     0,
			expectError: true,
			errorType:   "timeout",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output, err := CommandExecWithTimeout(tt.command, tt.timeout)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for command %q with timeout %v, but got none", tt.command, tt.timeout)
				} else if tt.errorType == "timeout" && !strings.Contains(err.Error(), "timed out") {
					t.Errorf("Expected timeout error, got: %v", err)
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error for command %q: %v", tt.command, err)
				return
			}
			
			if output == "" && tt.command == "echo quick" {
				t.Errorf("Expected non-empty output for echo command")
			}
		})
	}
}

func TestCommandExecInWorkDir(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := "/tmp"
	if runtime.GOOS == "windows" {
		tempDir = "C:\\temp"
	}

	tests := []struct {
		name        string
		workDir     string
		command     string
		expectError bool
	}{
		{
			name:        "Execute in temp directory",
			workDir:     tempDir,
			command:     getPwdCommand(),
			expectError: false,
		},
		{
			name:        "Execute in non-existent directory",
			workDir:     "/non/existent/directory",
			command:     "echo test",
			expectError: true,
		},
		{
			name:        "Execute with empty work directory",
			workDir:     "",
			command:     "echo test",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output, err := CommandExecInWorkDir(tt.workDir, tt.command)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for command %q in workdir %q, but got none", tt.command, tt.workDir)
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error for command %q in workdir %q: %v", tt.command, tt.workDir, err)
				return
			}
			
			// For pwd command in temp directory, output should contain the temp path
			if tt.command == getPwdCommand() && tt.workDir == tempDir {
				if !strings.Contains(output, "tmp") && !strings.Contains(output, "temp") {
					t.Logf("Output might not contain expected directory: %q", output)
				}
			}
		})
	}
}

func TestCommandExecWithTimeoutAndWorkdir(t *testing.T) {
	tempDir := "/tmp"
	if runtime.GOOS == "windows" {
		tempDir = "C:\\temp"
	}

	tests := []struct {
		name        string
		workDir     string
		command     string
		timeout     time.Duration
		expectError bool
	}{
		{
			name:        "Quick command in temp dir",
			workDir:     tempDir,
			command:     "echo test",
			timeout:     2 * time.Second,
			expectError: false,
		},
		{
			name:        "Timeout in specific directory",
			workDir:     tempDir,
			command:     getSleepCommand(2),
			timeout:     500 * time.Millisecond,
			expectError: true,
		},
		{
			name:        "Invalid directory with timeout",
			workDir:     "/invalid/directory",
			command:     "echo test",
			timeout:     1 * time.Second,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output, err := CommandExecWithTimeoutAndWorkdir(tt.workDir, tt.command, tt.timeout)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for command %q in workdir %q with timeout %v, but got none", 
						tt.command, tt.workDir, tt.timeout)
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error for command %q in workdir %q: %v", tt.command, tt.workDir, err)
				return
			}
			
			if tt.command == "echo test" && !strings.Contains(output, "test") {
				t.Errorf("Expected output to contain 'test', got: %q", output)
			}
		})
	}
}

// Helper functions to get platform-specific commands
func getListCommand() string {
	if runtime.GOOS == "windows" {
		return "dir"
	}
	return "ls"
}

func getPipeCommand() string {
	if runtime.GOOS == "windows" {
		return "echo hello | findstr hello"
	}
	return "echo hello | grep hello"
}

func getSleepCommand(seconds int) string {
	if runtime.GOOS == "windows" {
		return fmt.Sprintf("timeout /t %d", seconds)
	}
	return fmt.Sprintf("sleep %d", seconds)
}

func getPwdCommand() string {
	if runtime.GOOS == "windows" {
		return "cd"
	}
	return "pwd"
}

func TestCommandExecEdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		command     string
		expectError bool
	}{
		{
			name:        "Empty command",
			command:     "",
			expectError: true,
		},
		{
			name:        "Command with special characters",
			command:     "echo 'hello world'",
			expectError: false,
		},
		{
			name:        "Command with environment variable",
			command:     getEnvCommand(),
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output, err := CommandExec(tt.command)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for command %q, but got none", tt.command)
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error for command %q: %v", tt.command, err)
				return
			}
			
			// Basic validation that we got some output
			if tt.command != "" && output == "" {
				t.Logf("Warning: Empty output for command %q", tt.command)
			}
		})
	}
}

func getEnvCommand() string {
	if runtime.GOOS == "windows" {
		return "echo %PATH%"
	}
	return "echo $PATH"
}

// Test concurrent execution
func TestConcurrentCommandExec(t *testing.T) {
	const numGoroutines = 5
	const numCommands = 3
	
	done := make(chan bool, numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			for j := 0; j < numCommands; j++ {
				command := fmt.Sprintf("echo concurrent_test_%d_%d", id, j)
				output, err := CommandExec(command)

				if err != nil {
					t.Errorf("Goroutine %d, command %d failed: %v", id, j, err)
					return
				}

				expected := fmt.Sprintf("concurrent_test_%d_%d", id, j)
				if !strings.Contains(output, expected) {
					t.Errorf("Goroutine %d, command %d: expected output to contain %q, got %q",
						id, j, expected, output)
				}
			}
		}(i)
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		select {
		case <-done:
			// Goroutine completed
		case <-time.After(10 * time.Second):
			t.Fatalf("Timeout waiting for goroutine %d to complete", i)
		}
	}
}

// Benchmark tests
func BenchmarkCommandExec(b *testing.B) {
	command := "echo benchmark_test"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := CommandExec(command)
		if err != nil {
			b.Fatalf("CommandExec failed: %v", err)
		}
	}
}

func BenchmarkCommandExecWithTimeout(b *testing.B) {
	command := "echo benchmark_test"
	timeout := 5 * time.Second
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := CommandExecWithTimeout(command, timeout)
		if err != nil {
			b.Fatalf("CommandExecWithTimeout failed: %v", err)
		}
	}
}
