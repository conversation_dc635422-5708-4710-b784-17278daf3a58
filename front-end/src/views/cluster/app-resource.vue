<template>
  <div v-loading="loading">
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData"
             @submit.native.prevent>
      <el-form-item label="集群">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群" style="width: 100%;"
                   @change="clusterChange" filterable>
          <el-option
            v-for="item in clusterOptions"
            :key="item.name"
            :label="item.name + ' (' + item.description + ')'"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="环境">
        <el-select v-model="searchForm.namespace" placeholder="选择运行环境" style="width: 100%" filterable>
          <el-option
            v-for="item in namespaceOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
          <el-option key="*" label="所有" value="*"></el-option>
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="关键字">-->
      <!--        <el-input v-model.trim="searchForm.keyword" style="width: 260px"></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
        <export-button :table-ref="this.$refs.table001"></export-button>

        <div style="display: inline-block;margin-left: 60px;font-size: 12px;">
          显示：
          <el-checkbox v-model="showFields.appRemark" style="margin-right: 10px;">服务描述</el-checkbox>
          <el-checkbox v-model="showFields.appOwner" style="margin-right: 10px;">服务负责人</el-checkbox>
          <el-checkbox v-model="showFields.appLevel" style="margin-right: 10px;">服务等级</el-checkbox>
          <el-checkbox v-model="showFields.onlyShowDiffReplicas" style="margin-right: 10px;" @change="onShowDiffReplicasChange">只显示副本差异数据</el-checkbox>
        </div>
      </el-form-item>
    </el-form>

    <el-table
      ref="table001"
      :data="tableDataFilters"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      show-summary
      :default-sort="{prop: 'requestMemoryTotal', order: 'descending'}"
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用"
                       sortable
                       prop="name">
      </el-table-column>
      <el-table-column label="等级"
                       v-if="showFields.appLevel"
                       prop="appLevel"
                       width="80px">
      </el-table-column>
      <el-table-column label="应用描述"
                       v-if="showFields.appRemark"
                       prop="appRemark">
      </el-table-column>
      <el-table-column label="应用负责人"
                       v-if="showFields.appOwner"
                       prop="appOwner">
      </el-table-column>
      <el-table-column label="环境" prop="namespace" sortable>
      </el-table-column>
      <el-table-column label="变动时间"
                       sortable
                       prop="updateTime">
        <template slot-scope="scope">
          <div>
            <span>C: {{ scope.row.createTime }}</span><br/>
            <span>U: {{ scope.row.updateTime }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="运行副本"
                       sortable
                       align="center"
                       width="110px"
                       prop="replicas">
        <template slot-scope="scope">
          <b>{{ scope.row.replicas }}</b><br/>
          <el-button
            style="padding: 0"
            type="text"
            size="mini"
            @click="replicasEditDialog(scope.row.cluster,scope.row.namespace,scope.row.name,scope.row.replicas)"
            slot="reference">修改
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="发布流程副本"
                       align="center"
                       width="110px"
                       prop="pipelineReplicas">
        <template slot-scope="scope">
          <b v-if="scope.row.pipelineReplicas >= 0">{{ scope.row.pipelineReplicas }}</b>
          <span v-else>--</span>
          <br/>
          <el-button
            style="padding: 0"
            size="mini"
            type="text"
            @click="pipelinePage(scope.row.name)"
            slot="reference">流程页
          </el-button>
        </template>
      </el-table-column>

      <el-table-column label="CPU （单位：core）" align="center">
        <el-table-column label="分配总计" width="160" align="center" prop="requestCpuTotal" sortable>
          <template slot-scope="scope">
            <b style="color: #01AAED">{{ scope.row.requestCpuTotal }}</b>
          </template>
        </el-table-column>
        <el-table-column label="分配" width="100" align="center" prop="requestCpu" sortable>
        </el-table-column>
        <el-table-column label="最高" width="100" align="center" prop="limitCpu" sortable>
        </el-table-column>
      </el-table-column>

      <el-table-column label="内存（单位：MiB）" align="center">
        <el-table-column label="分配总计" width="160" align="center" prop="requestMemoryTotal" sortable>
          <template slot-scope="scope">
            <b style="color: #01AAED">{{ scope.row.requestMemoryTotal }}</b>
          </template>
        </el-table-column>
        <el-table-column label="分配" width="100" align="center" prop="requestMemory" sortable>
        </el-table-column>
        <el-table-column label="最高" width="100" align="center" prop="limitMemory" sortable>
        </el-table-column>
      </el-table-column>
      <el-table-column width="120px">
        <template slot-scope="scope">
          <router-link :to="{name:'pod-index',query:{'cluster':scope.row.cluster,'namespace':scope.row.namespace,'app':scope.row.name}}" target="_blank">
            <i class="el-icon-menu" style="color:#409EFF;font-weight: 500;">实例管理</i>
          </router-link>
        </template>
      </el-table-column>
    </el-table>
    <div>
      <el-backtop></el-backtop>
    </div>

    <el-dialog title="修改实例数" :visible.sync="replicasEditVisible" width="500px">
      <el-form label-position="right" label-width="80px">
        <el-form-item label="集群">
          <el-input v-model="replicasEdit.cluster" disabled></el-input>
        </el-form-item>
        <el-form-item label="环境">
          <el-input v-model="replicasEdit.namespace" disabled></el-input>
        </el-form-item>
        <el-form-item label="应用">
          <el-input v-model="replicasEdit.app" disabled></el-input>
        </el-form-item>
        <el-form-item label="实例数">
          <el-input-number v-model="replicasEdit.replicas" :min="0" :max="50"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="replicasEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="replicasEditSubmit" :loading="replicasEditLoading">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {scaleReplicas, searchDeployment} from "@/api/k8s/app";
import ExportButton from "@/views/components/export-button.vue";
import {allApps} from "@/api/app";

export default {
  name: "appResource",
  mounted() {
    this.loadTableData()
  },
  components: {
    ExportButton
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    }
  },
  data() {
    return {
      loading: false,
      searchEnv: "",
      showFields: {
        appRemark: false,
        appOwner: false,
        appLevel: false,
        onlyShowDiffReplicas: false,
      },
      searchForm: {
        cluster: "",
        namespace: "",
        keyword: ""
      },
      tableData: [],
      tableDataFilters: [],
      replicasEditLoading: false,
      replicasEditVisible: false,
      replicasEdit: {
        cluster: "",
        namespace: "",
        app: "",
        replicas: 1,
      }
    }
  },
  methods: {
    loadTableData() {
      if (!this.searchForm.cluster || !this.searchForm.namespace) {
        return
      }
      this.loading = true;
      searchDeployment(this.searchForm.cluster, this.searchForm.namespace).then(response => {
        let newData = response.data;
        for (let it of newData) {
          it.cluster = this.searchForm.cluster
          it.limitCpu = (it.limitCpu / 1000).toFixed(1)
          it.requestCpu = (it.requestCpu / 1000).toFixed(1)
          it.limitMemory = Math.floor(it.limitMemory / 1024 / 1024)
          it.requestMemory = Math.floor(it.requestMemory / 1024 / 1024)
          //total
          it.requestCpuTotal = (it.replicas * it.requestCpu).toFixed(1)
          it.requestMemoryTotal = Math.floor(it.replicas * it.requestMemory)
          it.appOwner = "-"
          it.appRemark = "-"
          it.appLevel = "-"
        }
        this.tableData = newData
        this.tableDataFilters = this.tableData
        this.appendAppInfo()
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false;
      })
    },
    clusterChange() {
      this.searchForm.namespace = ""
    },
    onShowDiffReplicasChange(val) {
      if(val === true){
        this.tableDataFilters = this.tableData.filter(item => item.replicas !== item.pipelineReplicas)
      } else {
        this.tableDataFilters = this.tableData.filter(item => item.replicas === item.pipelineReplicas)
      }
    },
    replicasEditDialog(cluster, namespace, app, replicas) {
      this.replicasEdit = {
        cluster: cluster,
        namespace: namespace,
        app: app,
        replicas: replicas,
      }
      this.replicasEditVisible = true;
    },
    appendAppInfo() {
      allApps().then(response => {
        let appMap = {};
        for (let item of response.data) {
          appMap[item['name']] = item;
        }
        for (let it of this.tableData) {
          let appItem = appMap[it.name];
          if(appItem){
            it.appRemark = appItem.remark;
            it.appOwner = appItem.owner;
            it.appLevel = appItem.level;
            it.createTime = appItem.createTime;
          }
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
      })
    },
    replicasEditSubmit() {
      this.replicasEditLoading = true
      scaleReplicas(this.replicasEdit).then(response => {
        this.$message.success("操作成功");
        for (let it of this.tableData) {
          if (it.cluster === this.replicasEdit.cluster && it.namespace === this.replicasEdit.namespace
            && it.name === this.replicasEdit.app) {
            it.replicas = this.replicasEdit.replicas
          }
        }
        this.replicasEditVisible = false;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.replicasEditLoading = false;
      })
    },
    grafanaPage(row) {
      let url = `/api/page/redirect?type=grafana&app=${row.name}&namespace=${row.namespace}&pod=`
      window.open(url)
    },
    pipelinePage(app) {
      let routerUrl = this.$router.resolve({name: 'cicd-app-deploy', query: {"app": app}})
      window.open(routerUrl.href, "_blank")
    }
  }
}
</script>
