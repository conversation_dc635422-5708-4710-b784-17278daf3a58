(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a3e24b02"],{"1e42":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)},n=[],o=(a("a481"),a("25ca")),l=a("21a6"),i=a.n(l),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var e=this.tableRef.$el,t=o["a"].table_to_book(e,{raw:!0}),a=o["b"](t,{bookType:"xlsx",bookSST:!0,type:"array"});try{var r="export-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";i.a.saveAs(new Blob([a],{type:"application/octet-stream"}),r)}catch(n){this.$message.error("导出失败, err: "+n.message),console.error(n)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,u=a("2877"),p=Object(u["a"])(c,r,n,!1,null,null,null);t["a"]=p.exports},b144:function(e,t,a){"use strict";function r(e){return JSON.parse(JSON.stringify(e))}function n(e){if(!e||!(e instanceof Date))return"";var t=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds();return t}function o(e){return"isCoreApp"===e?"核心服务":"onlyDeployTag"===e?"只允许部署Tag":"addSysctlKeepalive"===e?"调整内核参数":"skyWalkingAgent"===e?"性能跟踪":"appLogToKafka"===e?"接入ClickHouse日志":"buildUseRuntimeJDK"===e?"镜像JDK版本编译代码":"jvmGcLog"===e?"GC日志":e}a.d(t,"a",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"b",(function(){return o}))},b562:function(e,t,a){"use strict";a.d(t,"p",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"l",(function(){return i})),a.d(t,"j",(function(){return s})),a.d(t,"d",(function(){return c})),a.d(t,"i",(function(){return u})),a.d(t,"h",(function(){return p})),a.d(t,"m",(function(){return d})),a.d(t,"o",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"e",(function(){return b})),a.d(t,"c",(function(){return h})),a.d(t,"k",(function(){return g})),a.d(t,"q",(function(){return v})),a.d(t,"n",(function(){return _})),a.d(t,"g",(function(){return y}));var r=a("b775");function n(e){return Object(r["a"])({url:"/v1/app/search",method:"get",params:e})}function o(){return Object(r["a"])({url:"/v1/app/apps-with-env",method:"get"})}function l(){return Object(r["a"])({url:"/v1/app/all",method:"get"})}function i(){return Object(r["a"])({url:"/v1/app/names",method:"get"})}function s(e){return Object(r["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function c(e){return Object(r["a"])({url:"/v1/app",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/v1/app",method:"put",data:e})}function p(e){return Object(r["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function d(e,t,a){return Object(r["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:a}})}function m(e){return Object(r["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function f(e){return Object(r["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function h(e){return Object(r["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function g(e,t){return Object(r["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function v(e,t){return Object(r["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function _(e,t){return Object(r["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function y(e){return Object(r["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:e}})}},bb0b:function(e,t,a){"use strict";a.d(t,"k",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return l})),a.d(t,"l",(function(){return i})),a.d(t,"b",(function(){return s})),a.d(t,"f",(function(){return c})),a.d(t,"n",(function(){return u})),a.d(t,"o",(function(){return p})),a.d(t,"m",(function(){return d})),a.d(t,"c",(function(){return m})),a.d(t,"d",(function(){return f})),a.d(t,"g",(function(){return b})),a.d(t,"i",(function(){return h})),a.d(t,"h",(function(){return g})),a.d(t,"j",(function(){return v}));var r=a("b775");function n(e){return Object(r["a"])({url:"/v1/k8s/scale/auto",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/v1/k8s/scale/auto",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/v1/k8s/scale/auto",method:"delete",params:{id:e}})}function i(e){return Object(r["a"])({url:"/v1/k8s/scale/cron",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/v1/k8s/scale/cron",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/v1/k8s/scale/cron",method:"delete",params:{id:e}})}function u(e){return Object(r["a"])({url:"/v1/k8s/scale/log",method:"get",params:e})}function p(e){return Object(r["a"])({url:"/v1/k8s/scale/monitor/log",method:"get",params:e})}function d(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler",method:"get",params:e})}function m(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler/create-for-core-app",method:"post",params:e})}function b(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler",method:"delete",data:e})}function h(e){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler/migrate?cluster="+e,method:"post"})}function g(){return Object(r["a"])({url:"/v1/k8s/scale/podautoscaler/all-cluster-autoscaler-v2",method:"get"})}function v(e,t,a){return Object(r["a"])({url:"/v1/k8s/scale/all-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}},f429:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"集群"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:e.clusterChange},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",filterable:""},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},[a("el-option",{key:"",attrs:{label:"所有",value:""}}),e._v(" "),e._l(e.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用名"}},[a("el-select",{staticStyle:{width:"280px"},attrs:{filterable:"",placeholder:"请选择应用",clearable:""},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app",t)},expression:"searchForm.app"}},e._l(e.apps,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")])],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:function(t){return e.showEditDialog(null)}}},[e._v("新建")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1),e._v(" "),e._m(0),e._v(" "),e.clusterAutoScaleDisabled?a("el-alert",{staticStyle:{padding:"10px","margin-bottom":"20px"},attrs:{title:"当前集群已关闭自动扩缩容功能，请使用 【自动扩缩容V2】",type:"error",closable:!1,"show-icon":""}}):e._e(),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"所在集群",prop:"cluster"}}),e._v(" "),a("el-table-column",{attrs:{label:"CPU使用率阈值",align:"center",prop:"cpuTargetPercent"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s(t.row.cpuTargetPercent)+"%\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"检测数阈值",align:"center",prop:"scaleUpThreshold",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{label:"副本数",prop:"replicas",align:"center"}},[a("el-table-column",{attrs:{label:"发布流程副本→扩容副本",width:"180",align:"center",prop:"pipeReplicas"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.pipeReplicas?a("span",{staticStyle:{color:"orangered"}},[e._v(e._s(t.row.pipeReplicas))]):a("span",[e._v(e._s(t.row.pipeReplicas))]),e._v(" "),a("span",{staticStyle:{color:"#bbb"}},[e._v("→")]),e._v(" "),0==t.row.replicas?a("span",{staticStyle:{color:"orangered"}},[e._v(e._s(t.row.replicas))]):a("span",[e._v(e._s(t.row.replicas))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"当前运行",width:"100",align:"center",prop:"currReplicas"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.currReplicas?a("span",{staticStyle:{color:"orangered"}},[e._v(e._s(t.row.currReplicas))]):a("span",[e._v(e._s(t.row.currReplicas))])]}}])})],1),e._v(" "),a("el-table-column",{attrs:{label:"扩缩容时间段",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"12px","line-height":"16px"}},[e._v("\n          扩：全天"),a("br"),e._v("\n          缩："+e._s(t.row.scaleDownStartTime)+" - "+e._s(t.row.scaleDownEndTime)+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"系统分析",prop:"analyze"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{color:"orangered","font-size":"10px"}},[e._v("\n          "+e._s(t.row.analyze)+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建人",align:"center",prop:"author"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"220px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"line-height":"16px"}},[a("el-button",{staticClass:"el-icon-menu",staticStyle:{"font-size":"16px",color:"#67c23a",padding:"0"},attrs:{type:"text"},on:{click:function(a){return e.monitorLog(t.row)}}},[e._v("日志\n          ")]),e._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{slot:"reference",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.showEditDialog(t.row)}},slot:"reference"},[e._v("编辑\n          ")]),e._v(" "),a("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(a){return e.deleteCron(t.row)}}},[a("el-button",{staticStyle:{padding:"0"},attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[e._v("删除\n            ")])],1),e._v(" "),a("br"),e._v(" "),a("el-button",{staticClass:"el-icon-menu",staticStyle:{"padding-bottom":"0"},attrs:{type:"text"},on:{click:function(a){return e.pipePage(t.row)}}},[e._v("发布流程\n          ")]),e._v(" "),a("el-button",{staticClass:"el-icon-menu",staticStyle:{"padding-bottom":"0"},attrs:{type:"text"},on:{click:function(a){return e.podPage(t.row)}}},[e._v("实例管理\n          ")])],1)]}}])})],1),e._v(" "),a("el-dialog",{attrs:{title:"创建/编辑扩缩容配置",visible:e.dialogVisible,width:"700px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:e.editForm,"label-width":"120px",rules:e.editFormRules}},[a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",disabled:e.editFormAppEditDisable},model:{value:e.editForm.app,callback:function(t){e.$set(e.editForm,"app",t)},expression:"editForm.app"}},e._l(e.appOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境"}},[a("el-row",[a("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[a("el-form-item",{attrs:{prop:"cluster"}},[a("el-input",{attrs:{disabled:""},model:{value:e.editForm.cluster,callback:function(t){e.$set(e.editForm,"cluster",t)},expression:"editForm.cluster"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{prop:"namespace"}},[a("el-input",{attrs:{disabled:""},model:{value:e.editForm.namespace,callback:function(t){e.$set(e.editForm,"namespace",t)},expression:"editForm.namespace"}})],1)],1)],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"cpu使用率阈值",prop:"cpuTargetPercent"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.editForm.cpuTargetPercent,callback:function(t){e.$set(e.editForm,"cpuTargetPercent",t)},expression:"editForm.cpuTargetPercent"}},e._l(e.cpuPercentOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"检测数阈值",prop:"scaleUpThreshold"}},[a("el-input-number",{attrs:{min:1,max:30},model:{value:e.editForm.scaleUpThreshold,callback:function(t){e.$set(e.editForm,"scaleUpThreshold",t)},expression:"editForm.scaleUpThreshold"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"副本数扩容到",prop:"replicas"}},[a("el-input-number",{model:{value:e.editForm.replicas,callback:function(t){e.$set(e.editForm,"replicas",e._n(t))},expression:"editForm.replicas"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.submitLoading,expression:"submitLoading"}],attrs:{type:"primary"},on:{click:function(t){return e.createCron()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"监控日志",visible:e.monitorLogVisible,width:"900px"},on:{"update:visible":function(t){e.monitorLogVisible=t}}},[a("div",{staticStyle:{height:"500px","overflow-y":"auto"}},[a("el-table",{attrs:{data:e.monitorLogTableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"时间",width:"140",prop:"createdTime"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.cluster+"/"+t.row.namespace+"/"+t.row.app)+"\n          ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"CPU使用情况",prop:"remark"}})],1)],1)])],1)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("table",{staticStyle:{color:"rgb(119, 119, 119)","font-size":"12px",padding:"10px"}},[a("tr",[a("th",{staticStyle:{width:"70px","text-align":"left","vertical-align":"top"}},[e._v("扩容机制:")]),e._v(" "),a("td",{staticStyle:{width:"1000px"}},[e._v("缩容时间范围内，每分钟检测一次，如果应用 "),a("b",{staticStyle:{color:"#E6A23C"}},[e._v("所有实例的CPU平均使用率")]),e._v(" 大于【CPU使用率阈值】"),a("b",[e._v("并且")]),e._v(" 连续检测【检测数阈值】次都满足前面的条件，则一次性扩容到【扩容副本数】\n      ")])]),e._v(" "),a("tr",[a("th",{staticStyle:{width:"70px","text-align":"left","vertical-align":"top"}},[e._v("缩容机制:")]),e._v(" "),a("td",[a("div",[e._v("\n          缩容时间范围内，如果应用所有实例的CPU平均使用率小于【CPU使用率阈值】，\n          则缩容一个实例。一分钟后继续检测，满足条件则再缩容一个实例，直到当前【运行副本数】等于【发布流程副本数】\n        ")])])]),e._v(" "),a("tr",[a("th",{staticStyle:{width:"70px","text-align":"left","vertical-align":"top"}},[e._v("计算公式：")]),e._v(" "),a("td",{staticStyle:{width:"1000px"}},[e._v("【单个实例的CPU使用率】 = 实例cpu使用量 / 实例的CPU资源请求值（发布流程配置里的最大CPU）")])]),e._v(" "),a("tr",[a("th",[e._v("设计图")]),e._v(" "),a("td",[a("a",{staticStyle:{"font-size":"12px",color:"#01AAED"},attrs:{target:"_blank",href:"/images/auto-scale.jpg"}},[e._v("查看扩缩容机制图")])])])])}],o=a("2d63"),l=(a("7f7f"),a("b562")),i=a("b144"),s=a("bb0b"),c=a("1e42"),u={components:{ExportButton:c["a"]},mounted:function(){var e=this;this.$route.query.cluster?this.searchForm.cluster=this.$route.query.cluster:this.searchForm.cluster=this.clusterOptions[0].name,this.$route.query.namespace&&(this.searchForm.namespace=this.$route.query.namespace),this.$route.query.app&&(this.searchForm.app=this.$route.query.app),this.searchForm.app&&this.loadTableData(),Object(l["l"])().then((function(t){e.apps=t.data})).catch((function(t){e.$message.error("加载应用数据出错！ "+t.message)}))},computed:{clusterOptions:function(){return this.$settings.clusters},clusterAutoScaleDisabled:function(){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(a.name===this.searchForm.cluster&&!1===a.autoScale)return!0}}catch(r){t.e(r)}finally{t.f()}return!1},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(r){t.e(r)}finally{t.f()}}return[]},cpuPercentOptions:function(){for(var e=[],t=50;t<=130;t+=5)e.push({name:t+"%",value:t});return e}},data:function(){return{searchEnv:"",apps:[],searchForm:{cluster:"",namespace:"",app:""},editForm:{},editFormAppEditDisable:!1,editFormRules:{app:[{required:!0,message:"值不能为空",trigger:"blur"}],cluster:[{required:!0,message:"值不能为空",trigger:"blur"}],namespace:[{required:!0,message:"值不能为空",trigger:"blur"}],replicas:[{required:!0,message:"值不能为空",trigger:"blur"}],cpuTargetPercent:[{required:!0,message:"值不能为空",trigger:"blur"}],scaleUpThreshold:[{required:!0,message:"值不能为空",trigger:"blur"}]},tableData:[],tableLoading:!1,dialogVisible:!1,submitLoading:!1,appOptions:[],monitorLogVisible:!1,monitorLogTableData:[]}},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,Object(s["k"])(this.searchForm).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},editFormReset:function(){this.editForm={app:"",cluster:this.searchForm.cluster,namespace:this.searchForm.namespace,cpuTargetPercent:70,scaleUpThreshold:2,replicas:2}},showEditDialog:function(e){var t=this;if(this.appOptions.length<1&&Object(l["l"])().then((function(e){t.appOptions=e.data})).catch((function(e){t.$message.error(e.message)})),e)this.editForm=Object(i["a"])(e),this.editFormAppEditDisable=!0;else{if(!this.searchForm.cluster||!this.searchForm.namespace)return void this.$message.warning("请选选择集群和环境");this.editFormReset(),this.editFormAppEditDisable=!1}this.dialogVisible=!0},deleteCron:function(e){var t=this;Object(s["e"])(e.id).then((function(e){t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))},clusterChange:function(){this.searchForm.namespace=""},createCron:function(){var e=this;this.$refs["dialogEditForm"].validate((function(t){if(!t)return!1;e.submitLoading=!0,Object(s["a"])(e.editForm).then((function(t){e.dialogVisible=!1,e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.submitLoading=!1}))}))},pipePage:function(e){var t=this.$router.resolve({name:"cicd-app-deploy",query:{app:e.app}});window.open(t.href,"_blank")},podPage:function(e){var t=this.$router.resolve({name:"pod-index",query:{cluster:e.cluster,namespace:e.namespace,app:e.app}});window.open(t.href,"_blank")},monitorLog:function(e){var t=this,a={cluster:e.cluster,namespace:e.namespace,app:e.app};this.tableLoading=!0,Object(s["o"])(a).then((function(e){t.monitorLogVisible=!0,t.monitorLogTableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))}}},p=u,d=a("2877"),m=Object(d["a"])(p,r,n,!1,null,null,null);t["default"]=m.exports}}]);