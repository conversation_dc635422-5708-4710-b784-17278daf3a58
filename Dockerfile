FROM reg.firstshare.cn/docker.io/golang:1.21-bullseye
MAINTAINER <EMAIL>

COPY --from=reg.firstshare.cn/docker.io/bitnami/kubectl:1.13 /opt/bitnami/kubectl/bin/kubectl /bin/kubectl1.13
COPY --from=reg.firstshare.cn/docker.io/bitnami/kubectl:1.17 /opt/bitnami/kubectl/bin/kubectl /bin/kubectl1.17
COPY --from=reg.firstshare.cn/docker.io/bitnami/kubectl:1.18 /opt/bitnami/kubectl/bin/kubectl /bin/kubectl1.18
COPY --from=reg.firstshare.cn/docker.io/bitnami/kubectl:1.24 /opt/bitnami/kubectl/bin/kubectl /bin/kubectl1.24
COPY --from=reg.firstshare.cn/docker.io/bitnami/kubectl:1.25 /opt/bitnami/kubectl/bin/kubectl /bin/kubectl1.25
COPY --from=reg.firstshare.cn/docker.io/bitnami/kubectl:1.29 /opt/bitnami/kubectl/bin/kubectl /bin/kubectl1.29
COPY --from=reg.firstshare.cn/docker.io/bitnami/kubectl:1.32 /opt/bitnami/kubectl/bin/kubectl /bin/kubectl1.32
RUN ln -s /bin/kubectl1.13 /bin/kubectl && chmod +x /bin/kubectl*

COPY --from=reg.firstshare.cn/docker.io/alpine/helm:3.11.3 /usr/bin/helm /bin/helm
RUN chmod +x /bin/helm

COPY --from=reg.firstshare.cn/base/fs-docker-static-file /fs-statics/kube-capacity_v0.8.0_linux_x86_64.tar.gz /tmp/kube-capacity_v0.8.0_linux_x86_64.tar.gz
RUN cd /tmp && tar -xvf kube-capacity_v0.8.0_linux_x86_64.tar.gz && mv kube-capacity /bin/kube-capacity && chmod +x /bin/kube-capacity


RUN echo "\
deb https://mirrors.aliyun.com/debian/ bullseye main non-free contrib \n\
deb-src https://mirrors.aliyun.com/debian/ bullseye main non-free contrib \n\
deb https://mirrors.aliyun.com/debian-security/ bullseye-security main \n\
deb-src https://mirrors.aliyun.com/debian-security/ bullseye-security main \n\
deb https://mirrors.aliyun.com/debian/ bullseye-updates main non-free contrib \n\
deb-src https://mirrors.aliyun.com/debian/ bullseye-updates main non-free contrib \n\
deb https://mirrors.aliyun.com/debian/ bullseye-backports main non-free contrib \n\
deb-src https://mirrors.aliyun.com/debian/ bullseye-backports main non-free contrib \n\
" > /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y zip unzip cron vim curl less\
    && apt-get clean

ENV GOPROXY=https://goproxy.cn,direct
ENV GOSUMDB=off

COPY back-end/go.mod /opt/fs-app/back-end/go.mod
RUN cd /opt/fs-app/back-end/ && go mod download -x

COPY back-end /opt/fs-app/back-end

WORKDIR /opt/fs-app/back-end
RUN go build -v -o ${GOPATH}/bin/fs-k8s-app-manager

EXPOSE 80
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh
ENTRYPOINT ["/docker-entrypoint.sh"]
