<template>
  <div class="app-container">
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
      <el-form-item label="集群">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群" style="width: 100%;"
                   @change="clusterChange" filterable>
          <el-option
            v-for="item in clusterOptions"
            :key="item.name"
            :label="item.name + ' (' + item.description + ')'"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="环境">
        <el-select v-model="searchForm.namespace" placeholder="选择运行环境" style="width: 100%" filterable>
          <el-option
            key=""
            label="所有"
            value="">
          </el-option>
          <el-option
            v-for="item in namespaceOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用名">
        <el-select v-model="searchForm.app" filterable placeholder="请选择应用" style="width: 280px;" clearable>
          <el-option
            v-for="item in apps"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="text" icon="el-icon-circle-plus-outline" @click="showEditDialog(null)" style="margin-left: 20px;">新建</el-button>
        <export-button :table-ref="this.$refs.table001"></export-button>
      </el-form-item>
    </el-form>


    <table style="color: rgb(119, 119, 119);font-size: 12px;padding: 10px;">
      <tr>
        <th style="width: 70px;text-align: left;vertical-align: top;">扩容机制:</th>
        <td style="width: 1000px;">缩容时间范围内，每分钟检测一次，如果应用 <b style="color:#E6A23C">所有实例的CPU平均使用率</b> 大于【CPU使用率阈值】<b>并且</b> 连续检测【检测数阈值】次都满足前面的条件，则一次性扩容到【扩容副本数】
        </td>
      </tr>
      <tr>
        <th style="width: 70px;text-align: left;vertical-align: top;">缩容机制:</th>
        <td>
          <div>
            缩容时间范围内，如果应用所有实例的CPU平均使用率小于【CPU使用率阈值】，
            则缩容一个实例。一分钟后继续检测，满足条件则再缩容一个实例，直到当前【运行副本数】等于【发布流程副本数】
          </div>
        </td>
      </tr>
      <tr>
        <th style="width: 70px;text-align: left;vertical-align: top;">计算公式：</th>
        <td style="width: 1000px;">【单个实例的CPU使用率】 = 实例cpu使用量 / 实例的CPU资源请求值（发布流程配置里的最大CPU）</td>
      </tr>
      <tr>
        <th>设计图</th>
        <td><a target="_blank" href="/images/auto-scale.jpg" style="font-size: 12px;color: #01AAED">查看扩缩容机制图</a></td>
      </tr>
    </table>
    <el-alert
      v-if="clusterAutoScaleDisabled"
      title="当前集群已关闭自动扩缩容功能，请使用 【自动扩缩容V2】"
      style="padding: 10px;margin-bottom: 20px;"
      type="error"
      :closable="false"
      show-icon>
    </el-alert>
    <el-table
      ref="table001"
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用名"
                       sortable
                       prop="app">
      </el-table-column>
      <el-table-column label="运行环境"
                       prop="namespace">
      </el-table-column>
      <el-table-column label="所在集群"
                       prop="cluster">
      </el-table-column>
      <el-table-column label="CPU使用率阈值"
                       align="center"
                       prop="cpuTargetPercent">
        <template slot-scope="scope">
          {{ scope.row.cpuTargetPercent }}%
        </template>
      </el-table-column>
      <el-table-column label="检测数阈值"
                       align="center"
                       prop="scaleUpThreshold" width="100">
      </el-table-column>
      <el-table-column label="副本数" prop="replicas" align="center">
        <el-table-column label="发布流程副本→扩容副本" width="180" align="center" prop="pipeReplicas">
          <template slot-scope="scope">
            <span style="color: orangered;" v-if="scope.row.pipeReplicas == 0">{{ scope.row.pipeReplicas }}</span>
            <span v-else>{{ scope.row.pipeReplicas }}</span>
            <span style="color: #bbb;">→</span>
            <span style="color: orangered;" v-if="scope.row.replicas == 0">{{ scope.row.replicas }}</span>
            <span v-else>{{ scope.row.replicas }}</span>
          </template>
        </el-table-column>
        <el-table-column label="当前运行" width="100" align="center" prop="currReplicas">
          <template slot-scope="scope">
            <span style="color: orangered;" v-if="scope.row.currReplicas == 0">{{ scope.row.currReplicas }}</span>
            <span v-else>{{ scope.row.currReplicas }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="扩缩容时间段" width="150">
        <template slot-scope="scope">
          <div style="font-size: 12px;line-height: 16px;">
            <!-- 扩：{{ scope.row.scaleDownEndTime }} - {{ scope.row.scaleDownStartTime }}<br/> -->
            扩：全天<br/>
            缩：{{ scope.row.scaleDownStartTime }} - {{ scope.row.scaleDownEndTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="系统分析"
                       prop="analyze">
        <template slot-scope="scope">
          <div style="color: orangered;font-size: 10px;">
            {{ scope.row.analyze }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建人"
                       align="center"
                       prop="author">
      </el-table-column>
      <el-table-column label="操作"
                       width="220px">
        <template slot-scope="scope">
          <div style="line-height: 16px;">
            <el-button
              type="text"
              class="el-icon-menu"
              style="font-size: 16px;color:#67c23a;padding: 0"
              @click="monitorLog(scope.row)">日志
            </el-button>
            <el-button
              type="text"
              icon="el-icon-edit"
              style="padding: 0"
              @click="showEditDialog(scope.row)"
              slot="reference">编辑
            </el-button>
            <el-popconfirm :title="'确定要删除吗？'" @confirm="deleteCron(scope.row)">
              <el-button
                type="text"
                icon="el-icon-delete"
                style="padding: 0"
                slot="reference">删除
              </el-button>
            </el-popconfirm>
            <br/>
            <el-button
              type="text"
              class="el-icon-menu"
              style="padding-bottom: 0"
              @click="pipePage(scope.row)">发布流程
            </el-button>
            <el-button
              type="text"
              class="el-icon-menu"
              style="padding-bottom: 0"
              @click="podPage(scope.row)">实例管理
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="创建/编辑扩缩容配置" :visible.sync="dialogVisible" width="700px">
      <el-form :model="editForm" ref="dialogEditForm" label-width="120px" :rules="editFormRules">
        <el-form-item label="应用" prop="app">
          <el-select v-model="editForm.app" filterable style="width: 100%" :disabled="editFormAppEditDisable">
            <el-option
              v-for="value in appOptions"
              :key="value"
              :label="value"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运行环境">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;">
              <el-form-item prop="cluster">
                <el-input v-model="editForm.cluster" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="namespace">
                <el-input v-model="editForm.namespace" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="cpu使用率阈值" prop="cpuTargetPercent">
          <el-select v-model="editForm.cpuTargetPercent" style="width: 100%;">
            <el-option
              v-for="item in cpuPercentOptions"
              :key="item.value"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检测数阈值" prop="scaleUpThreshold">
          <el-input-number v-model="editForm.scaleUpThreshold" :min="1" :max="30"></el-input-number>
        </el-form-item>
        <el-form-item label="副本数扩容到" prop="replicas">
          <el-input-number v-model.number="editForm.replicas"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="createCron()" v-loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="监控日志" :visible.sync="monitorLogVisible" width="900px">
      <div style="height:500px;overflow-y: auto;">
        <el-table
          :data="monitorLogTableData"
          element-loading-text="Loading"
          border
          fit
          highlight-current-row
        >
          <el-table-column type="index">
          </el-table-column>
          <el-table-column label="时间"
                           width="140"
                           prop="createdTime">
          </el-table-column>
          <el-table-column label="应用">
            <template slot-scope="scope">
              {{ scope.row.cluster + "/" + scope.row.namespace + "/" + scope.row.app }}
            </template>
          </el-table-column>
          <el-table-column label="CPU使用情况"
                           prop="remark">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getAllAppName} from "@/api/app";
import {cloneObject} from "@/utils/my-util";
import {createAutoScale, deleteAutoScale, searchAutoScale, searchScaleMonitorLog} from "@/api/k8s/scale";
import ExportButton from "@/views/components/export-button.vue";

export default {
  components: {ExportButton},
  mounted() {
    if(this.$route.query.cluster) {
      this.searchForm.cluster = this.$route.query.cluster;
    }else {
      this.searchForm.cluster = this.clusterOptions[0].name;
    }
    if(this.$route.query.namespace) {
      this.searchForm.namespace = this.$route.query.namespace;
    }
    if(this.$route.query.app) {
      this.searchForm.app = this.$route.query.app;
    }
    if(this.searchForm.app) {
      this.loadTableData();
    }
    getAllAppName().then(response => {
      this.apps = response.data;
    }).catch((e) => {
      this.$message.error("加载应用数据出错！ " + e.message);
    });
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    clusterAutoScaleDisabled: function () {
      for (let clu of this.$settings.clusters) {
        if (clu.name === this.searchForm.cluster && clu.autoScale === false) {
          return true;
        }
      }
      return false;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    },
    cpuPercentOptions: function () {
      let ret = [];
      for (let i = 50; i <= 130; i = i + 5) {
        ret.push({
          name: i + "%",
          value: i
        })
      }
      return ret;
    }
  },
  data() {
    return {
      searchEnv: "",
      apps: [],
      searchForm: {
        cluster: "",
        namespace: "",
        app: ""
      },
      editForm: {},
      editFormAppEditDisable: false,
      editFormRules: {
        app: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        cluster: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        namespace: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        replicas: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        cpuTargetPercent: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        scaleUpThreshold: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ]
      },
      tableData: [],
      tableLoading: false,
      dialogVisible: false,
      submitLoading: false,
      appOptions: [],
      monitorLogVisible: false,
      monitorLogTableData: [],
    }
  },

  methods: {
    loadTableData() {
      this.tableLoading = true;
      searchAutoScale(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    editFormReset() {
      this.editForm = {
        app: "",
        cluster: this.searchForm.cluster,
        namespace: this.searchForm.namespace,
        cpuTargetPercent: 70,
        scaleUpThreshold: 2,
        replicas: 2,
      }
    },
    showEditDialog(row) {
      if (this.appOptions.length < 1) {
        getAllAppName().then(response => {
          this.appOptions = response.data
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }
      if (!row) {
        if (!this.searchForm.cluster || !this.searchForm.namespace) {
          this.$message.warning("请选选择集群和环境");
          return;
        }
        this.editFormReset();
        this.editFormAppEditDisable = false
      } else {
        this.editForm = cloneObject(row)
        this.editFormAppEditDisable = true
      }
      this.dialogVisible = true;
    },
    deleteCron(row) {
      deleteAutoScale(row.id).then(response => {
        this.$message.success("操作成功")
        this.loadTableData()
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    clusterChange() {
      this.searchForm.namespace = ""
    },
    createCron() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.submitLoading = true
        createAutoScale(this.editForm).then(response => {
          this.dialogVisible = false
          this.$message.success("操作成功")
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.submitLoading = false
        })
      });
    },
    pipePage(row) {
      let rou = this.$router.resolve({name: 'cicd-app-deploy', query: {"app": row.app}});
      window.open(rou.href, '_blank');
    },
    podPage(row) {
      let rou = this.$router.resolve({
        name: 'pod-index', query: {
          "cluster": row.cluster,
          "namespace": row.namespace,
          "app": row.app,
        }
      });
      window.open(rou.href, '_blank');
    },
    monitorLog(row) {
      let param = {
        "cluster": row.cluster,
        "namespace": row.namespace,
        "app": row.app,
      }
      this.tableLoading = true
      searchScaleMonitorLog(param).then(response => {
        this.monitorLogVisible = true;
        this.monitorLogTableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
