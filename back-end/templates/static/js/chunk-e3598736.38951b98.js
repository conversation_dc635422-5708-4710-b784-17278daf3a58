(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e3598736"],{8504:function(t,e,a){"use strict";a.d(e,"g",(function(){return o})),a.d(e,"a",(function(){return n})),a.d(e,"h",(function(){return c})),a.d(e,"c",(function(){return l})),a.d(e,"b",(function(){return u})),a.d(e,"i",(function(){return s})),a.d(e,"d",(function(){return p})),a.d(e,"f",(function(){return i})),a.d(e,"e",(function(){return d}));var r=a("b775");function o(t,e){return Object(r["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function n(t,e,a){return Object(r["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:a}})}function c(t){return Object(r["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function l(t,e,a){return Object(r["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:a}})}function u(t){return Object(r["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function s(t){return Object(r["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function p(t,e,a){return Object(r["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:a}})}function i(t){return Object(r["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,a,o,n){return Object(r["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:a,revision:o,deployTag:n||""}})}},"9ebb":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"helm-chart-build"}}),t._v(" "),a("div",[a("el-form",{ref:"searchForm",attrs:{model:t.searchForm}},[a("el-form-item",{attrs:{label:"部署到纯私有环境"}},[a("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},on:{change:t.cloudTypeChange},model:{value:t.privateCloud,callback:function(e){t.privateCloud=e},expression:"privateCloud"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"选择集群",prop:"cluster"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"选择k8s集群",disabled:t.privateCloud},on:{change:t.loadApp},model:{value:t.searchForm.cluster,callback:function(e){t.$set(t.searchForm,"cluster",e)},expression:"searchForm.cluster"}},t._l(this.clusterOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"选择环境",prop:"namespace"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"选择Namespace",filterable:"",disabled:t.privateCloud},on:{change:t.loadApp},model:{value:t.searchForm.namespace,callback:function(e){t.$set(t.searchForm,"namespace",e)},expression:"searchForm.namespace"}},t._l(this.namespaceOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"选择应用",prop:"app"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{filterable:"",placeholder:"选择应用",multiple:""},model:{value:t.searchForm.app,callback:function(e){t.$set(t.searchForm,"app",e)},expression:"searchForm.app"}},[a("el-option",{attrs:{value:"",label:"全部"}}),t._v(" "),t._l(this.appOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}))],2)],1),t._v(" "),t.privateCloud?a("el-form-item",{attrs:{label:"覆盖环境"}},[a("el-select",{staticStyle:{width:"340px"},model:{value:t.searchForm.overrideNamespace,callback:function(e){t.$set(t.searchForm,"overrideNamespace",e)},expression:"searchForm.overrideNamespace"}},[a("el-option",{attrs:{value:"cmhk-crm-di-std",label:"招商局集团测试（cmhk-crm-di-std）"}})],1)],1):t._e(),t._v(" "),a("el-form-item",[a("div",{staticStyle:{"margin-top":"20px","padding-left":"160px"}},[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text"},on:{click:t.logHistory}},[t._v("查看历史结果")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.createHelmChartBuildJob}},[t._v("开始构建")])],1)])],1)],1),t._v(" "),t.jobResult.length>0?a("div",{staticStyle:{"margin-top":"20px",border:"1px solid #ccc","background-color":"#eee",padding:"5px",width:"760px","font-size":"12px"}},t._l(t.jobResult,(function(e){return a("div",[t._v("\n      "+t._s(e)+"\n    ")])})),0):t._e()],1)},o=[],n=(a("7f7f"),a("2d63")),c=a("a68b"),l=a("8504"),u=a("c1ab"),s={components:{appManageTab:c["a"]},mounted:function(){},beforeDestroy:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var t,e=Object(n["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var a=t.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(r){e.e(r)}finally{e.f()}}return[]}},data:function(){return{searchForm:{cluster:"",namespace:"",app:"",overrideNamespace:""},appOptions:[],jobResult:[],privateCloud:!1}},methods:{cloudTypeChange:function(){console.log(this.privateCloud),this.privateCloud?(this.searchForm.cluster="forceecrm-k8s1",this.searchForm.namespace="forceecrm-public-prod",this.loadApp()):this.searchForm.overrideNamespace=""},loadApp:function(){var t=this.searchForm.cluster,e=this.searchForm.namespace;if(t&&e){var a=this;Object(l["g"])(t,e).then((function(t){a.appOptions=t.data})).catch((function(t){console.error(t)}))}else this.appOptions=[]},createHelmChartBuildJob:function(){var t=this;!this.privateCloud||this.searchForm.overrideNamespace?Object(u["m"])(this.searchForm.cluster,this.searchForm.namespace,this.searchForm.app,this.searchForm.overrideNamespace).then((function(e){t.jobResult.push("- 操作成功，构建完成后结果会存储到审计日志。构建服务比较多时，耗时会比较长。"+e.data)})).catch((function(e){t.$message.error(e.message)})).finally((function(){})):this.$message.warning("私有部署环境，必须选择覆盖环境")},logHistory:function(){var t=this.$router.resolve({name:"log-list",query:{operate:"helm-chart-build-job"}});window.open(t.href,"_blank")}}},p=s,i=a("2877"),d=Object(i["a"])(p,r,o,!1,null,null,null);e["default"]=d.exports},a68b:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"自动扩缩容-批量创建",name:"auto-scale-batch-create"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},o=[],n=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var a=t.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"auto-scale-batch-create"===a?this.$router.push({name:"tool-auto-scale-batch-create"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),c=n,l=(a("d54f"),a("2877")),u=Object(l["a"])(c,r,o,!1,null,null,null);e["a"]=u.exports},c1ab:function(t,e,a){"use strict";a.d(e,"j",(function(){return o})),a.d(e,"l",(function(){return n})),a.d(e,"z",(function(){return c})),a.d(e,"A",(function(){return l})),a.d(e,"d",(function(){return u})),a.d(e,"h",(function(){return s})),a.d(e,"D",(function(){return p})),a.d(e,"F",(function(){return i})),a.d(e,"y",(function(){return d})),a.d(e,"b",(function(){return m})),a.d(e,"g",(function(){return h})),a.d(e,"C",(function(){return f})),a.d(e,"E",(function(){return b})),a.d(e,"x",(function(){return v})),a.d(e,"G",(function(){return g})),a.d(e,"m",(function(){return y})),a.d(e,"e",(function(){return O})),a.d(e,"a",(function(){return j})),a.d(e,"B",(function(){return k})),a.d(e,"k",(function(){return _})),a.d(e,"i",(function(){return x})),a.d(e,"s",(function(){return F})),a.d(e,"v",(function(){return $})),a.d(e,"w",(function(){return C})),a.d(e,"o",(function(){return w})),a.d(e,"p",(function(){return N})),a.d(e,"t",(function(){return S})),a.d(e,"f",(function(){return T})),a.d(e,"u",(function(){return q})),a.d(e,"c",(function(){return A})),a.d(e,"q",(function(){return R})),a.d(e,"r",(function(){return H})),a.d(e,"n",(function(){return J}));var r=a("b775");function o(t){return Object(r["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function n(t){return Object(r["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(r["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function l(t){return Object(r["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function u(){return Object(r["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function s(t){return Object(r["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function p(t){return Object(r["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function i(t){return Object(r["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(r["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function h(t,e,a,o,n,c,l,u){return Object(r["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(n,"&fixVersion=").concat(e,"&suffixVersion=").concat(a,"&message=").concat(o,"&dependencyCheck=").concat(c,"&parentPom=").concat(l),method:"post",data:u})}function f(t){return Object(r["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function b(t){return Object(r["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function v(t){return Object(r["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(r["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,a,o){return Object(r["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&overrideNamespace=").concat(o),method:"post"})}function O(t,e,a,o,n){return Object(r["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(a,"&remark=").concat(o,"&dryRun=").concat(n),method:"post"})}function j(){return Object(r["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function k(){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function _(t){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function x(t){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function F(t){return Object(r["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function $(t){return Object(r["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function C(t){return Object(r["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function w(t){return Object(r["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function N(t){return Object(r["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function S(t){return Object(r["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function T(t){return Object(r["a"])({url:"/v1/tool/autoscale-batch-create",method:"post",data:t})}function q(t,e){return Object(r["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function A(t){return Object(r["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function R(t){return Object(r["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function H(t){return Object(r["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function J(t,e,a){return Object(r["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(a),method:"get"})}},d54f:function(t,e,a){"use strict";a("eba1")},eba1:function(t,e,a){}}]);