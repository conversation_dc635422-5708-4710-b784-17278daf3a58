package routers

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestSetupRouter(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)
	
	// Test that SetupRouter returns a valid router
	router := SetupRouter()
	
	if router == nil {
		t.<PERSON><PERSON>("SetupRouter should return a non-nil router")
	}
	
	// Test that the router is a Gin engine
	if _, ok := router.(*gin.Engine); !ok {
		t.<PERSON><PERSON><PERSON>("SetupRouter should return a *gin.Engine")
	}
}

func TestRouterBasicRoutes(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	// Test basic routes that should exist
	tests := []struct {
		name           string
		method         string
		path           string
		expectedStatus int
	}{
		{
			name:           "Root path",
			method:         "GET",
			path:           "/",
			expectedStatus: 200, // Should redirect or serve index
		},
		{
			name:           "Health check",
			method:         "GET",
			path:           "/health",
			expectedStatus: 200, // Common health check endpoint
		},
		{
			name:           "API prefix",
			method:         "GET",
			path:           "/api",
			expectedStatus: 404, // May not exist, but should handle gracefully
		},
		{
			name:           "Non-existent route",
			method:         "GET",
			path:           "/non-existent-route",
			expectedStatus: 404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest(tt.method, tt.path, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Log the actual status for debugging
			t.Logf("Route %s %s returned status: %d", tt.method, tt.path, w.Code)
			
			// For non-existent routes, we expect 404
			if tt.path == "/non-existent-route" && w.Code != 404 {
				t.Errorf("Expected status 404 for non-existent route, got %d", w.Code)
			}
		})
	}
}

func TestRouterMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	// Test that middleware is properly set up by making a request
	req, err := http.NewRequest("GET", "/", nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check for common middleware headers
	t.Logf("Response headers: %v", w.Header())
	
	// The router should handle the request without panicking
	if w.Code == 0 {
		t.Errorf("Router should return a valid HTTP status code")
	}
}

func TestRouterCORS(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	// Test CORS preflight request
	req, err := http.NewRequest("OPTIONS", "/", nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	
	req.Header.Set("Origin", "http://localhost:3000")
	req.Header.Set("Access-Control-Request-Method", "GET")
	
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	
	// Check if CORS headers are present
	corsHeaders := []string{
		"Access-Control-Allow-Origin",
		"Access-Control-Allow-Methods",
		"Access-Control-Allow-Headers",
	}
	
	for _, header := range corsHeaders {
		if value := w.Header().Get(header); value != "" {
			t.Logf("CORS header %s: %s", header, value)
		}
	}
	
	t.Logf("CORS preflight request returned status: %d", w.Code)
}

func TestRouterWithDifferentMethods(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	methods := []string{"GET", "POST", "PUT", "DELETE", "PATCH"}
	
	for _, method := range methods {
		t.Run("Method_"+method, func(t *testing.T) {
			req, err := http.NewRequest(method, "/", nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Log the response for each method
			t.Logf("Method %s returned status: %d", method, w.Code)
			
			// The router should handle all methods gracefully
			if w.Code == 0 {
				t.Errorf("Router should return a valid HTTP status code for method %s", method)
			}
		})
	}
}

func TestRouterStaticFiles(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	// Test static file routes
	staticPaths := []string{
		"/static/",
		"/assets/",
		"/favicon.ico",
		"/robots.txt",
	}
	
	for _, path := range staticPaths {
		t.Run("Static_"+path, func(t *testing.T) {
			req, err := http.NewRequest("GET", path, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			t.Logf("Static path %s returned status: %d", path, w.Code)
		})
	}
}

func TestRouterAPIRoutes(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	// Test common API routes
	apiPaths := []string{
		"/api/v1/apps",
		"/api/v1/users",
		"/api/v1/logs",
		"/api/v1/pods",
		"/api/v1/deployments",
	}
	
	for _, path := range apiPaths {
		t.Run("API_"+path, func(t *testing.T) {
			req, err := http.NewRequest("GET", path, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			t.Logf("API path %s returned status: %d", path, w.Code)
		})
	}
}

func TestRouterErrorHandling(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	// Test error handling with malformed requests
	tests := []struct {
		name   string
		method string
		path   string
	}{
		{
			name:   "Very long path",
			method: "GET",
			path:   "/" + string(make([]byte, 10000)),
		},
		{
			name:   "Path with special characters",
			method: "GET",
			path:   "/path/with/特殊字符/and/symbols!@#$%",
		},
		{
			name:   "Path with encoded characters",
			method: "GET",
			path:   "/path%20with%20spaces",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest(tt.method, tt.path, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Router should handle malformed requests gracefully
			t.Logf("Error handling test %s returned status: %d", tt.name, w.Code)
			
			if w.Code == 0 {
				t.Errorf("Router should return a valid HTTP status code for malformed request")
			}
		})
	}
}

func TestRouterConcurrentRequests(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	const numRequests = 10
	done := make(chan bool, numRequests)
	
	for i := 0; i < numRequests; i++ {
		go func(id int) {
			defer func() { done <- true }()
			
			req, err := http.NewRequest("GET", "/", nil)
			if err != nil {
				t.Errorf("Failed to create request %d: %v", id, err)
				return
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			if w.Code == 0 {
				t.Errorf("Request %d: Router should return a valid HTTP status code", id)
			}
		}(i)
	}
	
	// Wait for all requests to complete
	for i := 0; i < numRequests; i++ {
		<-done
	}
}

func TestRouterRecovery(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	
	// Add a route that panics for testing recovery middleware
	router.GET("/panic", func(c *gin.Context) {
		panic("test panic")
	})
	
	req, err := http.NewRequest("GET", "/panic", nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Recovery middleware should catch the panic and return 500
	if w.Code != 500 {
		t.Logf("Expected status 500 for panic recovery, got %d (recovery middleware may not be configured)", w.Code)
	}
}

// Benchmark tests
func BenchmarkRouterSetup(b *testing.B) {
	gin.SetMode(gin.TestMode)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		router := SetupRouter()
		_ = router
	}
}

func BenchmarkRouterRequest(b *testing.B) {
	gin.SetMode(gin.TestMode)
	
	router := SetupRouter()
	req, _ := http.NewRequest("GET", "/", nil)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}
