package template

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestParseTemplate(t *testing.T) {
	tests := []struct {
		name     string
		template string
		data     interface{}
		expected string
		hasError bool
	}{
		{
			name:     "Simple text without template",
			template: "Hello World",
			data:     nil,
			expected: "Hello World",
			hasError: false,
		},
		{
			name:     "Simple variable substitution",
			template: "Hello {{.Name}}",
			data:     map[string]string{"Name": "<PERSON>"},
			expected: "Hello John",
			hasError: false,
		},
		{
			name:     "Multiple variables",
			template: "{{.Greeting}} {{.Name}}, you are {{.Age}} years old",
			data: map[string]interface{}{
				"Greeting": "Hello",
				"Name":     "Alice",
				"Age":      30,
			},
			expected: "Hello Alice, you are 30 years old",
			hasError: false,
		},
		{
			name:     "Conditional template",
			template: "{{if .IsAdmin}}Admin{{else}}User{{end}}",
			data:     map[string]bool{"IsAdmin": true},
			expected: "Admin",
			hasError: false,
		},
		{
			name:     "Conditional template false",
			template: "{{if .IsAdmin}}Admin{{else}}User{{end}}",
			data:     map[string]bool{"IsAdmin": false},
			expected: "User",
			hasError: false,
		},
		{
			name:     "Range template",
			template: "{{range .Items}}{{.}} {{end}}",
			data:     map[string][]string{"Items": {"apple", "banana", "cherry"}},
			expected: "apple banana cherry ",
			hasError: false,
		},
		{
			name:     "Sprig function - upper",
			template: "{{.Name | upper}}",
			data:     map[string]string{"Name": "john"},
			expected: "JOHN",
			hasError: false,
		},
		{
			name:     "Sprig function - default",
			template: "{{.Name | default \"Anonymous\"}}",
			data:     map[string]string{},
			expected: "Anonymous",
			hasError: false,
		},
		{
			name:     "Invalid template syntax",
			template: "{{.Name",
			data:     map[string]string{"Name": "John"},
			expected: "",
			hasError: true,
		},
		{
			name:     "Missing variable",
			template: "Hello {{.MissingVar}}",
			data:     map[string]string{"Name": "John"},
			expected: "Hello <no value>",
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseTemplate(tt.template, tt.data)
			
			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			
			if result != tt.expected {
				t.Errorf("ParseTemplate() = %q, expected %q", result, tt.expected)
			}
		})
	}
}

func TestParseTemplateFile(t *testing.T) {
	// Create a temporary directory
	tempDir, err := os.MkdirTemp("", "template_test_")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tests := []struct {
		name         string
		templateFile string
		content      string
		data         interface{}
		expected     string
		hasError     bool
	}{
		{
			name:         "Simple template file",
			templateFile: "simple.tmpl",
			content:      "Hello {{.Name}}!",
			data:         map[string]string{"Name": "World"},
			expected:     "Hello World!",
			hasError:     false,
		},
		{
			name:         "Complex template file",
			templateFile: "complex.tmpl",
			content: `
App: {{.App}}
Version: {{.Version}}
{{if .Debug}}Debug Mode: ON{{end}}
Ports:
{{range .Ports}}  - {{.}}
{{end}}`,
			data: map[string]interface{}{
				"App":     "MyApp",
				"Version": "1.0.0",
				"Debug":   true,
				"Ports":   []int{8080, 9090},
			},
			expected: `
App: MyApp
Version: 1.0.0
Debug Mode: ON
Ports:
  - 8080
  - 9090
`,
			hasError: false,
		},
		{
			name:         "Template with sprig functions",
			templateFile: "sprig.tmpl",
			content:      "{{.Name | upper | quote}}",
			data:         map[string]string{"Name": "test app"},
			expected:     "\"TEST APP\"",
			hasError:     false,
		},
		{
			name:         "Non-existent file",
			templateFile: "non_existent.tmpl",
			content:      "",
			data:         nil,
			expected:     "",
			hasError:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var templatePath string
			
			if tt.name != "Non-existent file" {
				// Create template file
				templatePath = filepath.Join(tempDir, tt.templateFile)
				err := os.WriteFile(templatePath, []byte(tt.content), 0644)
				if err != nil {
					t.Fatalf("Failed to create template file: %v", err)
				}
			} else {
				templatePath = filepath.Join(tempDir, tt.templateFile)
			}
			
			result, err := ParseTemplateFile(templatePath, tt.data)
			
			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			
			if result != tt.expected {
				t.Errorf("ParseTemplateFile() = %q, expected %q", result, tt.expected)
			}
		})
	}
}

func TestRenderTemplate(t *testing.T) {
	// This is an internal function, but we can test it indirectly through ParseTemplate
	// Here we test edge cases that might not be covered by ParseTemplate tests
	
	tests := []struct {
		name     string
		template string
		data     interface{}
		hasError bool
	}{
		{
			name:     "Nil data",
			template: "Static text",
			data:     nil,
			hasError: false,
		},
		{
			name:     "Empty template",
			template: "",
			data:     map[string]string{"key": "value"},
			hasError: false,
		},
		{
			name:     "Complex nested data",
			template: "{{.User.Profile.Name}}",
			data: map[string]interface{}{
				"User": map[string]interface{}{
					"Profile": map[string]string{
						"Name": "John Doe",
					},
				},
			},
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ParseTemplate(tt.template, tt.data)
			
			if tt.hasError && err == nil {
				t.Errorf("Expected error but got none")
			}
			
			if !tt.hasError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
		})
	}
}

func TestSprigFunctions(t *testing.T) {
	// Test various sprig functions to ensure they're properly integrated
	tests := []struct {
		name     string
		template string
		data     interface{}
		expected string
	}{
		{
			name:     "String functions - upper",
			template: "{{.text | upper}}",
			data:     map[string]string{"text": "hello"},
			expected: "HELLO",
		},
		{
			name:     "String functions - lower",
			template: "{{.text | lower}}",
			data:     map[string]string{"text": "HELLO"},
			expected: "hello",
		},
		{
			name:     "String functions - title",
			template: "{{.text | title}}",
			data:     map[string]string{"text": "hello world"},
			expected: "Hello World",
		},
		{
			name:     "Default function",
			template: "{{.missing | default \"default_value\"}}",
			data:     map[string]string{},
			expected: "default_value",
		},
		{
			name:     "Quote function",
			template: "{{.text | quote}}",
			data:     map[string]string{"text": "hello world"},
			expected: "\"hello world\"",
		},
		{
			name:     "Join function",
			template: "{{.items | join \",\"}}",
			data:     map[string][]string{"items": {"a", "b", "c"}},
			expected: "a,b,c",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseTemplate(tt.template, tt.data)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			
			if result != tt.expected {
				t.Errorf("ParseTemplate() = %q, expected %q", result, tt.expected)
			}
		})
	}
}

// Benchmark tests
func BenchmarkParseTemplate(b *testing.B) {
	template := "Hello {{.Name}}, you are {{.Age}} years old and your role is {{.Role | upper}}"
	data := map[string]interface{}{
		"Name": "John",
		"Age":  30,
		"Role": "admin",
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := ParseTemplate(template, data)
		if err != nil {
			b.Fatalf("Unexpected error: %v", err)
		}
	}
}

func BenchmarkParseTemplateFile(b *testing.B) {
	// Setup
	tempDir, err := os.MkdirTemp("", "bench_template_")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	templateContent := "Hello {{.Name}}, you are {{.Age}} years old"
	templatePath := filepath.Join(tempDir, "bench.tmpl")
	err = os.WriteFile(templatePath, []byte(templateContent), 0644)
	if err != nil {
		b.Fatalf("Failed to create template file: %v", err)
	}

	data := map[string]interface{}{
		"Name": "John",
		"Age":  30,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := ParseTemplateFile(templatePath, data)
		if err != nil {
			b.Fatalf("Unexpected error: %v", err)
		}
	}
}

func TestTemplateWithComplexData(t *testing.T) {
	// Test with complex data structures similar to what might be used in k8s templates
	template := `
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{.App}}
  namespace: {{.Namespace}}
spec:
  replicas: {{.Replicas}}
  selector:
    matchLabels:
      app: {{.App}}
  template:
    metadata:
      labels:
        app: {{.App}}
    spec:
      containers:
      - name: {{.App}}
        image: {{.Image}}
        ports:
        {{range .Ports}}
        - containerPort: {{.Port}}
          name: {{.Name}}
        {{end}}
        env:
        {{range .Envs}}
        - name: {{.Name}}
          value: {{.Value | quote}}
        {{end}}
`

	data := map[string]interface{}{
		"App":       "test-app",
		"Namespace": "default",
		"Replicas":  3,
		"Image":     "nginx:latest",
		"Ports": []map[string]interface{}{
			{"Port": 8080, "Name": "http"},
			{"Port": 9090, "Name": "metrics"},
		},
		"Envs": []map[string]string{
			{"Name": "ENV", "Value": "production"},
			{"Name": "DEBUG", "Value": "false"},
		},
	}

	result, err := ParseTemplate(template, data)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
		return
	}

	// Check that the result contains expected values
	expectedStrings := []string{
		"name: test-app",
		"namespace: default",
		"replicas: 3",
		"image: nginx:latest",
		"containerPort: 8080",
		"containerPort: 9090",
		"value: \"production\"",
		"value: \"false\"",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't. Result:\n%s", expected, result)
		}
	}
}
