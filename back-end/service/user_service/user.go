package user_service

import (
	"fs-k8s-app-manager/models"
)

func All() (ret []models.User, err error) {
	err = models.DB().Find(&ret).Error
	return
}
func FindByName(name string) (ret models.User, err error) {
	err = models.DB().Where("username = ?", name).Take(&ret).Error
	return
}
func FindByRealName(realName string) (ret models.User, err error) {
	err = models.DB().Where("real_name = ?", realName).Take(&ret).Error
	return
}

func ListByRealName(realName string) (ret []models.User, err error) {
	err = models.DB().Where("real_name like ?", "%"+realName+"%").Find(&ret).Error
	return
}
func PreLikeByRealName(realName string) (ret models.User, err error) {
	err = models.DB().Where("real_name like ?", realName+"%").Take(&ret).Error
	return
}

func UpdateRecentApp(username string, apps []string) error {
	return models.DB().Model(&models.User{}).
		Where("username", username).
		Select("RecentApps").Updates(models.User{RecentApps: apps}).Error
}

func UpdateRecentPod(username string, service []string) error {
	return models.DB().Model(&models.User{}).
		Where("username", username).
		Select("RecentPods").Updates(models.User{RecentPods: service}).Error
}

func Create(entity *models.User) (err error) {
	return models.DB().Create(entity).Error
}

func Save(entity *models.User) (err error) {
	return models.DB().Save(entity).Error
}
