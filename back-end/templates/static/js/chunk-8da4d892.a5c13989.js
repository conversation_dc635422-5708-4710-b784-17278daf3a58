(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8da4d892"],{"2ffc":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[this.editMode?n("div",[n("div",{staticStyle:{"margin-bottom":"15px"}},[n("el-button",{attrs:{size:"small"},on:{click:t.editToggle}},[t._v("取消")]),t._v(" "),n("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.editSubmit}},[t._v("提交")])],1),t._v(" "),n("el-input",{attrs:{type:"textarea",autosize:""},model:{value:t.settingsJson,callback:function(e){t.settings<PERSON>son=e},expression:"settingsJson"}})],1):n("div",[n("div",{staticStyle:{"margin-bottom":"15px"}},[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:t.editToggle}},[t._v("编辑")]),t._v(" "),n("el-button",{staticStyle:{padding:"0",float:"right"},attrs:{type:"text"},on:{click:t.clear_cache}},[t._v("清除系统缓存\n      ")])],1),t._v(" "),this.editMode?t._e():n("vue-json-pretty",{attrs:{data:this.$settings}})],1)])},s=[],a=n("d538"),o=n.n(a),c=n("6797"),r={data:function(){return{loading:!1,editMode:!1,settingsJson:""}},mounted:function(){},methods:{clear_cache:function(){var t=this;this.loading=!0,Object(c["a"])().then((function(e){t.$message.success("操作成功")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))},editToggle:function(){this.$message({message:"修改方式： 发布系统里修改ConfigMap文件 -> 提交Git -> 通过GitLab CI 部署ConfigMap文件 -> [发布系统会自动发现文件变更并重新加载配置]",duration:"8000",type:"warning"})},editSubmit:function(){var t=this;this.loading=!0,Object(c["c"])(this.settingsJson).then((function(e){t.$message.success("修改成功，即将刷新页面内容"),setTimeout((function(){self.location.reload()}),1e3)})).catch((function(e){t.$message.error("操作失败，原因："+e.message)})).finally((function(){t.loading=!1}))}},components:{VueJsonPretty:o.a}},l=r,u=n("2877"),d=Object(u["a"])(l,i,s,!1,null,null,null);e["default"]=d.exports},6797:function(t,e,n){"use strict";n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return o}));var i=n("b775");function s(t){return Object(i["a"])({url:"/v1/sys/log",method:"get",params:{tailLines:t}})}function a(){return Object(i["a"])({url:"/v1/sys/setting/cache",method:"delete"})}function o(t){return Object(i["a"])({url:"/v1/sys/setting/edit",method:"post",data:t})}}}]);