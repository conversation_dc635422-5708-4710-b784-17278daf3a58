apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{DEPLOY_APP_NAME}}
  namespace: {{DEPLOY_K8S_NAMESPACE}}
  labels:
    app: {{DEPLOY_APP_NAME}}
  annotations:
    fxiaoke.com/managed-by: gitlab-ci
    fxiaoke.com/language: {{DEPLOY_APP_LANGUAGE}}
spec:
  strategy:
    rollingUpdate:
      maxSurge: 100%
    type: RollingUpdate
  replicas: {{DEPLOY_APP_REPLICAS}}
  selector:
    matchLabels:
      app: {{DEPLOY_APP_NAME}}
      version: v0
  template:
    metadata:
      labels:
        app: {{DEPLOY_APP_NAME}}
        version: v0
      annotations:
        fxiaoke.com/managed-by: gitlab-ci
        fxiaoke.com/language: {{DEPLOY_APP_LANGUAGE}}
        fxiaoke.com/commit-sha: "{{CI_COMMIT_SHORT_SHA}}"
    spec:
      containers:
        - name: {{DEPLOY_APP_NAME}}
          image: {{DEPLOY_APP_DOCKER_IMAGE}}
          imagePullPolicy: Always
          volumeMounts:
            - name: app-conf
              mountPath: /opt/fs-app/back-end/conf
          ports:
            - containerPort: 80
          resources:
            requests:
              cpu: {{DEPLOY_APP_REQUESTS_CPU}}
              memory: {{DEPLOY_APP_REQUESTS_MEMORY}}
            limits:
              cpu: {{DEPLOY_APP_LIMITS_CPU}}
              memory: {{DEPLOY_APP_LIMITS_MEMORY}}
          env:
            - name: TZ
              value: Asia/Shanghai
        - name: configmap-reload
          image: "{{DEPLOY_K8S_CONFIGMAP_RELOAD_IMAGE}}"
          args:
            - -volume-dir=/etc/config
            - -webhook-url=http://localhost/api/configmap/reload
            - -webhook-retries=3
          volumeMounts:
            - name: app-conf
              mountPath: /etc/config
          resources:
            limits:
              cpu: 20m
              memory: 64Mi
      volumes:
        - name: app-conf
          configMap:
            name: {{DEPLOY_APP_NAME}}-config
            items:
              - key: k8s1
                path: kubeconf/k8s1
              - key: k8s2-arm
                path: kubeconf/k8s2-arm
              - key: config.json
                path: config.json
              - key: settings.json
                path: settings.json
